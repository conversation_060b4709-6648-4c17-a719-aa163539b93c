# Bootstrap启动类异常处理优化说明

## 问题描述

原有的Bootstrap启动类缺乏异常处理机制，存在以下问题：

### 原问题位置
- **文件**: `contrast-comparison-starter/src/main/java/com/ideal/envc/Bootstrap.java`
- **行号**: `24-27`
- **问题**:
  1. 启动失败时无法提供有效的错误信息
  2. 缺乏优雅的失败处理机制
  3. 启动成功日志记录时机不当
  4. 没有启动前的环境检查

## 解决方案

### Bootstrap启动类优化

**主要改进**:
- 添加完善的try-catch异常处理机制
- 增加启动前环境检查
- 优化启动成功和失败的日志记录
- 添加优雅关闭钩子
- 提供详细的故障排除建议

**新增功能**:
```java
// 1. 启动前环境检查
performPreStartupChecks();

// 2. 异常处理和详细日志
try {
    context = SpringApplication.run(Bootstrap.class, args);
    printStartupSuccessInfo(context, duration);
} catch (Exception e) {
    handleStartupException(e, startTime);
    System.exit(FAILURE_EXIT_CODE);
}

// 3. 优雅关闭钩子
registerShutdownHook(context);
```

## 技术实现细节

### 1. 异常处理机制

```java
try {
    // 启动应用
    context = SpringApplication.run(Bootstrap.class, args);
} catch (Exception e) {
    // 根据异常信息提供具体建议
    handleStartupException(e, startTime);
    // 优雅退出
    System.exit(FAILURE_EXIT_CODE);
}
```

### 2. 环境检查

- **Java版本检查**: 记录当前JDK版本信息
- **内存检查**: 检查可用内存是否充足，低于512MB时给出警告

### 3. 故障排除建议

根据异常信息自动提供相应的解决建议：
- 包含"port"或"bind" → 检查端口占用
- 包含"database"或"connection" → 检查数据库连接
- 包含"class"或"ClassNotFoundException" → 检查依赖包完整性
- 包含"configuration"或"property" → 检查配置文件格式

### 4. 优雅关闭

```java
Runtime.getRuntime().addShutdownHook(new Thread(() -> {
    logger.info("接收到关闭信号，正在优雅关闭应用...");
    if (context != null && context.isActive()) {
        context.close();
    }
}));
```

## 优化效果

### 1. 更好的错误诊断
- 详细的错误信息和堆栈跟踪
- 根据异常信息提供针对性建议
- 记录启动失败的具体原因和耗时

### 2. 清晰的启动日志
- 启动前进行环境检查
- 启动成功后提供关键信息（耗时、端口等）
- 统一的日志格式便于问题排查

### 3. 优雅的启动和关闭
- 启动前进行基本环境检查
- 启动失败时自动清理资源
- 支持优雅关闭机制

## 使用方式

### 1. 基本使用
启动类会自动进行异常处理，无需额外配置。

### 2. 日志查看
启动过程中的详细信息会记录在日志中，包括：
- 启动前环境检查结果
- 启动成功的关键信息（耗时、端口）
- 启动失败的错误诊断和建议

## 向后兼容性

- 保持原有的启动方式不变
- 新增的异常处理不影响正常启动流程
- 不影响现有的业务逻辑
- 完全向后兼容

## 注意事项

1. **JDK兼容性**: 所有代码都使用JDK 1.8兼容的语法
2. **性能影响**: 新增的异常处理对启动性能影响极小
3. **日志级别**: 建议将启动相关的日志级别设置为INFO
4. **异常处理**: 启动失败时会自动退出，退出码为1
5. **资源清理**: 启动失败时会自动清理已创建的资源

## 示例日志输出

### 正常启动
```
========================================
    正在启动 Contrast-comparison Server
========================================
执行启动前环境检查...
Java版本: 1.8.0_291
启动前环境检查完成
正在启动 Contrast-comparison Server...
========================================
  Contrast-comparison Server 启动成功!
========================================
启动耗时: 3245ms
服务端口: 8080
访问地址: http://localhost:8080
应用已准备就绪，可以接收请求
```

### 启动失败
```
========================================
    正在启动 Contrast-comparison Server
========================================
执行启动前环境检查...
Java版本: 1.8.0_291
启动前环境检查完成
正在启动 Contrast-comparison Server...
========================================
  Contrast-comparison Server 启动失败!
========================================
启动失败耗时: 1234ms
失败原因: Port 8080 is already in use
建议: 检查端口是否被占用，或修改配置文件中的server.port设置
详细错误信息: [异常堆栈信息]
应用启动失败，程序即将退出
```
