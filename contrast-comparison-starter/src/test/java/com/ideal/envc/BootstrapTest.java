package com.ideal.envc;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Bootstrap启动类的单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Bootstrap单元测试")
class BootstrapTest {

    @Test
    @DisplayName("测试Bootstrap类存在")
    void testBootstrapClassExists() {
        // 验证Bootstrap类存在
        Class<?> bootstrapClass = Bootstrap.class;
        assertNotNull(bootstrapClass);
        assertEquals("Bootstrap", bootstrapClass.getSimpleName());
        assertEquals("com.ideal.envc.Bootstrap", bootstrapClass.getName());
    }

    @Test
    @DisplayName("测试main方法存在且签名正确")
    void testMainMethodExists() throws NoSuchMethodException {
        // 验证main方法存在
        Method mainMethod = Bootstrap.class.getMethod("main", String[].class);
        assertNotNull(mainMethod);

        // 验证方法修饰符
        assertTrue(Modifier.isStatic(mainMethod.getModifiers()), "main方法应该是静态的");
        assertTrue(Modifier.isPublic(mainMethod.getModifiers()), "main方法应该是公共的");

        // 验证返回类型
        assertEquals(void.class, mainMethod.getReturnType(), "main方法应该返回void");

        // 验证参数类型
        Class<?>[] parameterTypes = mainMethod.getParameterTypes();
        assertEquals(1, parameterTypes.length, "main方法应该有一个参数");
        assertEquals(String[].class, parameterTypes[0], "main方法参数应该是String[]");
    }

    @Test
    @DisplayName("测试Bootstrap类是公共类")
    void testBootstrapClassIsPublic() {
        Class<?> bootstrapClass = Bootstrap.class;
        assertTrue(Modifier.isPublic(bootstrapClass.getModifiers()), "Bootstrap类应该是公共的");
    }

    @Test
    @DisplayName("测试Bootstrap类不是抽象类")
    void testBootstrapClassIsNotAbstract() {
        Class<?> bootstrapClass = Bootstrap.class;
        assertFalse(Modifier.isAbstract(bootstrapClass.getModifiers()), "Bootstrap类不应该是抽象的");
    }

    @Test
    @DisplayName("测试Bootstrap类不是接口")
    void testBootstrapClassIsNotInterface() {
        Class<?> bootstrapClass = Bootstrap.class;
        assertFalse(bootstrapClass.isInterface(), "Bootstrap类不应该是接口");
    }

    @Test
    @DisplayName("测试Bootstrap类包名正确")
    void testBootstrapPackage() {
        Class<?> bootstrapClass = Bootstrap.class;
        Package pkg = bootstrapClass.getPackage();
        assertNotNull(pkg, "Bootstrap类应该有包");
        assertEquals("com.ideal.envc", pkg.getName(), "Bootstrap类应该在com.ideal.envc包中");
    }

    @Test
    @DisplayName("测试Bootstrap类可以被加载")
    void testBootstrapClassCanBeLoaded() {
        assertDoesNotThrow(() -> {
            Class.forName("com.ideal.envc.Bootstrap");
        }, "Bootstrap类应该可以被正常加载");
    }
}
