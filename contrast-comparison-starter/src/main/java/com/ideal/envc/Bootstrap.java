package com.ideal.envc;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.ConfigurableApplicationContext;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * Contrast-comparison应用启动类
 * 提供完善的异常处理机制
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.ideal.envc"},exclude = {QuartzAutoConfiguration.class})
@EnableDubbo
@ConfigurationPropertiesScan({"com.ideal.envc.config"})
@MapperScan(basePackages = { "com.ideal.envc.**.mapper","com.ideal.monitor.**.mapper"})
public class Bootstrap {
    private static final Logger logger = LoggerFactory.getLogger(Bootstrap.class);

    /**
     * 应用启动退出码
     */
    private static final int FAILURE_EXIT_CODE = 1;

    public static void main(String[] args) {
        // 记录启动开始时间
        long startTime = System.currentTimeMillis();

        logger.info("========================================");
        logger.info("    正在启动 Contrast-comparison Server");
        logger.info("========================================");

        ConfigurableApplicationContext context = null;
        try {
            // 执行启动前检查
            performPreStartupChecks();

            logger.info("正在启动 Contrast-comparison Server...");

            // 启动Spring Boot应用
            context = SpringApplication.run(Bootstrap.class, args);

            // 计算启动耗时
            long duration = System.currentTimeMillis() - startTime;

            // 打印启动成功信息
            printStartupSuccessInfo(context, duration);

            // 注册优雅关闭钩子
            registerShutdownHook(context);

        } catch (Exception e) {
            // 处理启动异常
            handleStartupException(e, startTime);

            // 清理资源
            if (context != null) {
                try {
                    context.close();
                } catch (Exception closeException) {
                    logger.error("关闭应用上下文时发生异常", closeException);
                }
            }

            // 异常退出
            System.exit(FAILURE_EXIT_CODE);
        }
    }



    /**
     * 执行启动前检查
     */
    private static void performPreStartupChecks() {
        logger.info("执行启动前环境检查...");

        // 检查Java版本
        String javaVersion = System.getProperty("java.version");
        logger.info("Java版本: {}", javaVersion);

        // 检查内存
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        if (maxMemory < 512 * 1024 * 1024) { // 小于512MB
            logger.warn("可用内存较少: {}MB，建议增加JVM堆内存设置", maxMemory / 1024 / 1024);
        }

        logger.info("启动前环境检查完成");
    }

    /**
     * 打印启动成功信息
     */
    private static void printStartupSuccessInfo(ConfigurableApplicationContext context, long duration) {
        logger.info("========================================");
        logger.info("  Contrast-comparison Server 启动成功!");
        logger.info("========================================");
        logger.info("启动耗时: {}ms", duration);

        // 获取服务器端口信息
        try {
            String port = context.getEnvironment().getProperty("server.port", "8080");
            logger.info("服务端口: {}", port);
        } catch (Exception e) {
            logger.warn("无法获取服务器端口信息: {}", e.getMessage());
        }

        logger.info("应用已准备就绪，可以接收请求");
    }

    /**
     * 处理启动异常
     */
    private static void handleStartupException(Exception e, long startTime) {
        long duration = System.currentTimeMillis() - startTime;

        logger.error("========================================");
        logger.error("  Contrast-comparison Server 启动失败!");
        logger.error("========================================");
        logger.error("启动失败耗时: {}ms", duration);
        logger.error("失败原因: {}", e.getMessage());

        // 根据异常类型提供具体的解决建议
        String exceptionMessage = e.getMessage();
        if (exceptionMessage != null) {
            if (exceptionMessage.contains("port") || exceptionMessage.contains("bind")) {
                logger.error("建议: 检查端口是否被占用，或修改配置文件中的server.port设置");
            } else if (exceptionMessage.contains("database") || exceptionMessage.contains("connection")) {
                logger.error("建议: 检查数据库连接配置和数据库服务状态");
            } else if (exceptionMessage.contains("class") || exceptionMessage.contains("ClassNotFoundException")) {
                logger.error("建议: 检查依赖包是否完整，执行 mvn clean install 重新构建");
            } else if (exceptionMessage.contains("configuration") || exceptionMessage.contains("property")) {
                logger.error("建议: 检查配置文件格式和必要的配置项是否正确");
            }
        }

        // 打印详细的异常堆栈
        logger.error("详细错误信息:", e);

        logger.error("应用启动失败，程序即将退出");
    }

    /**
     * 注册优雅关闭钩子
     */
    private static void registerShutdownHook(final ConfigurableApplicationContext context) {
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                logger.info("接收到关闭信号，正在优雅关闭应用...");
                try {
                    if (context != null && context.isActive()) {
                        context.close();
                        logger.info("应用已优雅关闭");
                    }
                } catch (Exception e) {
                    logger.error("关闭应用时发生异常", e);
                }
            }
        }, "shutdown-hook"));
    }
}