databaseChangeLog:
  - changeSet:
      id: dropFlowIdAndActId
      author: entegor
      changes:
        - dropUniqueConstraint:
            constraintName: flowIdAndActId
            tableName: ieai_monitor_flow_active_node
  - changeSet:
      id: fidAndAidandCid
      author: entegor
      changes:
        - addUniqueConstraint:
            columnNames: iflow_id, iact_id, icall_flow_id
            constraintName: fidAndAidandCid
            tableName: ieai_monitor_flow_active_node
  - changeSet:
      id: ieai_monitor_act_output
      author: engine
      changes:
        - createTable:
            columns:
              - column:
                  constraints:
                    nullable: false
                    primaryKey: true
                  name: iid
                  remarks: 主键
                  type: BIGINT
              - column:
                  name: ireq_id
                  remarks: 请求ID
                  type: VARCHAR(255)
              - column:
                  name: itype
                  remarks: 0 正常 1 异常
                  type: INT
              - column:
                  name: ioutput
                  remarks: 输出内容(json)
                  type: LONGTEXT
            tableName: ieai_monitor_act_output