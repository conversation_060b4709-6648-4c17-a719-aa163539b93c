databaseChangeLog:
    - changeSet:
          id: ieai_monitor_flow
          author: engine
          changes:
              - createTable:
                    columns:
                        - column:
                              constraints:
                                  nullable: false
                                  primaryKey: true
                              name: iid
                              remarks: 主键
                              type: BIGINT
                        - column:
                              name: iproject_uuid
                              remarks: 工程id
                              type: VARCHAR(100)
                        - column:
                              name: iproject_name
                              remarks: 工程名称
                              type: VARCHAR(100)
                        - column:
                              name: iporject_type
                              remarks: 工程类型
                              type: INT
                        - column:
                              constraints:
                                  unique: true
                              name: iflow_id
                              remarks: 工作流id或（ieai_run_instance表id）
                              type: BIGINT
                        - column:
                              name: iflow_name
                              remarks: 工作流名称
                              type: VARCHAR(200)
                        - column:
                              name: iflow_ins_name
                              remarks: 工作流实例名
                              type: VARCHAR(200)
                        - column:
                              name: ibiz_unique_id
                              remarks: 业务唯一标识
                              type: VARCHAR(50)
                        - column:
                              name: irun_server_ip
                              remarks: 运行服务器
                              type: VARCHAR(50)
                        - column:
                              name: irun_server_port
                              remarks: 运行服务器端口
                              type: INT
                        - column:
                              constraints:
                                  nullable: false
                              defaultValueComputed: CURRENT_TIMESTAMP
                              name: istart_time
                              remarks: 消息开始时间
                              type: timestamp
                        - column:
                              constraints:
                                  nullable: false
                              defaultValueComputed: CURRENT_TIMESTAMP
                              name: iend_time
                              remarks: 消息结束时间
                              type: timestamp
                        - column:
                              name: iflow_status
                              remarks: 状态
                              type: SMALLINT
                        - column:
                              name: iupdate_order_time
                              remarks: 更新状态时间
                              type: BIGINT
                        - column:
                              name: iflow_start_time
                              remarks: 流程启动时间
                              type: BIGINT
                        - column:
                              name: iflow_end_time
                              remarks: 流程结束时间
                              type: BIGINT
                        - column:
                              name: IF_FAIL
                              remarks: 是否失败
                              type: INT
                    tableName: ieai_monitor_flow
    - changeSet:
          id: ieai_monitor_flow_active_node
          author: engine
          changes:
              - createTable:
                    columns:
                        - column:
                              constraints:
                                  nullable: false
                                  primaryKey: true
                              name: iid
                              remarks: 主键
                              type: BIGINT
                        - column:
                              name: iflow_id
                              remarks: 工作流表主键
                              type: BIGINT
                        - column:
                              name: iact_id
                              remarks: 活动id
                              type: BIGINT
                        - column:
                              name: iact_name
                              remarks: 活动名称
                              type: VARCHAR(100)
                        - column:
                              name: iact_status
                              remarks: 活动状态
                              type: VARCHAR(100)
                        - column:
                              name: ireq_id
                              remarks: 远程执行唯一标识
                              type: VARCHAR(50)
                        - column:
                              name: iact_type
                              remarks: 活动类型
                              type: VARCHAR(100)
                        - column:
                              name: iagent_ip
                              type: VARCHAR(50)
                        - column:
                              name: iagent_port
                              type: INT
                        - column:
                              name: ishell_name
                              type: VARCHAR(255)
                        - column:
                              name: ishell_path
                              type: VARCHAR(500)
                        - column:
                              name: ierror_task_id
                              type: BIGINT
                        - column:
                              constraints:
                                  nullable: false
                              defaultValueComputed: CURRENT_TIMESTAMP
                              name: istart_time
                              remarks: 消息开始时间
                              type: timestamp
                        - column:
                              constraints:
                                  nullable: false
                              defaultValueComputed: CURRENT_TIMESTAMP
                              name: iend_time
                              remarks: 消息结束时间
                              type: timestamp
                        - column:
                              name: iupdate_order_time
                              remarks: 更新状态时间
                              type: BIGINT
                        - column:
                              name: icall_flow_id
                              type: BIGINT
                        - column:
                              name: ilast_line
                              type: VARCHAR(300)
                        - column:
                              name: iact_def_name
                              type: VARCHAR(50)
                    tableName: ieai_monitor_flow_active_node
    - changeSet:
          id: flowIdAndActId
          author: entegor
          changes:
              - addUniqueConstraint:
                    columnNames: iflow_id, iact_id
                    constraintName: flowIdAndActId
                    tableName: ieai_monitor_flow_active_node