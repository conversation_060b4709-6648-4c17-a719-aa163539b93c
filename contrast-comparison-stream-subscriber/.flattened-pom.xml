<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.ideal</groupId>
    <artifactId>ieai-envcontrast-comparison</artifactId>
    <version>1.0-SNAPSHOT</version>
  </parent>
  <groupId>com.ideal</groupId>
  <artifactId>contrast-comparison-stream-subscriber</artifactId>
  <version>1.0-SNAPSHOT</version>
  <name>contrast-comparison-stream-subscriber</name>
  <dependencies>
    <dependency>
      <groupId>com.ideal</groupId>
      <artifactId>ideal-message-spring-cloud-stream-publisher</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
