package com.ideal.envc;

import com.ideal.message.center.ISubscriber;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * ContrastMqRegisterConfig的单元测试类
 * <AUTHOR>
 */
public class ContrastMqRegisterConfigTest {

    @Mock
    private ISubscriber mockSubscriber;

    private ContrastMqRegisterConfig config;
    private Map<String, ISubscriber> subscribers;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        subscribers = new HashMap<>();
        subscribers.put("contrastReceiveTaskSendResultHandler", mockSubscriber);
        config = new ContrastMqRegisterConfig(subscribers);
    }

    @Test
    public void testContrastReceiveTaskSendResult() {
        // 获取消费者函数
        Consumer<String> consumer = config.contrastReceiveTaskSendResult();
        assertNotNull(consumer, "消费者函数不应为空");

        // 测试消息处理
        String testMessage = "test message";
        consumer.accept(testMessage);

        // 验证消息是否被正确处理
        verify(mockSubscriber, times(1)).notice(testMessage);
    }

    @Test
    public void testConstructor() {
        // 测试构造函数
        assertNotNull(config, "配置对象不应为空");
        
        // 测试空Map构造
        Map<String, ISubscriber> emptyMap = new HashMap<>();
        ContrastMqRegisterConfig emptyConfig = new ContrastMqRegisterConfig(emptyMap);
        assertNotNull(emptyConfig, "使用空Map构造的配置对象不应为空");
    }

    @Test
    public void testMessageHandling() {
        Consumer<String> consumer = config.contrastReceiveTaskSendResult();
        
        // 测试null消息处理
        assertDoesNotThrow(() -> consumer.accept(null), "处理null消息不应抛出异常");
        verify(mockSubscriber, times(1)).notice(null);
        
        // 测试空消息处理
        assertDoesNotThrow(() -> consumer.accept(""), "处理空消息不应抛出异常");
        verify(mockSubscriber, times(1)).notice("");
        
        // 测试长消息处理
        StringBuilder longMessage = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longMessage.append('a');
        }
        assertDoesNotThrow(() -> consumer.accept(longMessage.toString()), "处理长消息不应抛出异常");
        verify(mockSubscriber, times(1)).notice(longMessage.toString());
    }

    @Test
    public void testSubscriberNotFound() {
        // 创建一个没有注册处理器的配置
        Map<String, ISubscriber> emptySubscribers = new HashMap<>();
        ContrastMqRegisterConfig emptyConfig = new ContrastMqRegisterConfig(emptySubscribers);
        
        // 获取消费者函数
        Consumer<String> consumer = emptyConfig.contrastReceiveTaskSendResult();
        
        // 测试消息处理（应该不会抛出异常，但也不会处理消息）
        assertDoesNotThrow(() -> consumer.accept("test"), "未找到订阅者时不应抛出异常");
    }
} 