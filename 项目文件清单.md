# 环境对比分析工具项目文件清单

## 📁 项目目录结构

```
ieai-envcontrast-comparison/
├── 核心工具程序
│   ├── FileListComparator.java          # 文件列表对比工具（最新，推荐）
│   ├── FileListComparator.class         # 编译后的类文件
│   ├── EnvDiffAnalyzer.java             # 环境差异分析工具
│   ├── EnvDiffAnalyzer.class            # 编译后的类文件
│   ├── HtmlComparisonParser.java        # HTML解析器
│   ├── HtmlComparisonParser.class       # 编译后的类文件
│   ├── SimpleEnvAnalyzer.java           # 简化版工具
│   └── SimpleEnvAnalyzer.class          # 编译后的类文件
│
├── 启动脚本
│   ├── run_file_comparator.bat          # 文件列表对比工具启动脚本
│   ├── run_diff_analyzer.bat            # 环境差异分析工具启动脚本
│   ├── run_html_parser.bat              # HTML解析器启动脚本
│   ├── run.bat                          # 简化版工具启动脚本
│   ├── run.sh                           # Linux启动脚本
│   └── demo.bat                         # 演示脚本
│
├── 测试数据
│   ├── left_env.txt                     # 左侧环境测试数据
│   ├── right_env.txt                    # 右侧环境测试数据
│   ├── test_data.txt                    # JSON格式测试数据
│   └── new 52.txt                       # HTML格式对比文件
│
├── 文档资料
│   ├── 文件列表对比工具说明.md          # 新工具详细说明
│   ├── 环境差异分析工具说明.md          # 环境分析工具说明
│   ├── 快速使用指南.md                  # 快速使用指南
│   ├── 使用说明.md                      # 基本使用说明
│   ├── 项目总结.md                      # 项目总结
│   ├── 项目文件清单.md                  # 本文件
│   └── README.md                        # 项目说明
│
└── 配置文件
    └── pom.xml                          # Maven配置文件（可选）
```

## 🛠️ 工具功能对比

| 工具名称 | 主要功能 | 输入格式 | 适用场景 | 推荐度 |
|---------|---------|---------|---------|--------|
| **FileListComparator** | 文件列表字符串对比 | 两个大字符串 | 服务器文件列表对比 | ⭐⭐⭐⭐⭐ |
| **EnvDiffAnalyzer** | 环境差异分析 | 文件加载 | 环境文件对比 | ⭐⭐⭐⭐ |
| **HtmlComparisonParser** | HTML解析 | HTML文件 | HTML格式报告解析 | ⭐⭐⭐ |
| **SimpleEnvAnalyzer** | 简化版对比 | 文件加载 | 快速学习使用 | ⭐⭐ |

## 🚀 快速启动指南

### 方法1：使用启动脚本
```bash
# 启动文件列表对比工具（推荐）
run_file_comparator.bat

# 启动环境差异分析工具
run_diff_analyzer.bat

# 启动HTML解析器
run_html_parser.bat

# 启动简化版工具
run.bat
```

### 方法2：直接运行Java
```bash
# 文件列表对比工具（推荐）
java -Dfile.encoding=UTF-8 FileListComparator

# 环境差异分析工具
java -Dfile.encoding=UTF-8 EnvDiffAnalyzer

# HTML解析器
java -Dfile.encoding=UTF-8 HtmlComparisonParser

# 简化版工具
java -Dfile.encoding=UTF-8 SimpleEnvAnalyzer
```

### 方法3：使用演示脚本
```bash
demo.bat
```

## 📋 FileListComparator 使用示例

### 输入格式示例

**基线服务器文件列表：**
```
/opt/aa/bb.txt MD5:232131231
/opt/aa/cc.txt MD5:2322322131231
/opt/aa1/cc.txt MD5:2322322131231
/opt/aa/aa1/cc.txt MD5:23223221312dsds31
```

**目标服务器文件列表：**
```
/opt/aa/bb.txt MD5:232131231
/opt/aa/dd.txt MD5:23223221sds31231
/opt/aa1/cc.txt MD5:232sss2322131231
/opt/aa/aa1/cc.txt MD5:23223221312dsds31
/opt/bb/123.x
```

### 分析结果示例
- ✅ **一致文件**: `/opt/aa/bb.txt`, `/opt/aa/aa1/cc.txt`
- ❌ **缺失文件**: `/opt/aa/cc.txt` (目标服务器缺失)
- ➕ **多出文件**: `/opt/aa/dd.txt`, `/opt/bb/123.x` (目标服务器多出)
- ⚠️ **不一致文件**: `/opt/aa1/cc.txt` (MD5不同)

## 🎨 界面特色

### 颜色标识
- 🔴 **浅红色**: 缺失文件
- 🟢 **浅绿色**: 多出文件
- 🟡 **浅黄色**: 不一致文件
- 🔵 **浅蓝色**: 一致文件

### 功能按钮
- **开始对比分析**: 执行文件列表对比
- **清空内容**: 清空所有输入和结果
- **导出报告**: 保存分析结果为文本文件
- **加载示例数据**: 快速加载演示数据

## 📊 统计功能

### 基本统计
- 基线服务器文件总数
- 目标服务器文件总数
- 各类差异文件数量

### 差异率分析
- 缺失率 = 缺失文件数 / 基线文件总数 × 100%
- 不一致率 = 不一致文件数 / 基线文件总数 × 100%
- 一致率 = 一致文件数 / 基线文件总数 × 100%
- 目标多出率 = 多出文件数 / 目标文件总数 × 100%

### 智能建议
- 差异率 > 50%: 🚨 建议全面检查环境配置
- 差异率 20-50%: ⚠️ 建议重点关注关键文件
- 差异率 < 20%: ✅ 环境基本一致

## 🔧 技术要求

### 系统要求
- Java 8 或更高版本
- Windows/Linux/macOS 操作系统
- 支持UTF-8编码

### 编译要求
```bash
javac -encoding UTF-8 *.java
```

### 运行要求
```bash
java -Dfile.encoding=UTF-8 ClassName
```

## 📝 使用建议

### 最佳实践
1. **数据准备**: 确保文件列表格式正确
2. **编码统一**: 使用UTF-8编码
3. **路径规范**: 使用标准的Unix路径格式
4. **MD5完整**: 尽量提供完整的MD5值
5. **定期对比**: 建立定期对比机制

### 常见问题
1. **中文乱码**: 确保使用 `-Dfile.encoding=UTF-8` 参数
2. **路径错误**: 检查文件路径格式是否正确
3. **MD5缺失**: 没有MD5的文件仍可对比，但精度降低
4. **内存不足**: 大量文件时可能需要增加JVM内存

## 🎯 适用场景

### 生产环境
- 生产与测试环境一致性检查
- 系统升级前后文件变化验证
- 配置文件版本对比
- 部署验证和回归测试

### 开发环境
- 代码版本差异分析
- 构建产物对比
- 依赖库版本检查
- 环境配置同步

现在您可以根据需要选择合适的工具进行环境对比分析了！
