package com.ideal.envc.interaction.sysm;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.interaction.model.*;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.system.api.IBusinessSystem;
import com.ideal.system.api.IBusinessSystemCompuerList;
import com.ideal.system.api.ICenter;
import com.ideal.system.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SystemInteract单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("系统交互类测试")
class SystemInteractTest {

    @Mock
    private IBusinessSystem businessSystem;

    @Mock
    private IBusinessSystemCompuerList businessSystemComputerList;

    @Mock
    private ICenter center;

    private SystemInteract systemInteract;

    private UserDto userDto;
    private ProjectQueryDto projectQueryDto;
    private BusinessSystemApiDto businessSystemApiDto;
    private SystemComputerListQueryIo systemComputerListQueryIo;
    private CenterQueryDto centerQueryDto;

    @BeforeEach
    void setUp() {
        systemInteract = new SystemInteract(businessSystem, businessSystemComputerList, center);

        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("testUser");
        userDto.setFullName("测试用户");

        // 初始化项目查询条件
        projectQueryDto = new ProjectQueryDto();
        projectQueryDto.setBusinessSystemName("测试系统");
        projectQueryDto.setBusinessSystemCode("TEST_SYS");

        // 初始化业务系统DTO
        businessSystemApiDto = new BusinessSystemApiDto();
        businessSystemApiDto.setId(1L);
        businessSystemApiDto.setName("测试系统");
        businessSystemApiDto.setCode("TEST_SYS");
                 businessSystemApiDto.setType(1L);
         businessSystemApiDto.setTypeName("测试类型");
         businessSystemApiDto.setDeleted(0);
         businessSystemApiDto.setDescription("测试描述");

        // 初始化设备查询条件
        systemComputerListQueryIo = new SystemComputerListQueryIo();
        systemComputerListQueryIo.setBusinessSystemId(1L);
        systemComputerListQueryIo.setPageNum(1);
        systemComputerListQueryIo.setPageSize(10);
        systemComputerListQueryIo.setType(0);

        // 初始化中心查询条件
        centerQueryDto = new CenterQueryDto();
        centerQueryDto.setName("测试中心");
    }

    @ParameterizedTest
    @MethodSource("provideGetBusinessSystemListScenarios")
    @DisplayName("获取权限用户下的业务系统列表")
    void testGetBusinessSystemList(Long userId, List<BusinessSystemApiDto> mockResult, int expectedSize) {
        // given
        when(businessSystem.getBusinessSystemInfoByUserIdForApi(userId)).thenReturn(mockResult);

        // when
        List<BusinessSystemJo> result = systemInteract.getBusinessSystemList(userId);

        // then
        assertNotNull(result);
        assertEquals(expectedSize, result.size());

        if (expectedSize > 0 && mockResult != null && !mockResult.isEmpty()) {
            BusinessSystemJo firstJo = result.get(0);
            BusinessSystemApiDto firstDto = mockResult.get(0);
            assertEquals(firstDto.getId(), firstJo.getBusinessSystemId());
            assertEquals(firstDto.getName(), firstJo.getBusinessSystemName());
            assertEquals(firstDto.getCode(), firstJo.getBusinessSystemCode());
        }

        verify(businessSystem).getBusinessSystemInfoByUserIdForApi(userId);
    }

    static Stream<Arguments> provideGetBusinessSystemListScenarios() {
        BusinessSystemApiDto dto = new BusinessSystemApiDto();
        dto.setId(1L);
        dto.setName("测试系统");
        dto.setCode("TEST_SYS");
                 dto.setType(1L);
        dto.setTypeName("测试类型");
        dto.setDeleted(0);

        return Stream.of(
                // 正常情况
                Arguments.of(1L, Arrays.asList(dto), 1),
                // 返回null的情况
                Arguments.of(1L, null, 0),
                // 返回空列表的情况
                Arguments.of(1L, Collections.emptyList(), 0)
        );
    }

    @Test
    @DisplayName("分页查询业务系统列表-成功")
    void testGetBusinessSystemListOfPage_Success() throws Exception {
        // given
        PageInfo<BusinessSystemApiDto> mockPageInfo = new PageInfo<>();
        List<BusinessSystemApiDto> mockList = Arrays.asList(businessSystemApiDto);
        mockPageInfo.setList(mockList);
        mockPageInfo.setTotal(1);
        mockPageInfo.setPageNum(1);
        mockPageInfo.setPageSize(10);

        when(businessSystem.selectBusinessSystemList(any(BusinessSystemQueryDto.class)))
                .thenReturn(mockPageInfo);

        // when
        R<PageInfo<ProjectDto>> result = systemInteract.getBusinessSystemListOfPage(
                projectQueryDto, userDto, 1, 10);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());

        verify(businessSystem).selectBusinessSystemList(any(BusinessSystemQueryDto.class));
    }

    @Test
    @DisplayName("分页查询业务系统列表-返回null")
    void testGetBusinessSystemListOfPage_NullResult() throws Exception {
        // given
        when(businessSystem.selectBusinessSystemList(any(BusinessSystemQueryDto.class)))
                .thenReturn(null);

        // when
        R<PageInfo<ProjectDto>> result = systemInteract.getBusinessSystemListOfPage(
                projectQueryDto, userDto, 1, 10);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());

        verify(businessSystem).selectBusinessSystemList(any(BusinessSystemQueryDto.class));
    }

    @Test
    @DisplayName("分页查询业务系统列表-异常")
    void testGetBusinessSystemListOfPage_Exception() throws Exception {
        // given
        when(businessSystem.selectBusinessSystemList(any(BusinessSystemQueryDto.class)))
                .thenThrow(new RuntimeException("系统异常"));

        // when
        R<PageInfo<ProjectDto>> result = systemInteract.getBusinessSystemListOfPage(
                projectQueryDto, userDto, 1, 10);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());

        verify(businessSystem).selectBusinessSystemList(any(BusinessSystemQueryDto.class));
    }

    @ParameterizedTest
    @MethodSource("provideGetBusinessSystemIdListScenarios")
    @DisplayName("获取权限用户下的业务系统ID集合")
    void testGetBusinessSystemIdList(Long userId, List<BusinessSystemApiDto> mockResult, int expectedSize) {
        // given
        when(businessSystem.getBusinessSystemInfoByUserIdForApi(userId)).thenReturn(mockResult);

        // when
        List<Long> result = systemInteract.getBusinessSystemIdList(userId);

        // then
        assertNotNull(result);
        assertEquals(expectedSize, result.size());

        if (expectedSize > 0 && mockResult != null && !mockResult.isEmpty()) {
            assertEquals(mockResult.get(0).getId(), result.get(0));
        }

        verify(businessSystem).getBusinessSystemInfoByUserIdForApi(userId);
    }

    static Stream<Arguments> provideGetBusinessSystemIdListScenarios() {
        BusinessSystemApiDto dto = new BusinessSystemApiDto();
        dto.setId(1L);
        dto.setName("测试系统");

        return Stream.of(
                // 正常情况
                Arguments.of(1L, Arrays.asList(dto), 1),
                // 返回null的情况
                Arguments.of(1L, null, 0),
                // 返回空列表的情况
                Arguments.of(1L, Collections.emptyList(), 0)
        );
    }

    @ParameterizedTest
    @MethodSource("provideGetBusinessSystemInfoByBusSystemIdForApiScenarios")
    @DisplayName("获取特定业务系统ID集合下的业务系统信息")
    void testGetBusinessSystemInfoByBusSystemIdForApi(List<Long> businessSystemIds, 
                                                     List<BusinessSystemApiDto> mockResult, int expectedSize) {
        // given
        if (businessSystemIds != null && !businessSystemIds.isEmpty()) {
            when(businessSystem.getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds))
                    .thenReturn(mockResult);
        }

        // when
        List<BusinessSystemJo> result = systemInteract.getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);

        // then
        assertNotNull(result);
        assertEquals(expectedSize, result.size());

        if (businessSystemIds != null && !businessSystemIds.isEmpty()) {
            verify(businessSystem).getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
        }
    }

    static Stream<Arguments> provideGetBusinessSystemInfoByBusSystemIdForApiScenarios() {
        BusinessSystemApiDto dto = new BusinessSystemApiDto();
        dto.setId(1L);
        dto.setName("测试系统");
        dto.setCode("TEST_SYS");

        return Stream.of(
                // 正常情况
                Arguments.of(Arrays.asList(1L), Arrays.asList(dto), 1),
                // 返回null的情况
                Arguments.of(Arrays.asList(1L), null, 0),
                // 空的业务系统ID列表
                Arguments.of(Collections.emptyList(), null, 0),
                // null的业务系统ID列表
                Arguments.of(null, null, 0)
        );
    }

    @Test
    @DisplayName("获取业务系统设备列表-成功")
    void testGetBusinessSystemComputerList_Success() {
        // given
                 BusinessSystemCompuerListApiDto computerDto = new BusinessSystemCompuerListApiDto();
         computerDto.setComputerId(1L);
         computerDto.setComputerIp("***********");

        PageInfo<BusinessSystemCompuerListApiDto> mockPageInfo = new PageInfo<>();
        mockPageInfo.setList(Arrays.asList(computerDto));
        mockPageInfo.setTotal(1);

        when(businessSystemComputerList.queryBusinessSystemComputerList(any(SystemComputerListQueryDto.class)))
                .thenReturn(mockPageInfo);

        // when
        R<PageInfo<ComputerJo>> result = systemInteract.getBusinessSystemComputerList(systemComputerListQueryIo);

        // then
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());

        verify(businessSystemComputerList).queryBusinessSystemComputerList(any(SystemComputerListQueryDto.class));
    }

    @Test
    @DisplayName("获取业务系统设备列表-返回null")
    void testGetBusinessSystemComputerList_NullResult() {
        // given
        when(businessSystemComputerList.queryBusinessSystemComputerList(any(SystemComputerListQueryDto.class)))
                .thenReturn(null);

        // when
        R<PageInfo<ComputerJo>> result = systemInteract.getBusinessSystemComputerList(systemComputerListQueryIo);

        // then
        assertNotNull(result);
        assertEquals("130100", result.getCode());

        verify(businessSystemComputerList).queryBusinessSystemComputerList(any(SystemComputerListQueryDto.class));
    }

    @Test
    @DisplayName("获取设备信息映射-成功")
    void testGetComputerInfoMap_Success() {
        // given
        BusinessSystemCompuerListApiDto computerDto = new BusinessSystemCompuerListApiDto();
        computerDto.setComputerId(1L);
        computerDto.setAgentPort("22");
        computerDto.setOsName("Linux");

        when(businessSystemComputerList.queryComputerList(any(SystemComputerListQueryDto.class)))
                .thenReturn(Arrays.asList(computerDto));

        // when
        Map<Long, ComputerInfoDto> result = systemInteract.getComputerInfoMap(systemComputerListQueryIo);

        // then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));

        ComputerInfoDto computerInfo = result.get(1L);
        assertEquals(22, computerInfo.getAgentPort().intValue());
        assertEquals("Linux", computerInfo.getOsName());

        verify(businessSystemComputerList).queryComputerList(any(SystemComputerListQueryDto.class));
    }

    @Test
    @DisplayName("获取设备信息映射-端口转换异常")
    void testGetComputerInfoMap_PortParseException() {
        // given
        BusinessSystemCompuerListApiDto computerDto = new BusinessSystemCompuerListApiDto();
        computerDto.setComputerId(1L);
        computerDto.setAgentPort("invalid_port");
        computerDto.setOsName("Linux");

        when(businessSystemComputerList.queryComputerList(any(SystemComputerListQueryDto.class)))
                .thenReturn(Arrays.asList(computerDto));

        // when
        Map<Long, ComputerInfoDto> result = systemInteract.getComputerInfoMap(systemComputerListQueryIo);

        // then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));

        ComputerInfoDto computerInfo = result.get(1L);
        assertNull(computerInfo.getAgentPort()); // 端口转换失败，应该为null
        assertEquals("Linux", computerInfo.getOsName());

        verify(businessSystemComputerList).queryComputerList(any(SystemComputerListQueryDto.class));
    }

    @Test
    @DisplayName("获取设备信息映射-返回空列表")
    void testGetComputerInfoMap_EmptyResult() {
        // given
        when(businessSystemComputerList.queryComputerList(any(SystemComputerListQueryDto.class)))
                .thenReturn(Collections.emptyList());

        // when
        Map<Long, ComputerInfoDto> result = systemInteract.getComputerInfoMap(systemComputerListQueryIo);

        // then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(businessSystemComputerList).queryComputerList(any(SystemComputerListQueryDto.class));
    }

    @ParameterizedTest
    @MethodSource("provideGetCenterListScenarios")
    @DisplayName("获取中心列表")
    void testGetCenterList(CenterQueryDto queryDto, List<CenterApiDto> mockResult, int expectedSize) {
        // given
        when(center.getCenterListForApi(any(CenterApiDto.class))).thenReturn(mockResult);

        // when
        List<CenterDto> result = systemInteract.getCenterList(queryDto);

        // then
        assertNotNull(result);
        assertEquals(expectedSize, result.size());

        verify(center).getCenterListForApi(any(CenterApiDto.class));
    }

    static Stream<Arguments> provideGetCenterListScenarios() {
        CenterApiDto centerApiDto = new CenterApiDto();
        centerApiDto.setId(1L);
        centerApiDto.setName("测试中心");
        centerApiDto.setDeleted(1); // 未删除

        CenterApiDto deletedCenterApiDto = new CenterApiDto();
        deletedCenterApiDto.setId(2L);
        deletedCenterApiDto.setName("已删除中心");
        deletedCenterApiDto.setDeleted(0); // 已删除

        CenterQueryDto validQuery = new CenterQueryDto();
        validQuery.setName("测试中心");

        return Stream.of(
                // 正常情况
                Arguments.of(validQuery, Arrays.asList(centerApiDto), 1),
                // 包含已删除的中心（应该被过滤掉）
                Arguments.of(validQuery, new ArrayList<>(Arrays.asList(centerApiDto, deletedCenterApiDto)), 1),
                // 返回null的情况
                Arguments.of(validQuery, null, 0),
                // 查询条件为null的情况
                Arguments.of(null, Arrays.asList(centerApiDto), 1)
        );
    }

    @Test
    @DisplayName("获取中心ID与名称映射-成功")
    void testGetCenterMap_Success() {
        // given
        CenterApiDto centerApiDto = new CenterApiDto();
        centerApiDto.setId(1L);
        centerApiDto.setName("测试中心");
        centerApiDto.setDeleted(1);

        when(center.getCenterListForApi(any(CenterApiDto.class)))
                .thenReturn(Arrays.asList(centerApiDto));

        // when
        Map<Long, String> result = systemInteract.getCenterMap(centerQueryDto);

        // then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1L));
        assertEquals("测试中心", result.get(1L));

        verify(center).getCenterListForApi(any(CenterApiDto.class));
    }

    @Test
    @DisplayName("获取中心ID与名称映射-空结果")
    void testGetCenterMap_EmptyResult() {
        // given
        when(center.getCenterListForApi(any(CenterApiDto.class)))
                .thenReturn(null);

        // when
        Map<Long, String> result = systemInteract.getCenterMap(centerQueryDto);

        // then
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(center).getCenterListForApi(any(CenterApiDto.class));
    }

    @Test
    @DisplayName("获取业务系统设备列表分页信息-组装参数异常")
    void testGetBusinessSystemCompuerListOfPageInfo_BuildParamException() {
        // given
        SystemComputerListQueryIo invalidQueryIo = null;

        // when
        PageInfo<ComputerJo> result = systemInteract.getBusinessSystemCompuerListOfPageInfo(invalidQueryIo);

        // then
        assertNotNull(result);
        assertTrue(result.getList() == null || result.getList().isEmpty());
    }

    @Test
    @DisplayName("获取业务系统设备列表分页信息-带标签")
    void testGetBusinessSystemCompuerListOfPageInfo_WithTags() {
        // given
        SystemComputerListQueryInnerIo innerIo = new SystemComputerListQueryInnerIo();
        innerIo.setTags("tag1,tag2,tag3");
        innerIo.setBusinessSystemId(1L);

        systemComputerListQueryIo.setQueryParam(innerIo);

        BusinessSystemCompuerListApiDto computerDto = new BusinessSystemCompuerListApiDto();
        computerDto.setComputerId(1L);
        computerDto.setComputerIp("***********");
        
        // 设置标签
        TagApiDto tag1 = new TagApiDto();
        tag1.setName("tag1");
        TagApiDto tag2 = new TagApiDto();
        tag2.setName("tag2");
        computerDto.setTagList(Arrays.asList(tag1, tag2));

        PageInfo<BusinessSystemCompuerListApiDto> mockPageInfo = new PageInfo<>();
        mockPageInfo.setList(Arrays.asList(computerDto));
        mockPageInfo.setTotal(1);

        when(businessSystemComputerList.queryBusinessSystemComputerList(any(SystemComputerListQueryDto.class)))
                .thenReturn(mockPageInfo);

        // when
        PageInfo<ComputerJo> result = systemInteract.getBusinessSystemCompuerListOfPageInfo(systemComputerListQueryIo);

        // then
        assertNotNull(result);
        assertEquals(1, result.getList().size());
        
        ComputerJo computerJo = result.getList().get(0);
        assertEquals("tag1,tag2", computerJo.getTags());

        verify(businessSystemComputerList).queryBusinessSystemComputerList(any(SystemComputerListQueryDto.class));
    }
} 