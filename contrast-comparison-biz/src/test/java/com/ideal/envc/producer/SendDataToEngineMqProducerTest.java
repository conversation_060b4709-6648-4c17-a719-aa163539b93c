package com.ideal.envc.producer;

import com.ideal.envc.config.ContrastConfiguration;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.enums.MessageTopicEnum;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 发送数据到引擎MQ生产者单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class SendDataToEngineMqProducerTest {

    @Mock
    private IPublisher publisher;

    @Mock
    private ContrastConfiguration contrastConfiguration;

    @InjectMocks
    private SendDataToEngineMqProducer sendDataToEngineMqProducer;

    private Map<String, Object> testMessage;
    private List<String> testTaskFlowList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testMessage = new HashMap<>();
        testMessage.put("taskId", "task123");
        testMessage.put("type", "START");
        testMessage.put("timestamp", System.currentTimeMillis());

        testTaskFlowList = Arrays.asList("flow1", "flow2", "flow3");
    }

    @Test
    @DisplayName("测试发送任务到引擎_成功_使用配置中心topic")
    void testSendTaskToEngine_Success_WithConfigTopic() throws Exception {
        // 设置配置中心Mock
        ContrastConfiguration.Engine engine = new ContrastConfiguration.Engine();
        ContrastConfiguration.MqConfig sendTaskConfig = new ContrastConfiguration.MqConfig();
        sendTaskConfig.setTopic("customTopic");
        sendTaskConfig.setEnabled(true);
        engine.setSendTask(sendTaskConfig);
        
        when(contrastConfiguration.getEngine()).thenReturn(engine);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        String expectedChannel = "customTopic-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试发送任务到引擎_成功_使用默认topic")
    void testSendTaskToEngine_Success_WithDefaultTopic() throws Exception {
        // 设置配置中心Mock - 返回null或空配置
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试发送任务到引擎_成功_配置禁用时使用默认topic")
    void testSendTaskToEngine_Success_ConfigDisabledUseDefault() throws Exception {
        // 设置配置中心Mock - 配置禁用
        ContrastConfiguration.Engine engine = new ContrastConfiguration.Engine();
        ContrastConfiguration.MqConfig sendTaskConfig = new ContrastConfiguration.MqConfig();
        sendTaskConfig.setTopic("customTopic");
        sendTaskConfig.setEnabled(false);
        engine.setSendTask(sendTaskConfig);
        
        when(contrastConfiguration.getEngine()).thenReturn(engine);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用 - 应该使用默认topic
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试发送任务到引擎_成功_配置topic为空时使用默认topic")
    void testSendTaskToEngine_Success_EmptyConfigTopicUseDefault() throws Exception {
        // 设置配置中心Mock - topic为空
        ContrastConfiguration.Engine engine = new ContrastConfiguration.Engine();
        ContrastConfiguration.MqConfig sendTaskConfig = new ContrastConfiguration.MqConfig();
        sendTaskConfig.setTopic("");
        sendTaskConfig.setEnabled(true);
        engine.setSendTask(sendTaskConfig);
        
        when(contrastConfiguration.getEngine()).thenReturn(engine);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);

        // 验证方法调用 - 应该使用默认topic
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试发送任务到引擎_失败_消息为null")
    void testSendTaskToEngine_Fail_NullMessage() throws Exception {
        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(null);

        // 验证结果
        assertFalse(result);

        // 验证没有调用publisher
        verifyNoInteractions(publisher);
    }

    @Test
    @DisplayName("测试发送任务到引擎_抛出异常_通信异常")
    void testSendTaskToEngine_ThrowException_CommunicationException() throws Exception {
        // 设置Mock行为 - 抛出通信异常
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doThrow(new CommunicationException("发送失败")).when(publisher).apply(anyString(), anyString());

        // 执行测试方法并验证异常
        EngineServiceException exception = assertThrows(EngineServiceException.class, () -> {
            sendDataToEngineMqProducer.sendTaskToEngine(testMessage);
        });

        // 验证异常信息
        assertNotNull(exception.getCause());
        assertTrue(exception.getCause() instanceof CommunicationException);
        assertEquals("发送失败", exception.getCause().getMessage());
    }

    @Test
    @DisplayName("测试发送任务到引擎_验证JSON序列化")
    void testSendTaskToEngine_VerifyJsonSerialization() throws Exception {
        // 设置Mock
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);

        // 验证JSON序列化的消息内容
        verify(publisher, times(1)).apply(anyString(), argThat(jsonString -> {
            String json = (String) jsonString;
            return json.contains("task123") && json.contains("START");
        }));
    }

    @Test
    @DisplayName("测试发送启动任务流程数据_成功")
    void testSendStartTaskFlowData_Success() throws Exception {
        // 设置Mock
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendStartTaskFlowData(testTaskFlowList);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试发送启动任务流程数据_失败_数据为null")
    void testSendStartTaskFlowData_Fail_NullData() throws Exception {
        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendStartTaskFlowData(null);

        // 验证结果
        assertFalse(result);

        // 验证没有调用publisher
        verifyNoInteractions(publisher);
    }

    @Test
    @DisplayName("测试发送启动任务流程数据_抛出异常")
    void testSendStartTaskFlowData_ThrowException() throws Exception {
        // 设置Mock行为 - 抛出异常
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doThrow(new CommunicationException("网络异常")).when(publisher).apply(anyString(), anyString());

        // 执行测试方法并验证异常
        EngineServiceException exception = assertThrows(EngineServiceException.class, () -> {
            sendDataToEngineMqProducer.sendStartTaskFlowData(testTaskFlowList);
        });

        // 验证异常
        assertNotNull(exception.getCause());
        assertTrue(exception.getCause() instanceof CommunicationException);
    }

    @Test
    @DisplayName("测试配置获取_完整配置路径")
    void testGetTopicFromConfig_FullConfigPath() throws Exception {
        // 设置完整的配置路径
        ContrastConfiguration.Engine engine = new ContrastConfiguration.Engine();
        ContrastConfiguration.MqConfig sendTaskConfig = new ContrastConfiguration.MqConfig();
        sendTaskConfig.setTopic("fullConfigTopic");
        sendTaskConfig.setEnabled(true);
        engine.setSendTask(sendTaskConfig);
        
        when(contrastConfiguration.getEngine()).thenReturn(engine);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);
        verify(publisher, times(1)).apply(eq("fullConfigTopic-out-0"), anyString());
    }

    @Test
    @DisplayName("测试配置获取_Engine为null")
    void testGetTopicFromConfig_EngineNull() throws Exception {
        // 设置Engine为null
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试配置获取_SendTask为null")
    void testGetTopicFromConfig_SendTaskNull() throws Exception {
        // 设置SendTask为null
        ContrastConfiguration.Engine engine = new ContrastConfiguration.Engine();
        engine.setSendTask(null);
        
        when(contrastConfiguration.getEngine()).thenReturn(engine);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试配置获取_ContrastConfiguration为null")
    void testGetTopicFromConfig_ContrastConfigurationNull() throws Exception {
        // 创建新的实例，contrastConfiguration为null
        SendDataToEngineMqProducer producer = new SendDataToEngineMqProducer(publisher, null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = producer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);
        String expectedChannel = MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel() + "-out-0";
        verify(publisher, times(1)).apply(eq(expectedChannel), anyString());
    }

    @Test
    @DisplayName("测试发送任务_复杂对象")
    void testSendTaskToEngine_ComplexObject() throws Exception {
        // 创建复杂对象
        Map<String, Object> complexMessage = new HashMap<>();
        complexMessage.put("taskId", "complex123");
        complexMessage.put("nested", Arrays.asList("item1", "item2"));
        Map<String, String> subMap = new HashMap<>();
        subMap.put("key1", "value1");
        complexMessage.put("subObject", subMap);

        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(complexMessage);

        // 验证结果
        assertTrue(result);
        verify(publisher, times(1)).apply(anyString(), anyString());
    }

    @Test
    @DisplayName("测试发送任务_字符串消息")
    void testSendTaskToEngine_StringMessage() throws Exception {
        String stringMessage = "simple string message";
        
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(stringMessage);

        // 验证结果
        assertTrue(result);
        verify(publisher, times(1)).apply(anyString(), eq("\"simple string message\""));
    }

    @Test
    @DisplayName("测试发送任务_数字消息")
    void testSendTaskToEngine_NumberMessage() throws Exception {
        Integer numberMessage = 12345;
        
        when(contrastConfiguration.getEngine()).thenReturn(null);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(numberMessage);

        // 验证结果
        assertTrue(result);
        verify(publisher, times(1)).apply(anyString(), eq("12345"));
    }

    @Test
    @DisplayName("测试发送任务到引擎_失败_topic为空")
    void testSendTaskToEngine_Fail_EmptyTopic() throws Exception {
        // 创建一个返回空topic的Producer实例
        SendDataToEngineMqProducer producer = new SendDataToEngineMqProducer(publisher, contrastConfiguration) {
            @Override
            public boolean sendTaskToEngine(Object message) throws EngineServiceException {
                if (message == null) {
                    return false;
                }
                
                try {
                    // 模拟getTopicFromConfig返回空字符串
                    String topicValue = "";
                    if (StringUtils.isBlank(topicValue)) {
                        return false;
                    }
                    return true;
                } catch (Exception e) {
                    throw new EngineServiceException(e);
                }
            }
        };

        // 执行测试方法
        boolean result = producer.sendTaskToEngine(testMessage);

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试配置获取_enabled为null时使用配置topic")
    void testGetTopicFromConfig_EnabledNull() throws Exception {
        // 设置enabled为null的配置
        ContrastConfiguration.Engine engine = new ContrastConfiguration.Engine();
        ContrastConfiguration.MqConfig sendTaskConfig = new ContrastConfiguration.MqConfig();
        sendTaskConfig.setTopic("enabledNullTopic");
        sendTaskConfig.setEnabled(null); // 设置为null
        engine.setSendTask(sendTaskConfig);
        
        when(contrastConfiguration.getEngine()).thenReturn(engine);
        doNothing().when(publisher).apply(anyString(), anyString());

        // 执行测试方法
        boolean result = sendDataToEngineMqProducer.sendTaskToEngine(testMessage);

        // 验证结果
        assertTrue(result);
        // 验证使用了配置的topic（因为enabled为null时应该使用配置的topic）
        verify(publisher, times(1)).apply(eq("enabledNullTopic-out-0"), anyString());
    }
} 