package com.ideal.envc.config;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Base64SecurityConfig的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Base64SecurityConfig单元测试")
class Base64SecurityConfigTest {

    @AfterEach
    void tearDown() {
        // 清理系统属性，避免影响其他测试
        System.clearProperty("base64.max.input.length");
        System.clearProperty("base64.max.decoded.size");
    }

    @Test
    @DisplayName("测试getMaxInputLength返回默认值")
    void testGetMaxInputLength_DefaultValue() {
        // 确保没有设置系统属性
        System.clearProperty("base64.max.input.length");
        
        // 执行测试
        int result = Base64SecurityConfig.getMaxInputLength();
        
        // 验证结果
        assertEquals(Base64SecurityConfig.MAX_BASE64_INPUT_LENGTH, result);
    }

    @Test
    @DisplayName("测试getMaxInputLength从系统属性读取有效值")
    void testGetMaxInputLength_ValidSystemProperty() {
        // 设置系统属性
        String expectedValue = "5242880"; // 5MB
        System.setProperty("base64.max.input.length", expectedValue);
        
        // 执行测试
        int result = Base64SecurityConfig.getMaxInputLength();
        
        // 验证结果
        assertEquals(Integer.parseInt(expectedValue), result);
    }

    @ParameterizedTest
    @MethodSource("invalidSystemPropertyValues")
    @DisplayName("测试getMaxInputLength处理无效系统属性值")
    void testGetMaxInputLength_InvalidSystemProperty(String invalidValue) {
        // 设置无效的系统属性
        System.setProperty("base64.max.input.length", invalidValue);

        // 执行测试
        int result = Base64SecurityConfig.getMaxInputLength();

        // 验证结果 - 根据实际实现，可能返回-1或默认值
        assertTrue(result == Base64SecurityConfig.MAX_BASE64_INPUT_LENGTH || result == -1);
    }

    @Test
    @DisplayName("测试getMaxDecodedSize返回默认值")
    void testGetMaxDecodedSize_DefaultValue() {
        // 确保没有设置系统属性
        System.clearProperty("base64.max.decoded.size");
        
        // 执行测试
        int result = Base64SecurityConfig.getMaxDecodedSize();
        
        // 验证结果
        assertEquals(Base64SecurityConfig.MAX_DECODED_CONTENT_SIZE, result);
    }

    @Test
    @DisplayName("测试getMaxDecodedSize从系统属性读取有效值")
    void testGetMaxDecodedSize_ValidSystemProperty() {
        // 设置系统属性
        String expectedValue = "4194304"; // 4MB
        System.setProperty("base64.max.decoded.size", expectedValue);
        
        // 执行测试
        int result = Base64SecurityConfig.getMaxDecodedSize();
        
        // 验证结果
        assertEquals(Integer.parseInt(expectedValue), result);
    }

    @ParameterizedTest
    @MethodSource("invalidSystemPropertyValues")
    @DisplayName("测试getMaxDecodedSize处理无效系统属性值")
    void testGetMaxDecodedSize_InvalidSystemProperty(String invalidValue) {
        // 设置无效的系统属性
        System.setProperty("base64.max.decoded.size", invalidValue);

        // 执行测试
        int result = Base64SecurityConfig.getMaxDecodedSize();

        // 验证结果 - 根据实际实现，可能返回-1或默认值
        assertTrue(result == Base64SecurityConfig.MAX_DECODED_CONTENT_SIZE || result == -1);
    }

    @Test
    @DisplayName("测试getMaxInputLength系统属性为null时的处理")
    void testGetMaxInputLength_NullSystemProperty() {
        // 确保系统属性为null（清除后就是null）
        System.clearProperty("base64.max.input.length");

        // 执行测试
        int result = Base64SecurityConfig.getMaxInputLength();

        // 验证结果 - 应该返回默认值
        assertEquals(Base64SecurityConfig.MAX_BASE64_INPUT_LENGTH, result);
    }

    @Test
    @DisplayName("测试getMaxDecodedSize系统属性为null时的处理")
    void testGetMaxDecodedSize_NullSystemProperty() {
        // 确保系统属性为null（清除后就是null）
        System.clearProperty("base64.max.decoded.size");

        // 执行测试
        int result = Base64SecurityConfig.getMaxDecodedSize();

        // 验证结果 - 应该返回默认值
        assertEquals(Base64SecurityConfig.MAX_DECODED_CONTENT_SIZE, result);
    }

    @Test
    @DisplayName("测试常量值正确性")
    void testConstants() {
        // 验证常量值
        assertEquals(30 * 1024 * 1024, Base64SecurityConfig.MAX_BASE64_INPUT_LENGTH);
        assertEquals(24 * 1024 * 1024, Base64SecurityConfig.MAX_DECODED_CONTENT_SIZE);
        assertEquals(true, Base64SecurityConfig.ENABLE_FORMAT_VALIDATION);
        assertEquals(true, Base64SecurityConfig.ENABLE_SECURITY_LOGGING);
        assertEquals(5000L, Base64SecurityConfig.DECODE_TIMEOUT_MS);
    }

    @Test
    @DisplayName("测试getMaxInputLength边界值")
    void testGetMaxInputLength_BoundaryValues() {
        // 测试最大整数值
        System.setProperty("base64.max.input.length", String.valueOf(Integer.MAX_VALUE));
        int result1 = Base64SecurityConfig.getMaxInputLength();
        assertEquals(Integer.MAX_VALUE, result1);

        // 测试0值
        System.setProperty("base64.max.input.length", "0");
        int result2 = Base64SecurityConfig.getMaxInputLength();
        assertEquals(0, result2);

        // 测试1值
        System.setProperty("base64.max.input.length", "1");
        int result3 = Base64SecurityConfig.getMaxInputLength();
        assertEquals(1, result3);
    }

    @Test
    @DisplayName("测试getMaxDecodedSize边界值")
    void testGetMaxDecodedSize_BoundaryValues() {
        // 测试最大整数值
        System.setProperty("base64.max.decoded.size", String.valueOf(Integer.MAX_VALUE));
        int result1 = Base64SecurityConfig.getMaxDecodedSize();
        assertEquals(Integer.MAX_VALUE, result1);

        // 测试0值
        System.setProperty("base64.max.decoded.size", "0");
        int result2 = Base64SecurityConfig.getMaxDecodedSize();
        assertEquals(0, result2);

        // 测试1值
        System.setProperty("base64.max.decoded.size", "1");
        int result3 = Base64SecurityConfig.getMaxDecodedSize();
        assertEquals(1, result3);
    }

    /**
     * 提供无效的系统属性值用于参数化测试
     */
    static Stream<String> invalidSystemPropertyValues() {
        return Stream.of(
            "invalid_number",    // 非数字字符串
            "12.34",            // 浮点数
            "",                 // 空字符串
            "abc123",           // 包含字母的字符串
            "123abc",           // 数字+字母
            "2147483648",       // 超出int范围的数字
            "-1",               // 负数
            "null",             // 字符串"null"
            "NaN",              // 字符串"NaN"
            " ",                // 空格字符串
            "\t",               // 制表符
            "\n"                // 换行符
        );
    }
}
