package com.ideal.envc.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * ContrastConfiguration的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ContrastConfiguration单元测试")
public class ContrastConfigurationTest {

    @InjectMocks
    private ContrastConfiguration contrastConfiguration;

    private ContrastConfiguration.Engine engine;
    private ContrastConfiguration.Platform platform;
    private ContrastConfiguration.MqConfig mqConfig;

    @BeforeEach
    void setUp() {
        engine = new ContrastConfiguration.Engine();
        platform = new ContrastConfiguration.Platform();
        mqConfig = new ContrastConfiguration.MqConfig();
    }

    @Test
    @DisplayName("测试配置类实例化")
    void testConfigInstance() {
        assertNotNull(contrastConfiguration);
    }

    @Test
    @DisplayName("测试Engine配置")
    void testEngineConfiguration() {
        // 准备测试数据
        ContrastConfiguration.MqConfig sendTask = new ContrastConfiguration.MqConfig();
        sendTask.setTopic("send-task-topic");
        sendTask.setGroup("send-task-group");
        sendTask.setEnabled(true);

        ContrastConfiguration.MqConfig taskResult = new ContrastConfiguration.MqConfig();
        taskResult.setTopic("task-result-topic");
        taskResult.setGroup("task-result-group");
        taskResult.setEnabled(false);

        engine.setSendTask(sendTask);
        engine.setTaskResult(taskResult);
        contrastConfiguration.setEngine(engine);

        // 验证结果
        assertNotNull(contrastConfiguration.getEngine());
        assertEquals(sendTask, contrastConfiguration.getEngine().getSendTask());
        assertEquals(taskResult, contrastConfiguration.getEngine().getTaskResult());
        assertEquals("send-task-topic", contrastConfiguration.getEngine().getSendTask().getTopic());
        assertEquals("send-task-group", contrastConfiguration.getEngine().getSendTask().getGroup());
        assertTrue(contrastConfiguration.getEngine().getSendTask().getEnabled());
        assertEquals("task-result-topic", contrastConfiguration.getEngine().getTaskResult().getTopic());
        assertEquals("task-result-group", contrastConfiguration.getEngine().getTaskResult().getGroup());
        assertFalse(contrastConfiguration.getEngine().getTaskResult().getEnabled());
    }

    @Test
    @DisplayName("测试Platform配置")
    void testPlatformConfiguration() {
        // 准备测试数据
        ContrastConfiguration.MqConfig computer = new ContrastConfiguration.MqConfig();
        computer.setTopic("computer-topic");
        computer.setGroup("computer-group");
        computer.setEnabled(true);

        ContrastConfiguration.MqConfig businessSystem = new ContrastConfiguration.MqConfig();
        businessSystem.setTopic("business-system-topic");
        businessSystem.setGroup("business-system-group");
        businessSystem.setEnabled(false);

        platform.setComputer(computer);
        platform.setBusinessSystem(businessSystem);
        contrastConfiguration.setPlatform(platform);

        // 验证结果
        assertNotNull(contrastConfiguration.getPlatform());
        assertEquals(computer, contrastConfiguration.getPlatform().getComputer());
        assertEquals(businessSystem, contrastConfiguration.getPlatform().getBusinessSystem());
        assertEquals("computer-topic", contrastConfiguration.getPlatform().getComputer().getTopic());
        assertEquals("computer-group", contrastConfiguration.getPlatform().getComputer().getGroup());
        assertTrue(contrastConfiguration.getPlatform().getComputer().getEnabled());
        assertEquals("business-system-topic", contrastConfiguration.getPlatform().getBusinessSystem().getTopic());
        assertEquals("business-system-group", contrastConfiguration.getPlatform().getBusinessSystem().getGroup());
        assertFalse(contrastConfiguration.getPlatform().getBusinessSystem().getEnabled());
    }

    @Test
    @DisplayName("测试MqConfig配置")
    void testMqConfig() {
        // 准备测试数据
        mqConfig.setTopic("test-topic");
        mqConfig.setGroup("test-group");
        mqConfig.setEnabled(true);

        // 验证结果
        assertEquals("test-topic", mqConfig.getTopic());
        assertEquals("test-group", mqConfig.getGroup());
        assertTrue(mqConfig.getEnabled());
    }

    @Test
    @DisplayName("测试MqConfig默认值")
    void testMqConfigDefaults() {
        // 验证默认值
        assertTrue(mqConfig.getEnabled(), "enabled应该默认为true");
        assertNull(mqConfig.getTopic(), "topic应该默认为null");
        assertNull(mqConfig.getGroup(), "group应该默认为null");
    }

    @Test
    @DisplayName("测试配置序列化")
    void testSerialization() {
        // 验证所有配置类都实现了Serializable接口
        assertTrue(contrastConfiguration instanceof java.io.Serializable);
        assertTrue(engine instanceof java.io.Serializable);
        assertTrue(platform instanceof java.io.Serializable);
        assertTrue(mqConfig instanceof java.io.Serializable);
    }

    @Test
    @DisplayName("测试配置null值处理")
    void testNullHandling() {
        // 测试设置null值
        contrastConfiguration.setEngine(null);
        contrastConfiguration.setPlatform(null);
        
        // 验证结果
        assertNull(contrastConfiguration.getEngine());
        assertNull(contrastConfiguration.getPlatform());
        
        // 测试MqConfig的null值处理
        mqConfig.setTopic(null);
        mqConfig.setGroup(null);
        mqConfig.setEnabled(null);
        
        assertNull(mqConfig.getTopic());
        assertNull(mqConfig.getGroup());
        assertNull(mqConfig.getEnabled());
    }
} 