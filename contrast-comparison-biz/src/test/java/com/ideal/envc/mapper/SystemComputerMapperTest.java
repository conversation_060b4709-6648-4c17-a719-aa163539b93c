package com.ideal.envc.mapper;

import com.ideal.envc.model.entity.SystemComputerEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SystemComputerMapper单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class SystemComputerMapperTest {

    @Mock
    private SystemComputerMapper systemComputerMapper;

    private SystemComputerEntity testComputer1;
    private SystemComputerEntity testComputer2;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testComputer1 = new SystemComputerEntity();
        testComputer1.setComputerIp("*************");
        testComputer1.setComputerName("A-ECIF-APP01");

        testComputer2 = new SystemComputerEntity();
        testComputer2.setComputerIp("*************");
        testComputer2.setComputerName("B-ECIF-APP02");
    }

    @Test
    @DisplayName("测试根据IP查询主机名 - 查询成功")
    void testSelectComputerNameByIp_Success() {
        // 模拟查询结果
        when(systemComputerMapper.selectComputerNameByIp("*************"))
                .thenReturn("A-ECIF-APP01");

        // 执行查询
        String hostname = systemComputerMapper.selectComputerNameByIp("*************");

        // 验证结果
        assertEquals("A-ECIF-APP01", hostname);
        verify(systemComputerMapper).selectComputerNameByIp("*************");
    }

    @Test
    @DisplayName("测试根据IP查询主机名 - 查询不到结果")
    void testSelectComputerNameByIp_NotFound() {
        // 模拟查询不到结果
        when(systemComputerMapper.selectComputerNameByIp("192.168.1.999"))
                .thenReturn(null);

        // 执行查询
        String hostname = systemComputerMapper.selectComputerNameByIp("192.168.1.999");

        // 验证结果
        assertNull(hostname);
        verify(systemComputerMapper).selectComputerNameByIp("192.168.1.999");
    }

    @Test
    @DisplayName("测试根据IP查询主机名 - 空IP")
    void testSelectComputerNameByIp_EmptyIp() {
        // 模拟空IP查询
        when(systemComputerMapper.selectComputerNameByIp(""))
                .thenReturn(null);

        // 执行查询
        String hostname = systemComputerMapper.selectComputerNameByIp("");

        // 验证结果
        assertNull(hostname);
        verify(systemComputerMapper).selectComputerNameByIp("");
    }

    @Test
    @DisplayName("测试根据IP查询主机名 - null IP")
    void testSelectComputerNameByIp_NullIp() {
        // 模拟null IP查询
        when(systemComputerMapper.selectComputerNameByIp(null))
                .thenReturn(null);

        // 执行查询
        String hostname = systemComputerMapper.selectComputerNameByIp(null);

        // 验证结果
        assertNull(hostname);
        verify(systemComputerMapper).selectComputerNameByIp(null);
    }

    @Test
    @DisplayName("测试批量根据IP查询主机名 - 查询成功")
    void testSelectComputerNameMapByIps_Success() {
        // 准备测试数据
        List<String> ipList = Arrays.asList("*************", "*************");
        
        Map<String, SystemComputerEntity> expectedMap = new HashMap<>();
        expectedMap.put("*************", testComputer1);
        expectedMap.put("*************", testComputer2);

        // 模拟查询结果
        when(systemComputerMapper.selectComputerNameMapByIps(ipList))
                .thenReturn(expectedMap);

        // 执行查询
        Map<String, SystemComputerEntity> resultMap = systemComputerMapper.selectComputerNameMapByIps(ipList);

        // 验证结果
        assertNotNull(resultMap);
        assertEquals(2, resultMap.size());
        assertEquals("A-ECIF-APP01", resultMap.get("*************").getComputerName());
        assertEquals("B-ECIF-APP02", resultMap.get("*************").getComputerName());
        verify(systemComputerMapper).selectComputerNameMapByIps(ipList);
    }

    @Test
    @DisplayName("测试批量根据IP查询主机名 - 部分查询到结果")
    void testSelectComputerNameMapByIps_PartialResults() {
        // 准备测试数据
        List<String> ipList = Arrays.asList("*************", "192.168.1.999");
        
        Map<String, SystemComputerEntity> expectedMap = new HashMap<>();
        expectedMap.put("*************", testComputer1);
        // 192.168.1.999 查询不到，不在结果中

        // 模拟查询结果
        when(systemComputerMapper.selectComputerNameMapByIps(ipList))
                .thenReturn(expectedMap);

        // 执行查询
        Map<String, SystemComputerEntity> resultMap = systemComputerMapper.selectComputerNameMapByIps(ipList);

        // 验证结果
        assertNotNull(resultMap);
        assertEquals(1, resultMap.size());
        assertEquals("A-ECIF-APP01", resultMap.get("*************").getComputerName());
        assertNull(resultMap.get("192.168.1.999"));
        verify(systemComputerMapper).selectComputerNameMapByIps(ipList);
    }

    @Test
    @DisplayName("测试批量根据IP查询主机名 - 空列表")
    void testSelectComputerNameMapByIps_EmptyList() {
        // 准备测试数据
        List<String> emptyList = Arrays.asList();
        Map<String, SystemComputerEntity> expectedMap = new HashMap<>();

        // 模拟查询结果
        when(systemComputerMapper.selectComputerNameMapByIps(emptyList))
                .thenReturn(expectedMap);

        // 执行查询
        Map<String, SystemComputerEntity> resultMap = systemComputerMapper.selectComputerNameMapByIps(emptyList);

        // 验证结果
        assertNotNull(resultMap);
        assertTrue(resultMap.isEmpty());
        verify(systemComputerMapper).selectComputerNameMapByIps(emptyList);
    }

    @Test
    @DisplayName("测试批量根据IP查询主机名 - null列表")
    void testSelectComputerNameMapByIps_NullList() {
        // 模拟null列表查询
        when(systemComputerMapper.selectComputerNameMapByIps(null))
                .thenReturn(new HashMap<>());

        // 执行查询
        Map<String, SystemComputerEntity> resultMap = systemComputerMapper.selectComputerNameMapByIps(null);

        // 验证结果
        assertNotNull(resultMap);
        assertTrue(resultMap.isEmpty());
        verify(systemComputerMapper).selectComputerNameMapByIps(null);
    }

    @Test
    @DisplayName("测试批量根据IP查询主机名 - 单个IP")
    void testSelectComputerNameMapByIps_SingleIp() {
        // 准备测试数据
        List<String> singleIpList = Arrays.asList("*************");
        
        Map<String, SystemComputerEntity> expectedMap = new HashMap<>();
        expectedMap.put("*************", testComputer1);

        // 模拟查询结果
        when(systemComputerMapper.selectComputerNameMapByIps(singleIpList))
                .thenReturn(expectedMap);

        // 执行查询
        Map<String, SystemComputerEntity> resultMap = systemComputerMapper.selectComputerNameMapByIps(singleIpList);

        // 验证结果
        assertNotNull(resultMap);
        assertEquals(1, resultMap.size());
        assertEquals("A-ECIF-APP01", resultMap.get("*************").getComputerName());
        verify(systemComputerMapper).selectComputerNameMapByIps(singleIpList);
    }

    @Test
    @DisplayName("测试批量根据IP查询主机名 - 重复IP")
    void testSelectComputerNameMapByIps_DuplicateIps() {
        // 准备测试数据（包含重复IP）
        List<String> duplicateIpList = Arrays.asList("*************", "*************", "*************");
        
        Map<String, SystemComputerEntity> expectedMap = new HashMap<>();
        expectedMap.put("*************", testComputer1);
        expectedMap.put("*************", testComputer2);

        // 模拟查询结果
        when(systemComputerMapper.selectComputerNameMapByIps(duplicateIpList))
                .thenReturn(expectedMap);

        // 执行查询
        Map<String, SystemComputerEntity> resultMap = systemComputerMapper.selectComputerNameMapByIps(duplicateIpList);

        // 验证结果
        assertNotNull(resultMap);
        assertEquals(2, resultMap.size()); // 重复IP应该只有一个结果
        assertEquals("A-ECIF-APP01", resultMap.get("*************").getComputerName());
        assertEquals("B-ECIF-APP02", resultMap.get("*************").getComputerName());
        verify(systemComputerMapper).selectComputerNameMapByIps(duplicateIpList);
    }
}
