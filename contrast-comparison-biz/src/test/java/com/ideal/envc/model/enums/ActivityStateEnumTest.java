package com.ideal.envc.model.enums;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;

import static org.junit.jupiter.api.Assertions.*;

class ActivityStateEnumTest {

    @Test
    @DisplayName("测试ACT_STATE_NULL枚举值")
    void testActStateNull() {
        assertEquals("Null", ActivityStateEnum.ACT_STATE_NULL.getCode());
        assertEquals("未运行", ActivityStateEnum.ACT_STATE_NULL.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_WAITING枚举值")
    void testActStateWaiting() {
        assertEquals("Waiting", ActivityStateEnum.ACT_STATE_WAITING.getCode());
        assertEquals("等待", ActivityStateEnum.ACT_STATE_WAITING.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_READY枚举值")
    void testActStateReady() {
        assertEquals("Ready", ActivityStateEnum.ACT_STATE_READY.getCode());
        assertEquals("就绪", ActivityStateEnum.ACT_STATE_READY.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_RUNNING枚举值")
    void testActStateRunning() {
        assertEquals("Running", ActivityStateEnum.ACT_STATE_RUNNING.getCode());
        assertEquals("运行中", ActivityStateEnum.ACT_STATE_RUNNING.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_FINISH枚举值")
    void testActStateFinish() {
        assertEquals("Finished", ActivityStateEnum.ACT_STATE_FINISH.getCode());
        assertEquals("完成", ActivityStateEnum.ACT_STATE_FINISH.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_SKIPPED枚举值")
    void testActStateSkipped() {
        assertEquals("Skipped", ActivityStateEnum.ACT_STATE_SKIPPED.getCode());
        assertEquals("略过", ActivityStateEnum.ACT_STATE_SKIPPED.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_FAIL_SKIPPED枚举值")
    void testActStateFailSkipped() {
        assertEquals("Fail:Skipped", ActivityStateEnum.ACT_STATE_FAIL_SKIPPED.getCode());
        assertEquals("失败:略过", ActivityStateEnum.ACT_STATE_FAIL_SKIPPED.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_FAIL枚举值")
    void testActStateFail() {
        assertEquals("Fail", ActivityStateEnum.ACT_STATE_FAIL.getCode());
        assertEquals("失败", ActivityStateEnum.ACT_STATE_FAIL.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_FAIL_BUSINESS枚举值")
    void testActStateFailBusiness() {
        assertEquals("Fail:Business", ActivityStateEnum.ACT_STATE_FAIL_BUSINESS.getCode());
        assertEquals("失败：业务异常", ActivityStateEnum.ACT_STATE_FAIL_BUSINESS.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_EXECUTE枚举值")
    void testActStateExecute() {
        assertEquals("execute", ActivityStateEnum.ACT_STATE_EXECUTE.getCode());
        assertEquals("执行", ActivityStateEnum.ACT_STATE_EXECUTE.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_RETRY枚举值")
    void testActStateRetry() {
        assertEquals("Retry", ActivityStateEnum.ACT_STATE_RETRY.getCode());
        assertEquals("重试", ActivityStateEnum.ACT_STATE_RETRY.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_OTHER枚举值")
    void testActStateOther() {
        assertEquals("Other", ActivityStateEnum.ACT_STATE_OTHER.getCode());
        assertEquals("其他", ActivityStateEnum.ACT_STATE_OTHER.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_TIMEOUT枚举值")
    void testActStateTimeout() {
        assertEquals("Timeout", ActivityStateEnum.ACT_STATE_TIMEOUT.getCode());
        assertEquals("超时", ActivityStateEnum.ACT_STATE_TIMEOUT.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_ARRIVED枚举值")
    void testActStateArrived() {
        assertEquals("Arrived", ActivityStateEnum.ACT_STATE_ARRIVED.getCode());
        assertEquals("达到", ActivityStateEnum.ACT_STATE_ARRIVED.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_OVER枚举值")
    void testActStateOver() {
        assertEquals("Over", ActivityStateEnum.ACT_STATE_OVER.getCode());
        assertEquals("结束", ActivityStateEnum.ACT_STATE_OVER.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_MANUAL_RUNNING枚举值")
    void testActStateManualRunning() {
        assertEquals("ManualRunning", ActivityStateEnum.ACT_STATE_MANUAL_RUNNING.getCode());
        assertEquals("异常处理中", ActivityStateEnum.ACT_STATE_MANUAL_RUNNING.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_MANUAL_FINISH枚举值")
    void testActStateManualFinish() {
        assertEquals("ManualFinish", ActivityStateEnum.ACT_STATE_MANUAL_FINISH.getCode());
        assertEquals("人工处理结果", ActivityStateEnum.ACT_STATE_MANUAL_FINISH.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_ERROR_RETRY枚举值")
    void testActStateErrorRetry() {
        assertEquals("ErrorRetry", ActivityStateEnum.ACT_STATE_ERROR_RETRY.getCode());
        assertEquals("异常重试", ActivityStateEnum.ACT_STATE_ERROR_RETRY.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_ERROR_SUC枚举值")
    void testActStateErrorSuc() {
        assertEquals("ErrorSuc", ActivityStateEnum.ACT_STATE_ERROR_SUC.getCode());
        assertEquals("异常重试成功", ActivityStateEnum.ACT_STATE_ERROR_SUC.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_CONTINUE_RUNNING枚举值")
    void testActStateContinueRunning() {
        assertEquals("Continue", ActivityStateEnum.ACT_STATE_CONTINUE_RUNNING.getCode());
        assertEquals("继续", ActivityStateEnum.ACT_STATE_CONTINUE_RUNNING.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_INFOMACTION枚举值")
    void testActStateInfomaction() {
        assertEquals("InfoMaction", ActivityStateEnum.ACT_STATE_INFOMACTION.getCode());
        assertEquals("InfoMaction", ActivityStateEnum.ACT_STATE_INFOMACTION.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_UNRUNED枚举值")
    void testActStateUnruned() {
        assertEquals("Not_Allowed_Exec", ActivityStateEnum.ACT_STATE_UNRUNED.getCode());
        assertEquals("Not_Allowed_Exec", ActivityStateEnum.ACT_STATE_UNRUNED.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_AGENT_DISCONNECT枚举值")
    void testActStateAgentDisconnect() {
        assertEquals("Disconnect", ActivityStateEnum.ACT_STATE_AGENT_DISCONNECT.getCode());
        assertEquals("Disconnect", ActivityStateEnum.ACT_STATE_AGENT_DISCONNECT.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_AGENT_MANUALDISCONNECT枚举值")
    void testActStateAgentManualDisconnect() {
        assertEquals("ManualDisconnect", ActivityStateEnum.ACT_STATE_AGENT_MANUALDISCONNECT.getCode());
        assertEquals("ManualDisconnect", ActivityStateEnum.ACT_STATE_AGENT_MANUALDISCONNECT.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_QUEUEUP枚举值")
    void testActStateQueueup() {
        assertEquals("QueueUp", ActivityStateEnum.ACT_STATE_QUEUEUP.getCode());
        assertEquals("排队", ActivityStateEnum.ACT_STATE_QUEUEUP.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_HANGUP枚举值")
    void testActStateHangup() {
        assertEquals("HangUp", ActivityStateEnum.ACT_STATE_HANGUP.getCode());
        assertEquals("挂起", ActivityStateEnum.ACT_STATE_HANGUP.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_MUTEX_HANGUP枚举值")
    void testActStateMutexHangup() {
        assertEquals("MUTEX_HANGUP", ActivityStateEnum.ACT_STATE_MUTEX_HANGUP.getCode());
        assertEquals("MUTEX_HANGUP", ActivityStateEnum.ACT_STATE_MUTEX_HANGUP.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_DISABLE枚举值")
    void testActStateDisable() {
        assertEquals("Disable", ActivityStateEnum.ACT_STATE_DISABLE.getCode());
        assertEquals("禁用", ActivityStateEnum.ACT_STATE_DISABLE.getDesc());
    }

    @Test
    @DisplayName("测试ACT_AS400_JOBSCREEN_ALTER枚举值")
    void testActAs400JobscreenAlter() {
        assertEquals("AlterAS400", ActivityStateEnum.ACT_AS400_JOBSCREEN_ALTER.getCode());
        assertEquals("AlterAS400", ActivityStateEnum.ACT_AS400_JOBSCREEN_ALTER.getDesc());
    }

    @Test
    @DisplayName("测试ACT_STATE_PAUSED枚举值")
    void testActStatePaused() {
        assertEquals("Paused", ActivityStateEnum.ACT_STATE_PAUSED.getCode());
        assertEquals("Paused", ActivityStateEnum.ACT_STATE_PAUSED.getDesc());
    }

    @ParameterizedTest
    @EnumSource(ActivityStateEnum.class)
    @DisplayName("测试所有枚举值的getter方法不为空")
    void testAllEnumGettersNotNull(ActivityStateEnum state) {
        assertNotNull(state.getCode());
        assertNotNull(state.getDesc());
    }
} 