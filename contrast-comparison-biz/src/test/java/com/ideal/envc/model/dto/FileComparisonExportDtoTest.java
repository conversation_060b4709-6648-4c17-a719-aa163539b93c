package com.ideal.envc.model.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FileComparisonExportDto单元测试
 *
 * <AUTHOR>
 */
public class FileComparisonExportDtoTest {

    private FileComparisonExportDto exportDto;

    @BeforeEach
    void setUp() {
        exportDto = new FileComparisonExportDto();
    }

    @Test
    @DisplayName("测试默认构造函数")
    void testDefaultConstructor() {
        FileComparisonExportDto dto = new FileComparisonExportDto();
        assertNotNull(dto);
        assertNull(dto.getServerType());
        assertNull(dto.getComparisonPath());
        assertNull(dto.getIp());
        assertNull(dto.getHostname());
        assertNull(dto.getTotal());
        assertNull(dto.getMissing());
        assertNull(dto.getExtra());
        assertNull(dto.getInconsistent());
        assertNull(dto.getConsistent());
    }

    @Test
    @DisplayName("测试完整构造函数（包含比对目录）")
    void testFullConstructorWithComparisonPath() {
        String serverType = "基线服务器";
        String comparisonPath = "/opt/app/config";
        String ip = "*************";
        String hostname = "server01";
        Integer total = 100;
        Integer missing = 5;
        Integer extra = 3;
        Integer inconsistent = 2;
        Integer consistent = 90;

        FileComparisonExportDto dto = new FileComparisonExportDto(
                serverType, comparisonPath, ip, hostname, total,
                missing, extra, inconsistent, consistent
        );

        assertEquals(serverType, dto.getServerType());
        assertEquals(comparisonPath, dto.getComparisonPath());
        assertEquals(ip, dto.getIp());
        assertEquals(hostname, dto.getHostname());
        assertEquals(total, dto.getTotal());
        assertEquals(missing, dto.getMissing());
        assertEquals(extra, dto.getExtra());
        assertEquals(inconsistent, dto.getInconsistent());
        assertEquals(consistent, dto.getConsistent());
    }

    @Test
    @DisplayName("测试兼容构造函数（不包含比对目录）")
    void testCompatibleConstructorWithoutComparisonPath() {
        String serverType = "目标服务器";
        String ip = "*************";
        String hostname = "server02";
        Integer total = 95;
        Integer missing = 8;
        Integer extra = 1;
        Integer inconsistent = 4;
        Integer consistent = 82;

        FileComparisonExportDto dto = new FileComparisonExportDto(
                serverType, ip, hostname, total,
                missing, extra, inconsistent, consistent
        );

        assertEquals(serverType, dto.getServerType());
        assertEquals("", dto.getComparisonPath()); // 应该默认为空字符串
        assertEquals(ip, dto.getIp());
        assertEquals(hostname, dto.getHostname());
        assertEquals(total, dto.getTotal());
        assertEquals(missing, dto.getMissing());
        assertEquals(extra, dto.getExtra());
        assertEquals(inconsistent, dto.getInconsistent());
        assertEquals(consistent, dto.getConsistent());
    }

    @Test
    @DisplayName("测试所有getter和setter方法")
    void testGettersAndSetters() {
        String serverType = "测试服务器";
        String comparisonPath = "/home/<USER>/data";
        String ip = "********";
        String hostname = "testhost";
        Integer total = 50;
        Integer missing = 2;
        Integer extra = 1;
        Integer inconsistent = 3;
        Integer consistent = 44;

        exportDto.setServerType(serverType);
        exportDto.setComparisonPath(comparisonPath);
        exportDto.setIp(ip);
        exportDto.setHostname(hostname);
        exportDto.setTotal(total);
        exportDto.setMissing(missing);
        exportDto.setExtra(extra);
        exportDto.setInconsistent(inconsistent);
        exportDto.setConsistent(consistent);

        assertEquals(serverType, exportDto.getServerType());
        assertEquals(comparisonPath, exportDto.getComparisonPath());
        assertEquals(ip, exportDto.getIp());
        assertEquals(hostname, exportDto.getHostname());
        assertEquals(total, exportDto.getTotal());
        assertEquals(missing, exportDto.getMissing());
        assertEquals(extra, exportDto.getExtra());
        assertEquals(inconsistent, exportDto.getInconsistent());
        assertEquals(consistent, exportDto.getConsistent());
    }

    @Test
    @DisplayName("测试toString方法包含比对目录")
    void testToStringWithComparisonPath() {
        exportDto.setServerType("基线服务器");
        exportDto.setComparisonPath("/opt/config");
        exportDto.setIp("*************");
        exportDto.setHostname("server01");
        exportDto.setTotal(100);
        exportDto.setMissing(5);
        exportDto.setExtra(3);
        exportDto.setInconsistent(2);
        exportDto.setConsistent(90);

        String toStringResult = exportDto.toString();
        
        assertNotNull(toStringResult);
        assertTrue(toStringResult.contains("serverType='基线服务器'"));
        assertTrue(toStringResult.contains("comparisonPath='/opt/config'"));
        assertTrue(toStringResult.contains("ip='*************'"));
        assertTrue(toStringResult.contains("hostname='server01'"));
        assertTrue(toStringResult.contains("total=100"));
        assertTrue(toStringResult.contains("missing=5"));
        assertTrue(toStringResult.contains("extra=3"));
        assertTrue(toStringResult.contains("inconsistent=2"));
        assertTrue(toStringResult.contains("consistent=90"));
    }

    @Test
    @DisplayName("测试null值处理")
    void testNullValueHandling() {
        // 所有字段都设置为null
        exportDto.setServerType(null);
        exportDto.setComparisonPath(null);
        exportDto.setIp(null);
        exportDto.setHostname(null);
        exportDto.setTotal(null);
        exportDto.setMissing(null);
        exportDto.setExtra(null);
        exportDto.setInconsistent(null);
        exportDto.setConsistent(null);

        // 验证getter方法能正常返回null
        assertNull(exportDto.getServerType());
        assertNull(exportDto.getComparisonPath());
        assertNull(exportDto.getIp());
        assertNull(exportDto.getHostname());
        assertNull(exportDto.getTotal());
        assertNull(exportDto.getMissing());
        assertNull(exportDto.getExtra());
        assertNull(exportDto.getInconsistent());
        assertNull(exportDto.getConsistent());

        // toString方法应该能处理null值
        assertDoesNotThrow(() -> exportDto.toString());
    }

    @Test
    @DisplayName("测试序列化兼容性")
    void testSerializationCompatibility() {
        assertTrue(exportDto instanceof java.io.Serializable, 
                  "FileComparisonExportDto应该实现Serializable接口");
    }

    @Test
    @DisplayName("测试比对目录字段的特殊场景")
    void testComparisonPathSpecialCases() {
        // 测试空字符串
        exportDto.setComparisonPath("");
        assertEquals("", exportDto.getComparisonPath());

        // 测试包含特殊字符的路径
        String specialPath = "/opt/app-config/测试目录/file with spaces";
        exportDto.setComparisonPath(specialPath);
        assertEquals(specialPath, exportDto.getComparisonPath());

        // 测试Windows路径格式
        String windowsPath = "C:\\Program Files\\App\\config";
        exportDto.setComparisonPath(windowsPath);
        assertEquals(windowsPath, exportDto.getComparisonPath());
    }
}
