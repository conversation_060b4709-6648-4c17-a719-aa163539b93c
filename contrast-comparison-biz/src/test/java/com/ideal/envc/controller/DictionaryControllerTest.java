package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.DictionaryDto;
import com.ideal.envc.model.dto.DictionaryQueryDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IDictionaryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DictionaryController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("字典管理Controller测试")
class DictionaryControllerTest {

    @Mock
    private IDictionaryService dictionaryService;

    private DictionaryController dictionaryController;

    private TableQueryDto<DictionaryQueryDto> tableQueryDto;
    private DictionaryQueryDto dictionaryQueryDto;
    private DictionaryDto dictionaryDto;
    private PageInfo<DictionaryDto> pageInfo;

    @BeforeEach
    void setUp() {
        dictionaryController = new DictionaryController(dictionaryService);

        // 初始化测试数据
        dictionaryQueryDto = new DictionaryQueryDto();
        dictionaryQueryDto.setCode("TEST_CODE");

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(dictionaryQueryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        dictionaryDto = new DictionaryDto();
        dictionaryDto.setId(1L);
        dictionaryDto.setCode("TEST_CODE");
        dictionaryDto.setDescription("测试字典");

        List<DictionaryDto> dictionaryList = new ArrayList<>();
        dictionaryList.add(dictionaryDto);
        pageInfo = new PageInfo<>(dictionaryList);
        pageInfo.setTotal(1);
    }

    @Test
    @DisplayName("查询字典列表-成功")
    void testList_Success() {
        // given
        when(dictionaryService.selectDictionaryList(any(DictionaryQueryDto.class), eq(1), eq(10)))
                .thenReturn(pageInfo);

        // when
        R<PageInfo<DictionaryDto>> result = dictionaryController.list(tableQueryDto);

        // then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());

        verify(dictionaryService).selectDictionaryList(any(DictionaryQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询字典详情-成功")
    void testGetAgentInfoInfo_Success() {
        // given
        Long id = 1L;
        when(dictionaryService.selectDictionaryById(id)).thenReturn(dictionaryDto);

        // when
        R<DictionaryDto> result = dictionaryController.getAgentInfoInfo(id);

        // then
        assertNotNull(result);
        assertNotNull(result.getData());
        assertEquals(dictionaryDto.getId(), result.getData().getId());

        verify(dictionaryService).selectDictionaryById(id);
    }

    @Test
    @DisplayName("新增字典-成功")
    void testSave_Success() {
        // given
        when(dictionaryService.insertDictionary(dictionaryDto)).thenReturn(1);

        // when
        R<Void> result = dictionaryController.save(dictionaryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).insertDictionary(dictionaryDto);
    }

    @Test
    @DisplayName("新增字典-插入失败")
    void testSave_InsertFail() {
        // given
        when(dictionaryService.insertDictionary(dictionaryDto)).thenReturn(0);

        // when
        R<Void> result = dictionaryController.save(dictionaryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.ADD_FAIL.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.ADD_FAIL.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).insertDictionary(dictionaryDto);
    }

    @Test
    @DisplayName("新增字典-系统异常")
    void testSave_SystemException() {
        // given
        when(dictionaryService.insertDictionary(dictionaryDto))
                .thenThrow(new RuntimeException("系统异常"));

        // when
        R<Void> result = dictionaryController.save(dictionaryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).insertDictionary(dictionaryDto);
    }

    @Test
    @DisplayName("修改字典-成功")
    void testUpdate_Success() {
        // given
        when(dictionaryService.updateDictionary(dictionaryDto)).thenReturn(1);

        // when
        R<Void> result = dictionaryController.update(dictionaryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).updateDictionary(dictionaryDto);
    }

    @Test
    @DisplayName("修改字典-系统异常")
    void testUpdate_SystemException() {
        // given
        doThrow(new RuntimeException("系统异常")).when(dictionaryService).updateDictionary(dictionaryDto);

        // when
        R<Void> result = dictionaryController.update(dictionaryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).updateDictionary(dictionaryDto);
    }

    @Test
    @DisplayName("删除字典-成功")
    void testRemove_Success() {
        // given
        Long[] ids = {1L, 2L};
        when(dictionaryService.deleteDictionaryByIds(ids)).thenReturn(2);

        // when
        R<Void> result = dictionaryController.remove(ids);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).deleteDictionaryByIds(ids);
    }

    @Test
    @DisplayName("删除字典-系统异常")
    void testRemove_SystemException() {
        // given
        Long[] ids = {1L, 2L};
        doThrow(new RuntimeException("系统异常")).when(dictionaryService).deleteDictionaryByIds(ids);

        // when
        R<Void> result = dictionaryController.remove(ids);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(dictionaryService).deleteDictionaryByIds(ids);
    }

    @ParameterizedTest
    @MethodSource("provideValidateDictionaryCodeScenarios")
    @DisplayName("验证字典码是否存在")
    void testValidateDictionaryCode(String code, Boolean mockResult, String expectedCode) {
        // given
        when(dictionaryService.validateDictionaryCode(code)).thenReturn(mockResult);

        // when
        R<Boolean> result = dictionaryController.validateDictionaryCode(code);

        // then
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertEquals(mockResult, result.getData());

        verify(dictionaryService).validateDictionaryCode(code);
    }

    static Stream<Arguments> provideValidateDictionaryCodeScenarios() {
        return Stream.of(
                Arguments.of("EXISTING_CODE", true, ResponseCodeEnum.SUCCESS.getCode()),
                Arguments.of("NON_EXISTING_CODE", false, ResponseCodeEnum.SUCCESS.getCode())
        );
    }
} 