package com.ideal.envc.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.RunRuleDto;
import com.ideal.envc.service.IRunRuleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 节点规则结果控制器测试
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IRunRuleService runRuleService;

    @InjectMocks
    private RunRuleController runRuleController;

    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(runRuleController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("测试查询节点规则结果列表")
    void testList() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setId(1L);
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");
        runRuleDto.setEncode("UTF-8");
        runRuleDto.setCreatorName("测试用户");
        runRuleDto.setCreateTime(new Date());
        runRuleDto.setResult(0);
        runRuleDto.setState(1);

        List<RunRuleDto> runRuleDtoList = new ArrayList<>();
        runRuleDtoList.add(runRuleDto);

        PageInfo<RunRuleDto> pageInfo = new PageInfo<>(runRuleDtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);

        // 模拟服务方法，使用更灵活的参数匹配
        when(runRuleService.selectRunRuleList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/rule/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].id").value(1))
                .andExpect(jsonPath("$.data.list[0].path").value("/test/path"));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runRuleService).selectRunRuleList(any(), any(Integer.class), any(Integer.class));
    }

    @Test
    @DisplayName("测试查询节点规则结果详细信息")
    void testGetInfo() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setId(1L);
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");
        runRuleDto.setEncode("UTF-8");
        runRuleDto.setCreatorName("测试用户");
        runRuleDto.setCreateTime(new Date());
        runRuleDto.setResult(0);
        runRuleDto.setState(1);

        // 模拟服务方法，使用更灵活的参数匹配
        when(runRuleService.selectRunRuleById(any(Long.class))).thenReturn(runRuleDto);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/rule/get")
                        .param("id", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.path").value("/test/path"));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runRuleService).selectRunRuleById(any(Long.class));
    }

    @Test
    @DisplayName("测试新增保存节点规则结果-成功")
    void testSave_Success() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");
        runRuleDto.setEncode("UTF-8");
        runRuleDto.setCreatorName("测试用户");
        runRuleDto.setCreateTime(new Date());
        runRuleDto.setResult(0);
        runRuleDto.setState(1);

        // 模拟服务方法返回值大于0表示成功
        when(runRuleService.insertRunRule(any(RunRuleDto.class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).insertRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试新增保存节点规则结果-失败")
    void testSave_Fail() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");
        runRuleDto.setEncode("UTF-8");
        runRuleDto.setCreatorName("测试用户");
        runRuleDto.setCreateTime(new Date());
        runRuleDto.setResult(0);
        runRuleDto.setState(1);

        // 模拟服务方法返回值为0表示失败
        when(runRuleService.insertRunRule(any(RunRuleDto.class))).thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("130200"));

        // 验证服务方法调用
        verify(runRuleService).insertRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点规则结果")
    void testUpdate() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setId(1L);
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");
        runRuleDto.setEncode("UTF-8");
        runRuleDto.setCreatorName("测试用户");
        runRuleDto.setCreateTime(new Date());
        runRuleDto.setResult(0);
        runRuleDto.setState(1);

        // 模拟服务方法
        doReturn(1).when(runRuleService).updateRunRule(any(RunRuleDto.class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).updateRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试删除节点规则结果")
    void testRemove() throws Exception {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟服务方法，使用更灵活的参数匹配
        when(runRuleService.deleteRunRuleByIds(any(Long[].class))).thenReturn(2);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/remove")
                        .param("ids", "1", "2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runRuleService).deleteRunRuleByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试新增保存节点规则结果-系统异常")
    void testSave_SystemException() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");

        // 模拟服务方法抛出异常
        when(runRuleService.insertRunRule(any(RunRuleDto.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runRuleService).insertRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点规则结果-系统异常")
    void testUpdate_SystemException() throws Exception {
        // 准备测试数据
        RunRuleDto runRuleDto = new RunRuleDto();
        runRuleDto.setId(1L);
        runRuleDto.setEnvcRunInstanceInfoId(100L);
        runRuleDto.setPath("/test/path");

        // 模拟服务方法抛出异常
        when(runRuleService.updateRunRule(any(RunRuleDto.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runRuleService).updateRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试删除节点规则结果-系统异常")
    void testRemove_SystemException() throws Exception {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟服务方法抛出异常
        when(runRuleService.deleteRunRuleByIds(any(Long[].class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/remove")
                        .param("ids", "1", "2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runRuleService).deleteRunRuleByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试查询节点规则结果列表-空结果")
    void testList_EmptyResult() throws Exception {
        // 准备空的分页数据
        PageInfo<RunRuleDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
        emptyPageInfo.setTotal(0);
        emptyPageInfo.setPageNum(1);
        emptyPageInfo.setPageSize(10);

        // 模拟服务方法返回空结果
        when(runRuleService.selectRunRuleList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(emptyPageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/rule/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(0))
                .andExpect(jsonPath("$.data.list").isEmpty());

        // 验证服务方法调用
        verify(runRuleService).selectRunRuleList(any(), any(Integer.class), any(Integer.class));
    }

    @Test
    @DisplayName("测试查询节点规则结果详细信息-返回null")
    void testGetInfo_ReturnNull() throws Exception {
        // 模拟服务方法返回null
        when(runRuleService.selectRunRuleById(any(Long.class))).thenReturn(null);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/rule/get")
                        .param("id", "999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data").isEmpty());

        // 验证服务方法调用
        verify(runRuleService).selectRunRuleById(999L);
    }

    @Test
    @DisplayName("测试删除节点规则结果-空ID数组")
    void testRemove_EmptyIds() throws Exception {
        // 模拟服务方法返回0
        when(runRuleService.deleteRunRuleByIds(any(Long[].class))).thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/remove")
                        .param("ids", "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).deleteRunRuleByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试新增保存节点规则结果-空对象")
    void testSave_EmptyObject() throws Exception {
        // 准备空的测试数据
        RunRuleDto runRuleDto = new RunRuleDto();

        // 模拟服务方法返回1
        when(runRuleService.insertRunRule(any(RunRuleDto.class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).insertRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试修改保存节点规则结果-空对象")
    void testUpdate_EmptyObject() throws Exception {
        // 准备空的测试数据
        RunRuleDto runRuleDto = new RunRuleDto();

        // 模拟服务方法
        doReturn(1).when(runRuleService).updateRunRule(any(RunRuleDto.class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runRuleDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).updateRunRule(any(RunRuleDto.class));
    }

    @Test
    @DisplayName("测试查询节点规则结果列表-大分页参数")
    void testList_LargePageParams() throws Exception {
        // 准备测试数据
        PageInfo<RunRuleDto> pageInfo = new PageInfo<>(new ArrayList<>());
        pageInfo.setTotal(0);
        pageInfo.setPageNum(999);
        pageInfo.setPageSize(1000);

        // 模拟服务方法
        when(runRuleService.selectRunRuleList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/rule/list")
                        .param("pageNum", "999")
                        .param("pageSize", "1000")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).selectRunRuleList(any(), any(Integer.class), any(Integer.class));
    }

    @Test
    @DisplayName("测试查询节点规则结果详细信息-无效ID")
    void testGetInfo_InvalidId() throws Exception {
        // 模拟服务方法返回null
        when(runRuleService.selectRunRuleById(any(Long.class))).thenReturn(null);

        // 执行请求并验证结果 - 传入负数ID
        mockMvc.perform(get("/envc/rule/get")
                        .param("id", "-1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data").isEmpty());

        // 验证服务方法调用
        verify(runRuleService).selectRunRuleById(-1L);
    }

    @Test
    @DisplayName("测试删除节点规则结果-单个ID")
    void testRemove_SingleId() throws Exception {
        // 模拟服务方法返回1
        when(runRuleService.deleteRunRuleByIds(any(Long[].class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/rule/remove")
                        .param("ids", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runRuleService).deleteRunRuleByIds(any(Long[].class));
    }
} 