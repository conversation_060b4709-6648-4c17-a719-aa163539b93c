package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.Charset;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class CharsetControllerTest {

    @InjectMocks
    private CharsetController charsetController;

    @Test
    @DisplayName("测试获取字符集列表成功")
    void testGetCharsetList() {
        // 执行测试方法
        R<List<Map<String, String>>> result = charsetController.getCharsetList();

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        
        // 验证返回的字符集列表
        List<Map<String, String>> charsetList = result.getData();
        assertNotNull(charsetList);
        assertFalse(charsetList.isEmpty());
        
        // 验证列表中的每个元素
        for (Map<String, String> charsetInfo : charsetList) {
            assertTrue(charsetInfo.containsKey("name"));
            assertTrue(charsetInfo.containsKey("code"));
            String name = charsetInfo.get("name");
            String code = charsetInfo.get("code");
            assertEquals(name, code);
            assertTrue(Charset.availableCharsets().containsKey(name));
        }
    }
} 