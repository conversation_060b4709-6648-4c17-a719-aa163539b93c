package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IProjectService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.doThrow;

/**
 * 系统配置Controller的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class ProjectControllerTest {

    @Mock
    private IProjectService projectService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private ProjectController projectController;

    private ProjectDto projectDto;
    private ProjectQueryDto queryDto;
    private PageInfo<ProjectDto> pageInfo;
    private TableQueryDto<ProjectQueryDto> tableQueryDto;
    private UserDto userDto;
    private Long[] ids;
    private List<Long> businessSystemIds;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        projectDto = new ProjectDto();
        
        queryDto = new ProjectQueryDto();
        
        List<ProjectDto> list = new ArrayList<>();
        list.add(projectDto);
        pageInfo = new PageInfo<>(list);
        
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);
        
        userDto = new UserDto();
        
        ids = new Long[]{1L, 2L};
        
        businessSystemIds = Arrays.asList(1L, 2L);
    }

    @Test
    @DisplayName("测试查询已添加比对业务系统列表")
    void testList() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(pageInfo).when(projectService).selectProjectList(
                any(ProjectQueryDto.class), any(UserDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<ProjectDto>> result = projectController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).selectProjectList(
                tableQueryDto.getQueryParam(),
                userDto,
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询待添加比对业务系统列表")
    void testGetPendingSystemList() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(pageInfo).when(projectService).getPendingSystemList(
                any(ProjectQueryDto.class), any(UserDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<ProjectDto>> result = projectController.getPendingSystemList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).getPendingSystemList(
                tableQueryDto.getQueryParam(),
                userDto,
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询待添加比对业务系统列表 - 业务异常")
    void testGetPendingSystemListWithBusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new ContrastBusinessException("查询失败")).when(projectService).getPendingSystemList(
                any(ProjectQueryDto.class), any(UserDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<ProjectDto>> result = projectController.getPendingSystemList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals("查询失败", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).getPendingSystemList(
                tableQueryDto.getQueryParam(),
                userDto,
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询待添加比对业务系统列表 - 系统异常")
    void testGetPendingSystemListWithSystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new RuntimeException("系统异常")).when(projectService).getPendingSystemList(
                any(ProjectQueryDto.class), any(UserDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<ProjectDto>> result = projectController.getPendingSystemList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).getPendingSystemList(
                tableQueryDto.getQueryParam(),
                userDto,
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试根据一致性比对系统表主键查询比对业务系统详细信息")
    void testSelectProjectById() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(projectDto).when(projectService).selectProjectById(anyLong());

        // 执行方法
        R<ProjectDto> result = projectController.selectProjectById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        
        // 验证方法调用
        verify(projectService).selectProjectById(1L);
    }

    @Test
    @DisplayName("测试新增保存比对业务系统成功")
    void testSaveSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(1).when(projectService).insertProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.save(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(projectService).insertProject(projectDto);
    }

    @Test
    @DisplayName("测试新增保存比对业务系统失败")
    void testSaveFail() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(0).when(projectService).insertProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.save(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());
        
        // 验证方法调用
        verify(projectService).insertProject(projectDto);
    }

    @Test
    @DisplayName("测试绑定比对业务系统成功")
    void testAddSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(1).when(projectService).addProject(anyList(), any(UserDto.class));

        // 执行方法
        R<Void> result = projectController.add(businessSystemIds);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).addProject(businessSystemIds, userDto);
    }

    @Test
    @DisplayName("测试绑定比对业务系统失败")
    void testAddFail() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(0).when(projectService).addProject(anyList(), any(UserDto.class));

        // 执行方法
        R<Void> result = projectController.add(businessSystemIds);

        // 验证结果
        assertNotNull(result);
        assertEquals("130200", result.getCode());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).addProject(businessSystemIds, userDto);
    }

    @Test
    @DisplayName("测试修改保存比对业务系统")
    void testUpdate() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(1).when(projectService).updateProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.update(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(projectService).updateProject(projectDto);
    }

    @Test
    @DisplayName("测试删除比对业务系统")
    void testRemove() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(1).when(projectService).deleteProjectByIds(any(Long[].class));

        // 执行方法
        R<Void> result = projectController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(projectService).deleteProjectByIds(ids);
    }

    @Test
    @DisplayName("测试查询已添加比对业务系统列表_业务异常")
    void testList_BusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new ContrastBusinessException("查询失败")).when(projectService).selectProjectList(
                any(ProjectQueryDto.class), any(UserDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<ProjectDto>> result = projectController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals("查询失败", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).selectProjectList(
                tableQueryDto.getQueryParam(),
                userDto,
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询已添加比对业务系统列表_系统异常")
    void testList_SystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new RuntimeException("系统异常")).when(projectService).selectProjectList(
                any(ProjectQueryDto.class), any(UserDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<ProjectDto>> result = projectController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).selectProjectList(
                tableQueryDto.getQueryParam(),
                userDto,
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询比对业务系统详情_数据不存在")
    void testSelectProjectById_DataNotFound() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(null).when(projectService).selectProjectById(anyLong());

        // 执行方法
        R<ProjectDto> result = projectController.selectProjectById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).selectProjectById(1L);
    }

    @Test
    @DisplayName("测试查询比对业务系统详情_业务异常")
    void testSelectProjectById_BusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("查询失败")).when(projectService).selectProjectById(anyLong());

        // 执行方法
        R<ProjectDto> result = projectController.selectProjectById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals("查询失败", result.getMessage());
        
        // 验证方法调用
        verify(projectService).selectProjectById(1L);
    }

    @Test
    @DisplayName("测试查询比对业务系统详情_系统异常")
    void testSelectProjectById_SystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("系统异常")).when(projectService).selectProjectById(anyLong());

        // 执行方法
        R<ProjectDto> result = projectController.selectProjectById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).selectProjectById(1L);
    }

    @Test
    @DisplayName("测试新增保存比对业务系统_业务异常")
    void testSave_BusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("新增失败")).when(projectService).insertProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.save(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.ADD_FAIL.getCode(), result.getCode());
        assertEquals("新增失败", result.getMessage());
        
        // 验证方法调用
        verify(projectService).insertProject(projectDto);
    }

    @Test
    @DisplayName("测试新增保存比对业务系统_系统异常")
    void testSave_SystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("系统异常")).when(projectService).insertProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.save(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).insertProject(projectDto);
    }

    @Test
    @DisplayName("测试绑定比对业务系统_业务异常")
    void testAdd_BusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new ContrastBusinessException("绑定失败")).when(projectService).addProject(anyList(), any(UserDto.class));

        // 执行方法
        R<Void> result = projectController.add(businessSystemIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.ADD_FAIL.getCode(), result.getCode());
        assertEquals("绑定失败", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).addProject(businessSystemIds, userDto);
    }

    @Test
    @DisplayName("测试绑定比对业务系统_系统异常")
    void testAdd_SystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new RuntimeException("系统异常")).when(projectService).addProject(anyList(), any(UserDto.class));

        // 执行方法
        R<Void> result = projectController.add(businessSystemIds);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(projectService).addProject(businessSystemIds, userDto);
    }

    @Test
    @DisplayName("测试修改保存比对业务系统_失败")
    void testUpdate_Fail() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(0).when(projectService).updateProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.update(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.UPDATE_FAIL.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.UPDATE_FAIL.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).updateProject(projectDto);
    }

    @Test
    @DisplayName("测试修改保存比对业务系统_业务异常")
    void testUpdate_BusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("修改失败")).when(projectService).updateProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.update(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.UPDATE_FAIL.getCode(), result.getCode());
        assertEquals("修改失败", result.getMessage());
        
        // 验证方法调用
        verify(projectService).updateProject(projectDto);
    }

    @Test
    @DisplayName("测试修改保存比对业务系统_系统异常")
    void testUpdate_SystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("系统异常")).when(projectService).updateProject(any(ProjectDto.class));

        // 执行方法
        R<Void> result = projectController.update(projectDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).updateProject(projectDto);
    }

    @Test
    @DisplayName("测试删除比对业务系统_失败")
    void testRemove_Fail() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(0).when(projectService).deleteProjectByIds(any(Long[].class));

        // 执行方法
        R<Void> result = projectController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DELETE_FAIL.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.DELETE_FAIL.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).deleteProjectByIds(ids);
    }

    @Test
    @DisplayName("测试删除比对业务系统_业务异常")
    void testRemove_BusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("删除失败")).when(projectService).deleteProjectByIds(any(Long[].class));

        // 执行方法
        R<Void> result = projectController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DELETE_FAIL.getCode(), result.getCode());
        assertEquals("删除失败", result.getMessage());
        
        // 验证方法调用
        verify(projectService).deleteProjectByIds(ids);
    }

    @Test
    @DisplayName("测试删除比对业务系统_系统异常")
    void testRemove_SystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("系统异常")).when(projectService).deleteProjectByIds(any(Long[].class));

        // 执行方法
        R<Void> result = projectController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(projectService).deleteProjectByIds(ids);
    }
} 