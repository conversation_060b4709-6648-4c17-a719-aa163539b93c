package com.ideal.envc.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.RunInstanceInfoDto;
import com.ideal.envc.service.IRunInstanceInfoService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 实例详情Controller单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RunInstanceInfoControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IRunInstanceInfoService runInstanceInfoService;

    @InjectMocks
    private RunInstanceInfoController runInstanceInfoController;

    private ObjectMapper objectMapper;
    private RunInstanceInfoDto runInstanceInfoDto;
    private List<RunInstanceInfoDto> runInstanceInfoDtoList;
    private PageInfo<RunInstanceInfoDto> pageInfo;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(runInstanceInfoController).build();
        objectMapper = new ObjectMapper();

        // 初始化测试数据
        runInstanceInfoDto = new RunInstanceInfoDto();
        runInstanceInfoDto.setId(1L);
        runInstanceInfoDto.setEnvcRunInstanceId(100L);
        runInstanceInfoDto.setEnvcPlanId(200L);
        runInstanceInfoDto.setBusinessSystemId(300L);
        runInstanceInfoDto.setSourceCenterId(10L);
        runInstanceInfoDto.setSourceCenterName("源中心");
        runInstanceInfoDto.setTargetCenterId(20L);
        runInstanceInfoDto.setTargetCenterName("目标中心");
        runInstanceInfoDto.setSourceComputerId(1001L);
        runInstanceInfoDto.setSourceComputerIp("*************");
        runInstanceInfoDto.setSourceComputerPort(8080);
        runInstanceInfoDto.setSourceComputerOs("Linux");
        runInstanceInfoDto.setTargetComputerId(1002L);
        runInstanceInfoDto.setTargetComputerIp("*************");
        runInstanceInfoDto.setTargetComputerPort(8080);
        runInstanceInfoDto.setTargetComputerOs("Linux");
        runInstanceInfoDto.setStoreTime(new Date());
        runInstanceInfoDto.setResult(0);
        runInstanceInfoDto.setState(1);

        // 初始化列表数据
        runInstanceInfoDtoList = new ArrayList<>();
        runInstanceInfoDtoList.add(runInstanceInfoDto);

        // 初始化分页数据
        pageInfo = new PageInfo<>(runInstanceInfoDtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("测试查询实例详情列表")
    void testList() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.selectRunInstanceInfoList(any(), anyInt(), anyInt()))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/info/list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].id").value(1))
                .andExpect(jsonPath("$.data.list[0].envcRunInstanceId").value(100))
                .andExpect(jsonPath("$.data.list[0].envcPlanId").value(200));

        // 验证服务方法调用
        verify(runInstanceInfoService).selectRunInstanceInfoList(any(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("测试查询实例详情详细信息")
    void testGetInfo() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.selectRunInstanceInfoById(anyLong()))
                .thenReturn(runInstanceInfoDto);

        // 执行请求并验证结果
        mockMvc.perform(get("/info/get")
                        .param("id", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.envcRunInstanceId").value(100))
                .andExpect(jsonPath("$.data.envcPlanId").value(200));

        // 验证服务方法调用
        verify(runInstanceInfoService).selectRunInstanceInfoById(1L);
    }

    @Test
    @DisplayName("测试新增实例详情")
    void testSave() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.insertRunInstanceInfo(any()))
                .thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceInfoDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).insertRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试更新实例详情")
    void testUpdate() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.updateRunInstanceInfo(any()))
                .thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceInfoDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).updateRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试删除实例详情")
    void testRemove() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.deleteRunInstanceInfoByIds(any()))
                .thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/remove")
                        .param("ids", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).deleteRunInstanceInfoByIds(any());
    }

    @Test
    @DisplayName("测试新增实例详情-失败")
    void testSave_Fail() throws Exception {
        // 模拟服务方法返回0表示失败
        when(runInstanceInfoService.insertRunInstanceInfo(any()))
                .thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceInfoDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("130200"));

        // 验证服务方法调用
        verify(runInstanceInfoService).insertRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试新增实例详情-系统异常")
    void testSave_SystemException() throws Exception {
        // 模拟服务方法抛出异常
        when(runInstanceInfoService.insertRunInstanceInfo(any()))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行请求并验证结果
        mockMvc.perform(post("/info/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceInfoDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runInstanceInfoService).insertRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试更新实例详情-系统异常")
    void testUpdate_SystemException() throws Exception {
        // 模拟服务方法抛出异常
        doThrow(new RuntimeException("数据库连接异常"))
                .when(runInstanceInfoService).updateRunInstanceInfo(any());

        // 执行请求并验证结果
        mockMvc.perform(post("/info/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceInfoDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runInstanceInfoService).updateRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试删除实例详情-系统异常")
    void testRemove_SystemException() throws Exception {
        // 模拟服务方法抛出异常
        doThrow(new RuntimeException("数据库连接异常"))
                .when(runInstanceInfoService).deleteRunInstanceInfoByIds(any());

        // 执行请求并验证结果
        mockMvc.perform(post("/info/remove")
                        .param("ids", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runInstanceInfoService).deleteRunInstanceInfoByIds(any());
    }

    @Test
    @DisplayName("测试查询实例详情列表-空查询条件")
    void testList_EmptyQuery() throws Exception {
        // 模拟服务方法返回空分页
        PageInfo<RunInstanceInfoDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
        when(runInstanceInfoService.selectRunInstanceInfoList(any(), anyInt(), anyInt()))
                .thenReturn(emptyPageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/info/list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(0));

        // 验证服务方法调用
        verify(runInstanceInfoService).selectRunInstanceInfoList(any(), anyInt(), anyInt());
    }

    @Test
    @DisplayName("测试查询实例详情详细信息-返回null")
    void testGetInfo_ReturnNull() throws Exception {
        // 模拟服务方法返回null
        when(runInstanceInfoService.selectRunInstanceInfoById(anyLong()))
                .thenReturn(null);

        // 执行请求并验证结果
        mockMvc.perform(get("/info/get")
                        .param("id", "999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data").isEmpty());

        // 验证服务方法调用
        verify(runInstanceInfoService).selectRunInstanceInfoById(999L);
    }

    @Test
    @DisplayName("测试新增实例详情-空对象")
    void testSave_EmptyObject() throws Exception {
        // 创建空的RunInstanceInfoDto
        RunInstanceInfoDto emptyDto = new RunInstanceInfoDto();

        // 模拟服务方法
        when(runInstanceInfoService.insertRunInstanceInfo(any()))
                .thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).insertRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试更新实例详情-空对象")
    void testUpdate_EmptyObject() throws Exception {
        // 创建空的RunInstanceInfoDto
        RunInstanceInfoDto emptyDto = new RunInstanceInfoDto();

        // 模拟服务方法
        when(runInstanceInfoService.updateRunInstanceInfo(any()))
                .thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).updateRunInstanceInfo(any());
    }

    @Test
    @DisplayName("测试删除实例详情-空ID数组")
    void testRemove_EmptyIds() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.deleteRunInstanceInfoByIds(any()))
                .thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/remove")
                        .param("ids", "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).deleteRunInstanceInfoByIds(any());
    }

    @Test
    @DisplayName("测试查询实例详情列表-大分页参数")
    void testList_LargePageParams() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.selectRunInstanceInfoList(any(), anyInt(), anyInt()))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/info/list")
                        .param("pageNum", "999")
                        .param("pageSize", "1000")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).selectRunInstanceInfoList(any(), eq(999), eq(1000));
    }

    @Test
    @DisplayName("测试查询实例详情详细信息-无效ID")
    void testGetInfo_InvalidId() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.selectRunInstanceInfoById(anyLong()))
                .thenReturn(null);

        // 执行请求并验证结果
        mockMvc.perform(get("/info/get")
                        .param("id", "-1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).selectRunInstanceInfoById(-1L);
    }

    @Test
    @DisplayName("测试删除实例详情-多个ID")
    void testRemove_MultipleIds() throws Exception {
        // 模拟服务方法
        when(runInstanceInfoService.deleteRunInstanceInfoByIds(any()))
                .thenReturn(3);

        // 执行请求并验证结果
        mockMvc.perform(post("/info/remove")
                        .param("ids", "1", "2", "3")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceInfoService).deleteRunInstanceInfoByIds(any());
    }
} 