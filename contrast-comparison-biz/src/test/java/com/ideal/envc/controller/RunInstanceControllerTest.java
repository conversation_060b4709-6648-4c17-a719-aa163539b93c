package com.ideal.envc.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.RunInstanceDto;
import com.ideal.envc.model.dto.RunInstanceQueryDto;
import com.ideal.envc.service.IRunInstanceService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 实例Controller单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RunInstanceControllerTest {

    private MockMvc mockMvc;

    @Mock
    private IRunInstanceService runInstanceService;

    @InjectMocks
    private RunInstanceController runInstanceController;

    private ObjectMapper objectMapper;
    private RunInstanceDto runInstanceDto;
    private List<RunInstanceDto> runInstanceDtoList;
    private PageInfo<RunInstanceDto> pageInfo;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(runInstanceController).build();
        objectMapper = new ObjectMapper();

        // 初始化测试数据
        runInstanceDto = new RunInstanceDto();
        runInstanceDto.setId(1L);
        runInstanceDto.setEnvcPlanId(100L);
        runInstanceDto.setEnvcTaskId(200L);
        runInstanceDto.setResult(0);
        runInstanceDto.setState(1);
        runInstanceDto.setFrom(2); // 手动触发
        runInstanceDto.setStarterName("admin");
        runInstanceDto.setStarterId(1001L);
        runInstanceDto.setStartTime(new Date());
        runInstanceDto.setEndTime(new Date());
        runInstanceDto.setElapsedTime(5000L);

        // 初始化列表数据
        runInstanceDtoList = new ArrayList<>();
        runInstanceDtoList.add(runInstanceDto);

        // 初始化分页数据
        pageInfo = new PageInfo<>(runInstanceDtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("测试查询实例列表")
    void testList() throws Exception {
        // 准备测试数据
        TableQueryDto<RunInstanceQueryDto> tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 模拟服务方法，使用更灵活的参数匹配
        when(runInstanceService.selectRunInstanceList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/instance/list")
                        .param("pageNum", "1")
                        .param("pageSize", "10")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.list[0].id").value(1))
                .andExpect(jsonPath("$.data.list[0].envcPlanId").value(100));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runInstanceService).selectRunInstanceList(any(), any(Integer.class), any(Integer.class));
    }

    @Test
    @DisplayName("测试查询实例详细信息")
    void testGetInfo() throws Exception {
        // 模拟服务方法，使用更灵活的参数匹配
        when(runInstanceService.selectRunInstanceById(any(Long.class))).thenReturn(runInstanceDto);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/instance/get")
                        .param("id", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.envcPlanId").value(100));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runInstanceService).selectRunInstanceById(any(Long.class));
    }

    @Test
    @DisplayName("测试新增保存实例-成功")
    void testSave_Success() throws Exception {
        // 模拟服务方法返回值大于0表示成功
        when(runInstanceService.insertRunInstance(any(RunInstanceDto.class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).insertRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试新增保存实例-失败")
    void testSave_Fail() throws Exception {
        // 模拟服务方法返回值为0表示失败
        when(runInstanceService.insertRunInstance(any(RunInstanceDto.class))).thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("130200"));

        // 验证服务方法调用
        verify(runInstanceService).insertRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试修改保存实例")
    void testUpdate() throws Exception {
        // 模拟服务方法
        doReturn(1).when(runInstanceService).updateRunInstance(any(RunInstanceDto.class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).updateRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试删除实例")
    void testRemove() throws Exception {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟服务方法，使用更灵活的参数匹配
        when(runInstanceService.deleteRunInstanceByIds(any(Long[].class))).thenReturn(2);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/remove")
                        .param("ids", "1", "2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用，使用更灵活的参数匹配
        verify(runInstanceService).deleteRunInstanceByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试新增保存实例-系统异常")
    void testSave_SystemException() throws Exception {
        // 模拟服务方法抛出异常
        when(runInstanceService.insertRunInstance(any(RunInstanceDto.class)))
                .thenThrow(new RuntimeException("数据库连接异常"));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runInstanceService).insertRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试修改保存实例-系统异常")
    void testUpdate_SystemException() throws Exception {
        // 模拟服务方法抛出异常
        doThrow(new RuntimeException("数据库连接异常"))
                .when(runInstanceService).updateRunInstance(any(RunInstanceDto.class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(runInstanceDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runInstanceService).updateRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试删除实例-系统异常")
    void testRemove_SystemException() throws Exception {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // 模拟服务方法抛出异常
        doThrow(new RuntimeException("数据库连接异常"))
                .when(runInstanceService).deleteRunInstanceByIds(any(Long[].class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/remove")
                        .param("ids", "1", "2")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("139900"));

        // 验证服务方法调用
        verify(runInstanceService).deleteRunInstanceByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试查询实例列表-空查询条件")
    void testList_EmptyQuery() throws Exception {
        // 模拟服务方法
        when(runInstanceService.selectRunInstanceList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(new PageInfo<>(new ArrayList<>()));

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/instance/list")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data.total").value(0));

        // 验证服务方法调用
        verify(runInstanceService).selectRunInstanceList(any(), any(Integer.class), any(Integer.class));
    }

    @Test
    @DisplayName("测试查询实例详细信息-返回null")
    void testGetInfo_ReturnNull() throws Exception {
        // 模拟服务方法返回null
        when(runInstanceService.selectRunInstanceById(any(Long.class))).thenReturn(null);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/instance/get")
                        .param("id", "999")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"))
                .andExpect(jsonPath("$.data").isEmpty());

        // 验证服务方法调用
        verify(runInstanceService).selectRunInstanceById(999L);
    }

    @Test
    @DisplayName("测试新增保存实例-空对象")
    void testSave_EmptyObject() throws Exception {
        // 创建空的RunInstanceDto
        RunInstanceDto emptyDto = new RunInstanceDto();

        // 模拟服务方法
        when(runInstanceService.insertRunInstance(any(RunInstanceDto.class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/save")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).insertRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试修改保存实例-空对象")
    void testUpdate_EmptyObject() throws Exception {
        // 创建空的RunInstanceDto
        RunInstanceDto emptyDto = new RunInstanceDto();

        // 模拟服务方法
        doReturn(1).when(runInstanceService).updateRunInstance(any(RunInstanceDto.class));

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/update")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(emptyDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).updateRunInstance(any(RunInstanceDto.class));
    }

    @Test
    @DisplayName("测试删除实例-空ID数组")
    void testRemove_EmptyIds() throws Exception {
        // 模拟服务方法
        when(runInstanceService.deleteRunInstanceByIds(any(Long[].class))).thenReturn(0);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/remove")
                        .param("ids", "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).deleteRunInstanceByIds(any(Long[].class));
    }

    @Test
    @DisplayName("测试查询实例列表-大分页参数")
    void testList_LargePageParams() throws Exception {
        // 模拟服务方法
        when(runInstanceService.selectRunInstanceList(any(), any(Integer.class), any(Integer.class)))
                .thenReturn(pageInfo);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/instance/list")
                        .param("pageNum", "999")
                        .param("pageSize", "1000")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).selectRunInstanceList(any(), eq(999), eq(1000));
    }

    @Test
    @DisplayName("测试查询实例详细信息-无效ID")
    void testGetInfo_InvalidId() throws Exception {
        // 模拟服务方法
        when(runInstanceService.selectRunInstanceById(any(Long.class))).thenReturn(null);

        // 执行请求并验证结果
        mockMvc.perform(get("/envc/instance/get")
                        .param("id", "-1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).selectRunInstanceById(-1L);
    }

    @Test
    @DisplayName("测试删除实例-单个ID")
    void testRemove_SingleId() throws Exception {
        // 模拟服务方法
        when(runInstanceService.deleteRunInstanceByIds(any(Long[].class))).thenReturn(1);

        // 执行请求并验证结果
        mockMvc.perform(post("/envc/instance/remove")
                        .param("ids", "1")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value("10000"));

        // 验证服务方法调用
        verify(runInstanceService).deleteRunInstanceByIds(any(Long[].class));
    }
} 