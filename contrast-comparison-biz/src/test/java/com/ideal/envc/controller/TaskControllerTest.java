package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.model.dto.TaskCronUpdateDto;
import com.ideal.envc.model.dto.TaskDto;
import com.ideal.envc.model.dto.TaskOperateResultDto;
import com.ideal.envc.model.dto.TaskPlanListDto;
import com.ideal.envc.model.dto.TaskQueryDto;
import com.ideal.envc.model.dto.TaskStartOrStopDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.service.ITaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import com.ideal.envc.model.enums.ResponseCodeEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 任务管理Controller的单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class TaskControllerTest {

    @Mock
    private ITaskService taskService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private TaskController taskController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;
    private TaskDto taskDto;
    private TaskQueryDto queryDto;
    private PageInfo<TaskDto> pageInfo;
    private TableQueryDto<TaskQueryDto> tableQueryDto;
    private UserDto userDto;
    private TaskCronUpdateDto taskCronUpdateDto;
    private TaskStartOrStopDto taskStartOrStopDto;
    private PageInfo<TaskPlanListDto> taskPlanPageInfo;
    private TaskOperateResultDto taskOperateResultDto;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(taskController).build();
        objectMapper = new ObjectMapper();
        
        // 初始化基础测试数据
        taskDto = new TaskDto();
        taskDto.setId(1L);
        taskDto.setEnvcPlanId(100L);
        taskDto.setCron("0 0 12 * * ?");

        queryDto = new TaskQueryDto();
        queryDto.setEnvcPlanId(100L);

        List<TaskDto> list = new ArrayList<>();
        list.add(taskDto);
        pageInfo = new PageInfo<>(list);

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("测试用户");

        taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 12 * * ?");

        taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));

        List<TaskPlanListDto> taskPlanList = new ArrayList<>();
        TaskPlanListDto taskPlanListDto = new TaskPlanListDto();
        taskPlanListDto.setEnvcPlanId(100L);
        taskPlanListDto.setPlanName("测试方案");
        taskPlanList.add(taskPlanListDto);
        taskPlanPageInfo = new PageInfo<>(taskPlanList);

        taskOperateResultDto = new TaskOperateResultDto();
        taskOperateResultDto.setAllSuccess(true);
        taskOperateResultDto.setSuccessTaskIds(Arrays.asList(1L, 2L));

        when(userinfoComponent.getUser()).thenReturn(userDto);
    }

    @Test
    @DisplayName("测试查询任务列表")
    void testList() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(pageInfo).when(taskService).selectTaskList(
                any(TaskQueryDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<TaskDto>> result = taskController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        
        // 验证方法调用
        verify(taskService).selectTaskList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询任务列表时抛出业务异常")
    void testListWithBusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("查询任务列表失败")).when(taskService).selectTaskList(
                any(TaskQueryDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<TaskDto>> result = taskController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals("查询任务列表失败", result.getMessage());
        
        // 验证方法调用
        verify(taskService).selectTaskList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询任务列表时抛出系统异常")
    void testListWithSystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("系统异常")).when(taskService).selectTaskList(
                any(TaskQueryDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<TaskDto>> result = taskController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(taskService).selectTaskList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试查询任务详细信息")
    void testGetTaskInfoInfo() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(taskDto).when(taskService).selectTaskDetailById(anyLong());

        // 执行方法
        R<TaskDto> result = taskController.getTaskInfoInfo(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());

        // 验证方法调用
        verify(taskService).selectTaskDetailById(1L);
    }

    @Test
    @DisplayName("测试查询任务详情时抛出业务异常")
    void testGetTaskInfoWithBusinessException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("查询任务详情失败")).when(taskService).selectTaskDetailById(anyLong());

        // 执行方法
        R<TaskDto> result = taskController.getTaskInfoInfo(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals("查询任务详情失败", result.getMessage());
        
        // 验证方法调用
        verify(taskService).selectTaskDetailById(1L);
    }

    @Test
    @DisplayName("测试查询任务详情时抛出系统异常")
    void testGetTaskInfoWithSystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("系统异常")).when(taskService).selectTaskDetailById(anyLong());

        // 执行方法
        R<TaskDto> result = taskController.getTaskInfoInfo(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(taskService).selectTaskDetailById(1L);
    }

    @Test
    @DisplayName("测试新增任务成功")
    void testSaveSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(1).when(taskService).insertTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.save(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(taskService).insertTask(taskDto);
    }

    @Test
    @DisplayName("测试新增任务失败")
    void testSaveFail() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(0).when(taskService).insertTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.save(taskDto);

        // 验证结果
        assertNotNull(result);
        // 验证方法调用
        verify(taskService).insertTask(taskDto);
    }

    @Test
    @DisplayName("测试新增任务异常-已存在相同cron表达式")
    void testSaveWithDuplicateCronException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入"))
            .when(taskService).insertTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.save(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("13001", result.getCode());

        // 验证方法调用
        verify(taskService).insertTask(taskDto);
    }

    @Test
    @DisplayName("测试新增任务异常-参数验证失败")
    void testSaveWithParameterValidationException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("任务方案ID不能为空"))
            .when(taskService).insertTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.save(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("13002", result.getCode());
        assertEquals("任务方案ID不能为空", result.getMessage());

        // 验证方法调用
        verify(taskService).insertTask(taskDto);
    }

    @Test
    @DisplayName("测试新增任务异常-系统异常")
    void testSaveWithSystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("数据库连接失败"))
            .when(taskService).insertTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.save(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("00000", result.getCode());
        assertTrue(result.getMessage().contains("系统异常"));

        // 验证方法调用
        verify(taskService).insertTask(taskDto);
    }

    @Test
    @DisplayName("测试修改任务成功")
    void testUpdateSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为 - updateTask返回int，表示更新的行数
        when(taskService.updateTask(any(TaskDto.class))).thenReturn(1);

        // 执行方法
        R<Void> result = taskController.update(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证方法调用
        verify(taskService).updateTask(taskDto);
    }

    @Test
    @DisplayName("测试修改任务异常-已存在相同cron表达式")
    void testUpdateWithDuplicateCronException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入"))
            .when(taskService).updateTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.update(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("13001", result.getCode());

        // 验证方法调用
        verify(taskService).updateTask(taskDto);
    }

    @Test
    @DisplayName("测试修改任务异常-参数验证失败")
    void testUpdateWithParameterValidationException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("任务ID不能为空"))
            .when(taskService).updateTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.update(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("13003", result.getCode());
        assertEquals("任务ID不能为空", result.getMessage());

        // 验证方法调用
        verify(taskService).updateTask(taskDto);
    }

    @Test
    @DisplayName("测试修改任务异常-系统异常")
    void testUpdateWithSystemException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doThrow(new RuntimeException("数据库更新失败"))
            .when(taskService).updateTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.update(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("00000", result.getCode());
        assertTrue(result.getMessage().contains("系统异常"));

        // 验证方法调用
        verify(taskService).updateTask(taskDto);
    }

    @Test
    @DisplayName("测试新增任务并立即启动成功")
    void testCreateTaskSuccess() throws ContrastBusinessException, ScheduleJobOperateException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(1L).when(taskService).createTask(any(TaskDto.class), any(UserDto.class));

        // 执行方法
        R<Long> result = taskController.createTask(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertEquals(1L, result.getData());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).createTask(taskDto, userDto);
    }

    @Test
    @DisplayName("测试新增任务并立即启动失败-已存在相同cron表达式")
    void testCreateTaskWithDuplicateCronException() throws ContrastBusinessException, ScheduleJobOperateException {
        // 设置 Mock 行为
        doThrow(new ContrastBusinessException("同一方案下已存在相同cron表达式的任务")).when(taskService).createTask(any(TaskDto.class), any(UserDto.class));

        // 执行方法
        R<Long> result = taskController.createTask(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode(), result.getCode());
        assertEquals("同一方案下已存在相同cron表达式的任务", result.getMessage());
        
        // 验证方法调用
        verify(taskService).createTask(taskDto, userinfoComponent.getUser());
    }

    @Test
    @DisplayName("测试新增任务并立即启动时抛出系统异常")
    void testCreateTaskWithSystemException() throws ContrastBusinessException, ScheduleJobOperateException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new RuntimeException("系统异常")).when(taskService).createTask(any(TaskDto.class), any(UserDto.class));

        // 执行方法
        R<Long> result = taskController.createTask(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).createTask(taskDto, userDto);
    }

    @Test
    @DisplayName("测试修改保存任务")
    void testUpdate() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(1).when(taskService).updateTask(any(TaskDto.class));

        // 执行方法
        R<Void> result = taskController.update(taskDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(taskService).updateTask(taskDto);
    }

    @Test
    @DisplayName("测试删除任务")
    void testRemove() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(1).when(taskService).deleteTaskByIds(any(Long[].class));

        // 要删除的ID数组
        Long[] ids = new Long[]{1L, 2L};

        // 执行方法
        R<Void> result = taskController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(taskService).deleteTaskByIds(ids);
    }

    @Test
    @DisplayName("测试更新任务周期表达式成功")
    void testUpdateCronSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(1).when(taskService).updateTaskCron(any(TaskCronUpdateDto.class), any(UserDto.class));

        // 执行方法
        R<Void> result = taskController.updateCron(taskCronUpdateDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).updateTaskCron(taskCronUpdateDto, userDto);
    }

    @Test
    @DisplayName("当更新cron表达式时遇到重复cron异常")
    void testUpdateCronWithDuplicateCronException() throws Exception {
        // Given
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 12 * * ?");

        when(taskService.updateTaskCron(any(TaskCronUpdateDto.class), any(UserDto.class)))
            .thenThrow(new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入"));

        // When & Then
        mockMvc.perform(post("/task/updateCron")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(taskCronUpdateDto)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode()))
            .andExpect(jsonPath("$.message").value("同一方案下已存在相同cron表达式的任务，请重新输入"));
    }

    @Test
    @DisplayName("当更新cron表达式时参数不完整")
    void testUpdateCronWithIncompleteParamsException() throws Exception {
        // Given
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 12 * * ?");

        when(taskService.updateTaskCron(any(TaskCronUpdateDto.class), any(UserDto.class)))
            .thenThrow(new ContrastBusinessException("参数不完整"));

        // When & Then
        mockMvc.perform(post("/task/updateCron")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(taskCronUpdateDto)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode()))
            .andExpect(jsonPath("$.message").value("参数不完整"));
    }

    @Test
    @DisplayName("当更新cron表达式时任务不存在")
    void testUpdateCronWithTaskNotExistException() throws Exception {
        // Given
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 12 * * ?");

        when(taskService.updateTaskCron(any(TaskCronUpdateDto.class), any(UserDto.class)))
            .thenThrow(new ContrastBusinessException("任务不存在"));

        // When & Then
        mockMvc.perform(post("/task/updateCron")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(taskCronUpdateDto)))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.code").value(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode()))
            .andExpect(jsonPath("$.message").value("任务不存在"));
    }

    @Test
    @DisplayName("测试查询任务方案列表")
    void testTaskPlanList() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(taskPlanPageInfo).when(taskService).selectTaskPlanList(
                any(TaskQueryDto.class), anyInt(), anyInt());

        // 执行方法
        R<PageInfo<TaskPlanListDto>> result = taskController.taskPlanList(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getList().size());
        
        // 验证方法调用
        verify(taskService).selectTaskPlanList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试操作任务（启动或停止）成功")
    void testOperateTasksSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为，使用 doNothing() 处理 void 方法
        doReturn(userDto).when(userinfoComponent).getUser();
        doNothing().when(taskService).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));

        // 执行方法
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());

        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).operateTasks(taskStartOrStopDto, userDto);
    }

    @Test
    @DisplayName("测试操作任务（启动或停止）失败")
    void testOperateTasksFail() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new RuntimeException("操作失败")).when(taskService).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));

        // 执行方法
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("139900", result.getCode());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).operateTasks(taskStartOrStopDto, userDto);
    }

    @Test
    @DisplayName("测试删除定时任务并删除任务成功")
    void testRemoveScheduleJobSuccess() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(taskOperateResultDto).when(taskService).removeScheduleJob(any(), any(UserDto.class));

        // 执行方法
        R<TaskOperateResultDto> result = taskController.removeScheduleJob(Arrays.asList(1L, 2L));

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(true, result.getData().isAllSuccess());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).removeScheduleJob(Arrays.asList(1L, 2L), userDto);
    }

    @Test
    @DisplayName("测试删除定时任务并删除任务失败-空任务ID列表")
    void testRemoveScheduleJobWithEmptyTaskIds() throws ContrastBusinessException {
        // 执行方法
        R<TaskOperateResultDto> result = taskController.removeScheduleJob(new ArrayList<>());

        // 验证结果
        assertNotNull(result);
        assertEquals("13001", result.getCode());
    }

    @Test
    @DisplayName("测试删除定时任务并删除任务部分成功")
    void testRemoveScheduleJobPartialSuccess() throws ContrastBusinessException {
        // 设置部分成功的结果
        TaskOperateResultDto partialSuccessResult = new TaskOperateResultDto();
        partialSuccessResult.setAllSuccess(false);
        partialSuccessResult.setSuccessTaskIds(Arrays.asList(1L));
        partialSuccessResult.setFailedTaskIds(Arrays.asList(2L));
        partialSuccessResult.setFailReason("任务2删除失败");

        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doReturn(partialSuccessResult).when(taskService).removeScheduleJob(any(), any(UserDto.class));

        // 执行方法
        R<TaskOperateResultDto> result = taskController.removeScheduleJob(Arrays.asList(1L, 2L));

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(false, result.getData().isAllSuccess());
        assertEquals(1, result.getData().getSuccessTaskIds().size());
        assertEquals(1, result.getData().getFailedTaskIds().size());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).removeScheduleJob(Arrays.asList(1L, 2L), userDto);
    }

    @Test
    @DisplayName("测试删除定时任务并删除任务异常")
    void testRemoveScheduleJobException() throws ContrastBusinessException {
        // 设置 Mock 行为
        doReturn(userDto).when(userinfoComponent).getUser();
        doThrow(new RuntimeException("操作异常")).when(taskService).removeScheduleJob(any(), any(UserDto.class));

        // 执行方法
        R<TaskOperateResultDto> result = taskController.removeScheduleJob(Arrays.asList(1L, 2L));

        // 验证结果
        assertNotNull(result);
        assertEquals("00000", result.getCode());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).removeScheduleJob(Arrays.asList(1L, 2L), userDto);
    }

    @Test
    @DisplayName("测试操作任务时用户未登录场景")
    void testOperateTasksWithNoUser() throws ContrastBusinessException {
        // 准备测试数据
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));
        
        // Mock依赖 - 用户未登录
        when(userinfoComponent.getUser()).thenReturn(null);
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.UNAUTHORIZED_ACCESS.getCode(), result.getCode());
        assertEquals("用户未登录或登录已过期", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService, never()).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试操作任务时用户ID为空场景")
    void testOperateTasksWithNullUserId() throws ContrastBusinessException {
        // 准备测试数据
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));
        
        // Mock依赖 - 用户ID为空
        UserDto userWithNullId = new UserDto();
        userWithNullId.setId(null);
        when(userinfoComponent.getUser()).thenReturn(userWithNullId);
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.UNAUTHORIZED_ACCESS.getCode(), result.getCode());
        assertEquals("用户未登录或登录已过期", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService, never()).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试操作任务时操作类型不合法场景")
    void testOperateTasksWithInvalidOperateType() throws ContrastBusinessException {
        // 准备测试数据
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(999); // 使用不合法的操作类型
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));
        
        // Mock依赖
        when(userinfoComponent.getUser()).thenReturn(userDto);
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.PARAM_FORMAT_ERROR.getCode(), result.getCode());
        assertEquals("不支持的操作类型", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService, never()).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试操作任务时任务ID列表为空场景")
    void testOperateTasksWithEmptyTaskIds() throws ContrastBusinessException {
        // 准备测试数据 - 空任务ID列表
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Collections.emptyList());
        
        // Mock依赖
        when(userinfoComponent.getUser()).thenReturn(userDto);
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.REQUIRED_FIELD_MISSING.getCode(), result.getCode());
        assertEquals("任务ID列表不能为空", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService, never()).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试操作任务时任务ID列表为null场景")
    void testOperateTasksWithNullTaskIds() throws ContrastBusinessException {
        // 准备测试数据 - null任务ID列表
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(null);
        
        // Mock依赖
        when(userinfoComponent.getUser()).thenReturn(userDto);
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.REQUIRED_FIELD_MISSING.getCode(), result.getCode());
        assertEquals("任务ID列表不能为空", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService, never()).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试操作任务时抛出业务异常")
    void testOperateTasksWithBusinessException() throws ContrastBusinessException {
        // 准备测试数据
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));
        
        // Mock依赖
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new ContrastBusinessException("操作任务失败")).when(taskService).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.UPDATE_FAIL.getCode(), result.getCode());
        assertEquals("操作任务失败", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).operateTasks(eq(taskStartOrStopDto), eq(userDto));
    }

    @Test
    @DisplayName("测试操作任务时抛出包含'已经处于'的运行时异常")
    void testOperateTasksWithTaskAlreadyInStateException() throws ContrastBusinessException {
        // 准备测试数据
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));
        
        // Mock依赖
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new RuntimeException("任务已经处于启动状态")).when(taskService).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.UPDATE_FAIL.getCode(), result.getCode());
        assertEquals("任务已经处于启动状态", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).operateTasks(eq(taskStartOrStopDto), eq(userDto));
    }

    @Test
    @DisplayName("测试操作任务时抛出系统异常")
    void testOperateTasksWithSystemException() throws ContrastBusinessException {
        // 准备测试数据
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setOperateType(1); // 1表示启动
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L, 2L));
        
        // Mock依赖
        when(userinfoComponent.getUser()).thenReturn(userDto);
        doThrow(new RuntimeException("其他系统异常")).when(taskService).operateTasks(any(TaskStartOrStopDto.class), any(UserDto.class));
        
        // 执行测试
        R<Void> result = taskController.operateTasks(taskStartOrStopDto);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals("系统异常，请稍后重试", result.getMessage());
        
        // 验证方法调用
        verify(userinfoComponent).getUser();
        verify(taskService).operateTasks(eq(taskStartOrStopDto), eq(userDto));
    }
} 