package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IStartContrastService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 启动比对控制器测试类
 */
@ExtendWith(MockitoExtension.class)
public class StartContrastControllerTest {

    @Mock
    private IStartContrastService startContrastService;

    @Mock
    private UserinfoComponent userinfoComponent;

    @InjectMocks
    private StartContrastController startContrastController;

    private PlanLevelStartDto planLevelStartDto;
    private SystemLevelStartDto systemLevelStartDto;
    private NodeLevelStartDto nodeLevelStartDto;
    private RuleLevelStartDto ruleLevelStartDto;
    private TaskLevelStartDto taskLevelStartDto;
    private UserDto userDto;
    private StartResult startResult;
    private StartResult failStartResult;

    @BeforeEach
    public void setUp() {
        // 初始化测试数据
        planLevelStartDto = new PlanLevelStartDto();
        planLevelStartDto.setPlanIds(java.util.Arrays.asList(1L, 2L));
        planLevelStartDto.setSourceCenterId(1L);
        planLevelStartDto.setTargetCenterId(2L);

        systemLevelStartDto = new SystemLevelStartDto();
        systemLevelStartDto.setSystemIds(java.util.Arrays.asList(1L, 2L));
        systemLevelStartDto.setSourceCenterId(1L);
        systemLevelStartDto.setTargetCenterId(2L);

        nodeLevelStartDto = new NodeLevelStartDto();
        nodeLevelStartDto.setNodeIds(java.util.Arrays.asList(1L, 2L));
        nodeLevelStartDto.setSourceCenterId(1L);
        nodeLevelStartDto.setTargetCenterId(2L);

        ruleLevelStartDto = new RuleLevelStartDto();
        ruleLevelStartDto.setRuleIds(java.util.Arrays.asList(1L, 2L));
        ruleLevelStartDto.setSourceCenterId(1L);
        ruleLevelStartDto.setTargetCenterId(2L);

        taskLevelStartDto = new TaskLevelStartDto();
        taskLevelStartDto.setTaskIds(java.util.Arrays.asList(1L, 2L));
        taskLevelStartDto.setSourceCenterId(1L);
        taskLevelStartDto.setTargetCenterId(2L);

        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("testUser");
        userDto.setFullName("Test User");

        startResult = new StartResult();
        startResult.setSuccess(true);
        startResult.setMessage("操作成功");
        startResult.addInstanceId(1L);

        failStartResult = new StartResult();
        failStartResult.setSuccess(false);
        failStartResult.setMessage("操作失败");
        failStartResult.addFailedMessageId("失败的消息ID");

        // 设置userinfoComponent的行为
        when(userinfoComponent.getUser()).thenReturn(userDto);
    }

    @Test
    @DisplayName("测试按照方案级别启动 - 成功情况")
    public void testStartByPlansSuccess() {
        // 设置mock行为
        when(startContrastService.startByPlans(any(PlanLevelStartDto.class), any(UserDto.class)))
                .thenReturn(startResult);

        // 执行测试
        R<StartResult> result = startContrastController.startByPlans(planLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(true, result.getData().isSuccess());
        assertEquals("操作成功", result.getData().getMessage());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByPlans(any(PlanLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照方案级别启动 - 异常情况")
    public void testStartByPlansFail() {
        // 设置mock行为 - 抛出异常
        when(startContrastService.startByPlans(any(PlanLevelStartDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<StartResult> result = startContrastController.startByPlans(planLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.PLAN_START_FAIL.getCode(), result.getCode());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByPlans(any(PlanLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照系统级别启动 - 成功情况")
    public void testStartBySystemsSuccess() {
        // 设置mock行为
        when(startContrastService.startBySystems(any(SystemLevelStartDto.class), any(UserDto.class)))
                .thenReturn(startResult);

        // 执行测试
        R<StartResult> result = startContrastController.startBySystems(systemLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(true, result.getData().isSuccess());
        assertEquals("操作成功", result.getData().getMessage());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startBySystems(any(SystemLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照系统级别启动 - 异常情况")
    public void testStartBySystemsFail() {
        // 设置mock行为 - 抛出异常
        when(startContrastService.startBySystems(any(SystemLevelStartDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<StartResult> result = startContrastController.startBySystems(systemLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_START_FAIL.getCode(), result.getCode());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startBySystems(any(SystemLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照节点级别启动 - 成功情况")
    public void testStartByNodesSuccess() {
        // 设置mock行为
        when(startContrastService.startByNodes(any(NodeLevelStartDto.class), any(UserDto.class)))
                .thenReturn(startResult);

        // 执行测试
        R<StartResult> result = startContrastController.startByNodes(nodeLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(true, result.getData().isSuccess());
        assertEquals("操作成功", result.getData().getMessage());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByNodes(any(NodeLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照节点级别启动 - 异常情况")
    public void testStartByNodesFail() {
        // 设置mock行为 - 抛出异常
        when(startContrastService.startByNodes(any(NodeLevelStartDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<StartResult> result = startContrastController.startByNodes(nodeLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.NODE_START_FAIL.getCode(), result.getCode());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByNodes(any(NodeLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照规则级别启动 - 成功情况")
    public void testStartByRulesSuccess() {
        // 设置mock行为
        when(startContrastService.startByRules(any(RuleLevelStartDto.class), any(UserDto.class)))
                .thenReturn(startResult);

        // 执行测试
        R<StartResult> result = startContrastController.startByRules(ruleLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(true, result.getData().isSuccess());
        assertEquals("操作成功", result.getData().getMessage());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByRules(any(RuleLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照规则级别启动 - 异常情况")
    public void testStartByRulesFail() {
        // 设置mock行为 - 抛出异常
        when(startContrastService.startByRules(any(RuleLevelStartDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<StartResult> result = startContrastController.startByRules(ruleLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.RULE_START_FAIL.getCode(), result.getCode());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByRules(any(RuleLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照任务级别启动 - 成功情况")
    public void testStartByTasksSuccess() {
        // 设置mock行为
        when(startContrastService.startByTasks(any(TaskLevelStartDto.class), any(UserDto.class)))
                .thenReturn(startResult);

        // 执行测试
        R<StartResult> result = startContrastController.startByTasks(taskLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertNotNull(result.getData());
        assertEquals(true, result.getData().isSuccess());
        assertEquals("操作成功", result.getData().getMessage());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByTasks(any(TaskLevelStartDto.class), any(UserDto.class));
    }

    @Test
    @DisplayName("测试按照任务级别启动 - 异常情况")
    public void testStartByTasksFail() {
        // 设置mock行为 - 抛出异常
        when(startContrastService.startByTasks(any(TaskLevelStartDto.class), any(UserDto.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 执行测试
        R<StartResult> result = startContrastController.startByTasks(taskLevelStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.TASK_START_FAIL.getCode(), result.getCode());

        // 验证服务调用
        verify(userinfoComponent, times(1)).getUser();
        verify(startContrastService, times(1)).startByTasks(any(TaskLevelStartDto.class), any(UserDto.class));
    }
} 