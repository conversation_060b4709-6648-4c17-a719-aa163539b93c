package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.PlanDto;
import com.ideal.envc.model.dto.PlanQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.IPlanService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PlanController单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("方案管理Controller测试")
class PlanControllerTest {

    @Mock
    private IPlanService planService;

    @Mock
    private UserinfoComponent userinfoComponent;

    private PlanController planController;

    private TableQueryDto<PlanQueryDto> tableQueryDto;
    private PlanQueryDto planQueryDto;
    private PlanDto planDto;
    private UserDto userDto;
    private PageInfo<PlanDto> pageInfo;

    @BeforeEach
    void setUp() {
        planController = new PlanController(planService, userinfoComponent);

        // 初始化测试数据
        planQueryDto = new PlanQueryDto();
        planQueryDto.setName("测试方案");

        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(planQueryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        planDto = new PlanDto();
        planDto.setId(1L);
        planDto.setName("测试方案");
        planDto.setPlanDesc("测试方案描述");

        userDto = new UserDto();
        userDto.setLoginName("testUser");
        userDto.setFullName("测试用户");

        List<PlanDto> planList = new ArrayList<>();
        planList.add(planDto);
        pageInfo = new PageInfo<>(planList);
        pageInfo.setTotal(1);
    }

    @Test
    @DisplayName("查询方案列表-成功")
    void testList_Success() throws Exception {
        // given
        when(planService.selectPlanList(any(PlanQueryDto.class), eq(1), eq(10)))
                .thenReturn(pageInfo);

        // when
        R<PageInfo<PlanDto>> result = planController.list(tableQueryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());

        verify(planService).selectPlanList(any(PlanQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询方案列表-异常")
    void testList_Exception() throws Exception {
        // given
        when(planService.selectPlanList(any(PlanQueryDto.class), eq(1), eq(10)))
                .thenThrow(new RuntimeException("数据库异常"));

        // when
        R<PageInfo<PlanDto>> result = planController.list(tableQueryDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(planService).selectPlanList(any(PlanQueryDto.class), eq(1), eq(10));
    }

    @Test
    @DisplayName("查询方案详情-成功")
    void testGetAgentInfoInfo_Success() throws Exception {
        // given
        Long id = 1L;
        when(planService.selectPlanById(id)).thenReturn(planDto);

        // when
        R<PlanDto> result = planController.getAgentInfoInfo(id);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNotNull(result.getData());
        assertEquals(planDto.getId(), result.getData().getId());

        verify(planService).selectPlanById(id);
    }

    @Test
    @DisplayName("查询方案详情-数据不存在")
    void testGetAgentInfoInfo_DataNotFound() throws Exception {
        // given
        Long id = 999L;
        when(planService.selectPlanById(id)).thenReturn(null);

        // when
        R<PlanDto> result = planController.getAgentInfoInfo(id);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.DATA_NOT_FOUND.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(planService).selectPlanById(id);
    }

    @Test
    @DisplayName("查询方案详情-异常")
    void testGetAgentInfoInfo_Exception() throws Exception {
        // given
        Long id = 1L;
        when(planService.selectPlanById(id)).thenThrow(new RuntimeException("数据库异常"));

        // when
        R<PlanDto> result = planController.getAgentInfoInfo(id);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.QUERY_FAIL.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(planService).selectPlanById(id);
    }

    @Test
    @DisplayName("新增方案-成功")
    void testSave_Success() throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.insertPlan(planDto, userDto)).thenReturn(1);

        // when
        R<Void> result = planController.save(planDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).insertPlan(planDto, userDto);
    }

    @Test
    @DisplayName("新增方案-插入失败")
    void testSave_InsertFail() throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.insertPlan(planDto, userDto)).thenReturn(0);

        // when
        R<Void> result = planController.save(planDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.ADD_FAIL.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.ADD_FAIL.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).insertPlan(planDto, userDto);
    }

    @ParameterizedTest
    @MethodSource("provideSaveBusinessExceptionScenarios")
    @DisplayName("新增方案-业务异常")
    void testSave_BusinessException(String exceptionMessage, String expectedCode) throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.insertPlan(planDto, userDto))
                .thenThrow(new ContrastBusinessException(exceptionMessage));

        // when
        R<Void> result = planController.save(planDto);

        // then
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        assertEquals(exceptionMessage, result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).insertPlan(planDto, userDto);
    }

    static Stream<Arguments> provideSaveBusinessExceptionScenarios() {
        return Stream.of(
                Arguments.of("方案名称已存在", ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode()),
                Arguments.of("方案名称不能为空", ResponseCodeEnum.ADD_PARAM_ERROR.getCode()),
                Arguments.of("其他业务异常", ResponseCodeEnum.ADD_FAIL.getCode())
        );
    }

    @Test
    @DisplayName("新增方案-系统异常")
    void testSave_SystemException() throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.insertPlan(planDto, userDto))
                .thenThrow(new RuntimeException("系统异常"));

        // when
        R<Void> result = planController.save(planDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).insertPlan(planDto, userDto);
    }

    @Test
    @DisplayName("修改方案-成功")
    void testUpdate_Success() throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.updatePlan(planDto, userDto)).thenReturn(1);

        // when
        R<Void> result = planController.update(planDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).updatePlan(planDto, userDto);
    }

    @ParameterizedTest
    @MethodSource("provideUpdateBusinessExceptionScenarios")
    @DisplayName("修改方案-业务异常")
    void testUpdate_BusinessException(String exceptionMessage, String expectedCode) throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.updatePlan(planDto, userDto))
                .thenThrow(new ContrastBusinessException(exceptionMessage));

        // when
        R<Void> result = planController.update(planDto);

        // then
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        assertEquals(exceptionMessage, result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).updatePlan(planDto, userDto);
    }

    static Stream<Arguments> provideUpdateBusinessExceptionScenarios() {
        return Stream.of(
                Arguments.of("方案不存在", ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode()),
                Arguments.of("方案名称已存在", ResponseCodeEnum.DATA_ALREADY_EXISTS.getCode()),
                Arguments.of("方案名称不能为空", ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode()),
                Arguments.of("其他业务异常", ResponseCodeEnum.UPDATE_FAIL.getCode())
        );
    }

    @Test
    @DisplayName("修改方案-系统异常")
    void testUpdate_SystemException() throws Exception {
        // given
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.updatePlan(planDto, userDto))
                .thenThrow(new RuntimeException("系统异常"));

        // when
        R<Void> result = planController.update(planDto);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).updatePlan(planDto, userDto);
    }

    @Test
    @DisplayName("删除方案-成功")
    void testRemove_Success() throws Exception {
        // given
        Long[] ids = {1L, 2L};
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.deletePlanByIds(ids, userDto)).thenReturn(2);

        // when
        R<Void> result = planController.remove(ids);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SUCCESS.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SUCCESS.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).deletePlanByIds(ids, userDto);
    }

    @ParameterizedTest
    @MethodSource("provideRemoveBusinessExceptionScenarios")
    @DisplayName("删除方案-业务异常")
    void testRemove_BusinessException(String exceptionMessage, String expectedCode) throws Exception {
        // given
        Long[] ids = {1L, 2L};
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.deletePlanByIds(ids, userDto))
                .thenThrow(new ContrastBusinessException(exceptionMessage));

        // when
        R<Void> result = planController.remove(ids);

        // then
        assertNotNull(result);
        assertEquals(expectedCode, result.getCode());
        assertEquals(exceptionMessage, result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).deletePlanByIds(ids, userDto);
    }

    static Stream<Arguments> provideRemoveBusinessExceptionScenarios() {
        return Stream.of(
                Arguments.of("方案不存在", ResponseCodeEnum.DELETE_DATA_NOT_FOUND.getCode()),
                Arguments.of("ID不能为空", ResponseCodeEnum.DELETE_PARAM_ERROR.getCode()),
                Arguments.of("其他业务异常", ResponseCodeEnum.DELETE_FAIL.getCode())
        );
    }

    @Test
    @DisplayName("删除方案-系统异常")
    void testRemove_SystemException() throws Exception {
        // given
        Long[] ids = {1L, 2L};
        when(userinfoComponent.getUser()).thenReturn(userDto);
        when(planService.deletePlanByIds(ids, userDto))
                .thenThrow(new RuntimeException("系统异常"));

        // when
        R<Void> result = planController.remove(ids);

        // then
        assertNotNull(result);
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getCode(), result.getCode());
        assertEquals(ResponseCodeEnum.SYSTEM_ERROR.getDesc(), result.getMessage());
        assertNull(result.getData());

        verify(userinfoComponent).getUser();
        verify(planService).deletePlanByIds(ids, userDto);
    }
} 