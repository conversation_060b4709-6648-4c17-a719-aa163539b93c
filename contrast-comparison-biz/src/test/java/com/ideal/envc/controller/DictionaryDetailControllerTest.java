package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.DictionaryDetailDto;
import com.ideal.envc.model.dto.DictionaryDetailQueryDto;
import com.ideal.envc.service.IDictionaryDetailService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 字典详情控制器测试
 */
@ExtendWith(MockitoExtension.class)
public class DictionaryDetailControllerTest {

    @Mock
    private IDictionaryDetailService dictionaryDetailService;

    @InjectMocks
    private DictionaryDetailController dictionaryDetailController;

    private TableQueryDto<DictionaryDetailQueryDto> tableQueryDto;
    private DictionaryDetailQueryDto queryDto;
    private DictionaryDetailDto dictionaryDetailDto;
    private PageInfo<DictionaryDetailDto> pageInfo;
    private List<DictionaryDetailDto> dictionaryDetailDtoList;

    @BeforeEach
    void setUp() {
        // 初始化查询参数
        queryDto = new DictionaryDetailQueryDto();
        queryDto.setCode("TEST_CODE");
        queryDto.setEnvcDictionaryId(1L);
        
        tableQueryDto = new TableQueryDto<>();
        tableQueryDto.setQueryParam(queryDto);
        tableQueryDto.setPageNum(1);
        tableQueryDto.setPageSize(10);

        // 初始化DTO对象
        dictionaryDetailDto = new DictionaryDetailDto();
        dictionaryDetailDto.setId(1L);
        dictionaryDetailDto.setEnvcDictionaryId(1L);
        dictionaryDetailDto.setCode("TEST_CODE");
        dictionaryDetailDto.setLable("测试标签");
        dictionaryDetailDto.setValue("测试值");
        dictionaryDetailDto.setSort(1L);
        dictionaryDetailDto.setDeleted(0);
        dictionaryDetailDto.setArrayFlag(0);
        dictionaryDetailDto.setValueType("string");
        dictionaryDetailDto.setCreatorId(1001L);
        dictionaryDetailDto.setCreatorName("测试用户");
        dictionaryDetailDto.setCreateTime(new Date());

        dictionaryDetailDtoList = new ArrayList<>();
        dictionaryDetailDtoList.add(dictionaryDetailDto);

        // 初始化分页对象
        pageInfo = new PageInfo<>(dictionaryDetailDtoList);
        pageInfo.setTotal(1);
    }

    @Test
    @DisplayName("测试查询字典详情列表")
    void testList() {
        // 设置Mock行为
        when(dictionaryDetailService.selectDictionaryDetailList(
                any(DictionaryDetailQueryDto.class), anyInt(), anyInt())).thenReturn(pageInfo);

        // 执行测试方法
        R<PageInfo<DictionaryDetailDto>> result = dictionaryDetailController.list(tableQueryDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().getTotal());
        
        // 验证服务调用
        verify(dictionaryDetailService).selectDictionaryDetailList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
    }

    @Test
    @DisplayName("测试根据ID查询字典详情信息")
    void testGetAgentInfoInfo() {
        // 设置Mock行为
        when(dictionaryDetailService.selectDictionaryDetailById(anyLong())).thenReturn(dictionaryDetailDto);

        // 执行测试方法
        R<DictionaryDetailDto> result = dictionaryDetailController.getAgentInfoInfo(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1L, result.getData().getId());
        assertEquals("TEST_CODE", result.getData().getCode());
        assertEquals("测试标签", result.getData().getLable());
        
        // 验证服务调用
        verify(dictionaryDetailService).selectDictionaryDetailById(1L);
    }

    @Test
    @DisplayName("测试新增字典详情")
    void testSave() {
        // 设置Mock行为
        when(dictionaryDetailService.insertDictionaryDetail(any(DictionaryDetailDto.class))).thenReturn(1);

        // 执行测试方法
        R<Void> result = dictionaryDetailController.save(dictionaryDetailDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(dictionaryDetailService).insertDictionaryDetail(dictionaryDetailDto);
    }

    @Test
    @DisplayName("测试新增字典详情失败")
    void testSaveFail() {
        // 设置Mock行为
        when(dictionaryDetailService.insertDictionaryDetail(any(DictionaryDetailDto.class))).thenReturn(0);

        // 执行测试方法
        R<Void> result = dictionaryDetailController.save(dictionaryDetailDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("00000", result.getCode());
        
        // 验证服务调用
        verify(dictionaryDetailService).insertDictionaryDetail(dictionaryDetailDto);
    }

    @Test
    @DisplayName("测试修改字典详情")
    void testUpdate() {
        // 设置Mock行为
        doReturn(1).when(dictionaryDetailService).updateDictionaryDetail(any(DictionaryDetailDto.class));

        // 执行测试方法
        R<Void> result = dictionaryDetailController.update(dictionaryDetailDto);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(dictionaryDetailService).updateDictionaryDetail(dictionaryDetailDto);
    }

    @Test
    @DisplayName("测试删除字典详情")
    void testRemove() {
        // 设置测试数据
        Long[] ids = {1L, 2L};
        
        // 设置Mock行为
        doReturn(2).when(dictionaryDetailService).deleteDictionaryDetailByIds(any());

        // 执行测试方法
        R<Void> result = dictionaryDetailController.remove(ids);

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        
        // 验证服务调用
        verify(dictionaryDetailService).deleteDictionaryDetailByIds(ids);
    }
    
    @Test
    @DisplayName("测试根据字典码获取字典详情列表")
    void testFindDictionaryDeatailList() {
        // 设置Mock行为
        when(dictionaryDetailService.findDictionaryDetailListByCode(anyString())).thenReturn(dictionaryDetailDtoList);

        // 执行测试方法
        R<List<DictionaryDetailDto>> result = dictionaryDetailController.findDictionaryDeatailList("TEST_CODE");

        // 验证结果
        assertNotNull(result);
        assertEquals("10000", result.getCode());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        assertEquals("TEST_CODE", result.getData().get(0).getCode());
        
        // 验证服务调用
        verify(dictionaryDetailService).findDictionaryDetailListByCode("TEST_CODE");
    }
} 