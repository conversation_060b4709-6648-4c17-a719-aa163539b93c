package com.ideal.envc.consumer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.EntegorSendBizUniqueDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ContrastReceiveTaskSendResultHandler单元测试
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class ContrastReceiveTaskSendResultHandlerTest {

    @InjectMocks
    private ContrastReceiveTaskSendResultHandler handler;

    private EntegorSendBizUniqueDto entegorSendBizUniqueDto;

    @BeforeEach
    void setUp() {
        entegorSendBizUniqueDto = new EntegorSendBizUniqueDto();
        entegorSendBizUniqueDto.setTaskFlowId(1L);
        entegorSendBizUniqueDto.setIsException(false);
    }

    @Test
    @DisplayName("测试处理有效消息 - 成功场景")
    void testNotice_ValidMessage_Success() {
        // 准备测试数据
        String messageJson = JSON.toJSONString(entegorSendBizUniqueDto);

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理null消息")
    void testNotice_NullMessage() {
        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(null));
    }

    @Test
    @DisplayName("测试处理空字符串消息")
    void testNotice_EmptyString() {
        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(""));
    }

    @Test
    @DisplayName("测试处理空白字符串消息")
    void testNotice_BlankString() {
        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice("   "));
    }

    @Test
    @DisplayName("测试处理无效JSON消息")
    void testNotice_InvalidJson() {
        // 准备测试数据
        String invalidJson = "invalid json format";

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(invalidJson));
    }

    @Test
    @DisplayName("测试处理JSON解析异常")
    void testNotice_JsonParseException() {
        // 准备测试数据 - 会导致JSON解析异常的消息
        String malformedJson = "{\"taskFlowId\":\"invalid_number\",\"isException\":true}";

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(malformedJson));
    }

    @Test
    @DisplayName("测试处理完整有效消息")
    void testNotice_CompleteValidMessage() {
        // 准备测试数据
        entegorSendBizUniqueDto.setTaskFlowId(12345L);
        entegorSendBizUniqueDto.setIsException(false);
        
        String messageJson = JSON.toJSONString(entegorSendBizUniqueDto);

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理异常消息")
    void testNotice_ExceptionMessage() {
        // 准备测试数据
        entegorSendBizUniqueDto.setTaskFlowId(12345L);
        entegorSendBizUniqueDto.setIsException(true);
        
        String messageJson = JSON.toJSONString(entegorSendBizUniqueDto);

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理缺少必要字段的消息")
    void testNotice_MissingRequiredFields() {
        // 准备测试数据 - 缺少taskFlowId或isException字段
        String incompleteJson = "{\"otherField\":\"test_value\"}";

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(incompleteJson));
    }

    @Test
    @DisplayName("测试处理null字段的消息")
    void testNotice_NullFields() {
        // 准备测试数据 - 包含null字段
        String nullFieldsJson = "{\"taskFlowId\":null,\"isException\":null}";

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(nullFieldsJson));
    }

    @Test
    @DisplayName("测试处理特殊字符消息")
    void testNotice_SpecialCharacters() {
        // 准备测试数据 - 包含特殊字符
        String messageJson = "{\"taskFlowId\":12345,\"isException\":false,\"specialField\":\"测试中文和特殊字符!@#$%^&*()\"}";

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理大数值消息")
    void testNotice_LargeValues() {
        // 准备测试数据 - 使用大数值
        entegorSendBizUniqueDto.setTaskFlowId(Long.MAX_VALUE);
        entegorSendBizUniqueDto.setIsException(false);
        String messageJson = JSON.toJSONString(entegorSendBizUniqueDto);

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理零值消息")
    void testNotice_ZeroValues() {
        // 准备测试数据 - 使用零值
        entegorSendBizUniqueDto.setTaskFlowId(0L);
        entegorSendBizUniqueDto.setIsException(false);
        String messageJson = JSON.toJSONString(entegorSendBizUniqueDto);

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理负数消息")
    void testNotice_NegativeValues() {
        // 准备测试数据 - 使用负数值
        entegorSendBizUniqueDto.setTaskFlowId(-1L);
        entegorSendBizUniqueDto.setIsException(false);
        String messageJson = JSON.toJSONString(entegorSendBizUniqueDto);

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(messageJson));
    }

    @Test
    @DisplayName("测试处理复杂嵌套JSON消息")
    void testNotice_ComplexNestedJson() {
        // 准备测试数据 - 复杂的嵌套JSON结构
        String complexJson = "{\"taskFlowId\":12345,\"isException\":false,\"nestedObject\":{\"field1\":\"value1\",\"field2\":123},\"arrayField\":[\"item1\",\"item2\",\"item3\"]}";

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(complexJson));
    }

    @Test
    @DisplayName("测试处理非字符串对象消息")
    void testNotice_NonStringObject() {
        // 准备测试数据 - 非字符串对象
        Object nonStringObject = new Object();

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(nonStringObject));
    }

    @Test
    @DisplayName("测试处理数字对象消息")
    void testNotice_NumberObject() {
        // 准备测试数据 - 数字对象
        Integer numberObject = 12345;

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(numberObject));
    }

    @Test
    @DisplayName("测试处理布尔对象消息")
    void testNotice_BooleanObject() {
        // 准备测试数据 - 布尔对象
        Boolean booleanObject = true;

        // 执行测试方法 - 不应该抛出异常
        assertDoesNotThrow(() -> handler.notice(booleanObject));
    }
} 