package com.ideal.envc.strategy.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.EngineCompareOutPutDto;
import com.ideal.envc.model.dto.OutputParseResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SyncFileOutputParseStrategy的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("SyncFileOutputParseStrategy单元测试")
class SyncFileOutputParseStrategyTest {

    @InjectMocks
    private SyncFileOutputParseStrategy strategy;

    private List<EngineActOutputBean> actOutputs;
    private String actDefName;

    @BeforeEach
    void setUp() {
        actOutputs = new ArrayList<>();
        actDefName = "测试活动";
    }

    @Test
    @DisplayName("解析输出 - 成功场景 - 同步结果成功")
    void parse_Success_ResultTrue() {
        // 准备测试数据
        EngineActOutputBean outputBean = new EngineActOutputBean();
        EngineCompareOutPutDto outputDto = new EngineCompareOutPutDto();
        outputDto.setRet(true);
        outputDto.setCompareResult("文件同步成功");
        outputBean.setOutput(JSON.toJSONString(outputDto));
        actOutputs.add(outputBean);

        // 执行测试
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertTrue(result.isRet());
        assertEquals("文件同步成功", result.getContent());
    }

    @Test
    @DisplayName("解析输出 - 成功场景 - 同步结果失败")
    void parse_Success_ResultFalse() {
        // 准备测试数据
        EngineActOutputBean outputBean = new EngineActOutputBean();
        EngineCompareOutPutDto outputDto = new EngineCompareOutPutDto();
        outputDto.setRet(false);
        outputDto.setCompareResult("文件同步失败");
        outputBean.setOutput(JSON.toJSONString(outputDto));
        actOutputs.add(outputBean);

        // 执行测试
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertFalse(result.isRet());
        assertEquals("文件同步失败", result.getContent());
    }

    @Test
    @DisplayName("解析输出 - 输入为空列表")
    void parse_EmptyList() {
        // 准备测试数据 - 空列表
        actOutputs = Collections.emptyList();

        // 执行测试
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("解析输出 - 输入为null")
    void parse_NullList() {
        // 执行测试
        OutputParseResult result = strategy.parse(null, actDefName);

        // 验证结果
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("解析输出 - JSON解析异常")
    void parse_JsonParseException() {
        // 准备测试数据 - 无效的JSON
        EngineActOutputBean outputBean = new EngineActOutputBean();
        outputBean.setOutput("这不是有效的JSON");
        actOutputs.add(outputBean);

        // 执行测试
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertFalse(result.isRet());
        assertEquals("", result.getContent());
    }

    @Test
    @DisplayName("解析输出 - 同步结果为null")
    void parse_NullSyncResult() {
        // 准备测试数据
        EngineActOutputBean outputBean = new EngineActOutputBean();
        EngineCompareOutPutDto outputDto = new EngineCompareOutPutDto();
        outputDto.setRet(null); // ret为null
        outputDto.setCompareResult("文件同步结果");
        outputBean.setOutput(JSON.toJSONString(outputDto));
        actOutputs.add(outputBean);

        // 执行测试
        OutputParseResult result = strategy.parse(actOutputs, actDefName);

        // 验证结果
        assertFalse(result.isRet());
        assertEquals("文件同步结果", result.getContent());
    }

    @Test
    @DisplayName("获取策略类型")
    void getType() {
        // 执行测试
        String type = strategy.getType();

        // 验证结果
        assertEquals("SYNC_FILE", type);
    }
} 