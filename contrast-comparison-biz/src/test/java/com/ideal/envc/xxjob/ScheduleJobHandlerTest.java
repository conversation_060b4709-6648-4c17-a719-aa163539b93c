package com.ideal.envc.xxjob;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.service.IStartContrastService;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.context.XxlJobHelper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * 定时类任务触发入口的单元测试
 */
@ExtendWith(MockitoExtension.class)
public class ScheduleJobHandlerTest {

    @Mock
    private IStartContrastService startContrastService;

    @InjectMocks
    private ScheduleJobHandler scheduleJobHandler;

    @Test
    @DisplayName("测试任务执行 - 参数为空")
    void testContrastJobHandlerWithEmptyParam() {
        try (MockedStatic<XxlJobHelper> mockedXxlJobHelper = mockStatic(XxlJobHelper.class)) {
            // 设置Mock行为 - 返回空参数
            mockedXxlJobHelper.when(XxlJobHelper::getJobParam).thenReturn("");
            
            // 执行测试方法
            scheduleJobHandler.contrastJobHandler();
            
            // 验证结果
            mockedXxlJobHelper.verify(XxlJobHelper::getJobParam);
            mockedXxlJobHelper.verify(() -> XxlJobHelper.handleResult(eq(XxlJobContext.HANDLE_CODE_FAIL), anyString()));
        }
    }

    @Test
    @DisplayName("测试任务执行 - 非法JSON格式")
    void testContrastJobHandlerWithInvalidJson() {
        try (MockedStatic<XxlJobHelper> mockedXxlJobHelper = mockStatic(XxlJobHelper.class)) {
            // 设置Mock行为 - 返回非法JSON格式
            mockedXxlJobHelper.when(XxlJobHelper::getJobParam).thenReturn("invalid json");
            
            // 执行测试方法
            scheduleJobHandler.contrastJobHandler();
            
            // 验证结果
            mockedXxlJobHelper.verify(XxlJobHelper::getJobParam);
            mockedXxlJobHelper.verify(() -> XxlJobHelper.handleResult(eq(XxlJobContext.HANDLE_CODE_FAIL), anyString()));
        }
    }

    @Test
    @DisplayName("测试任务执行 - 解析后对象为空")
    void testContrastJobHandlerWithNullObject() {
        try (MockedStatic<XxlJobHelper> mockedXxlJobHelper = mockStatic(XxlJobHelper.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 设置Mock行为
            mockedXxlJobHelper.when(XxlJobHelper::getJobParam).thenReturn("{}");
            mockedJson.when(() -> JSON.parseObject(anyString(), eq(ContrastScheduleJobTaskDto.class))).thenReturn(null);
            
            // 执行测试方法
            scheduleJobHandler.contrastJobHandler();
            
            // 验证结果
            mockedXxlJobHelper.verify(XxlJobHelper::getJobParam);
            mockedJson.verify(() -> JSON.parseObject(anyString(), eq(ContrastScheduleJobTaskDto.class)));
            mockedXxlJobHelper.verify(() -> XxlJobHelper.handleResult(eq(XxlJobContext.HANDLE_CODE_FAIL), anyString()));
        }
    }

    @Test
    @DisplayName("测试任务执行 - 成功场景")
    void testContrastJobHandlerSuccess() {
        try (MockedStatic<XxlJobHelper> mockedXxlJobHelper = mockStatic(XxlJobHelper.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 创建测试数据
            ContrastScheduleJobTaskDto taskDto = new ContrastScheduleJobTaskDto();
            taskDto.setTaskId(1L);
            taskDto.setTaskName("test task");
            taskDto.setPlanId(100L);
            taskDto.setCreatorId(1L);
            taskDto.setCreateName("test user");
            
            // 设置Mock行为
            mockedXxlJobHelper.when(XxlJobHelper::getJobParam).thenReturn("{\"name\":\"test\"}");
            mockedXxlJobHelper.when(XxlJobHelper::getJobId).thenReturn(1L);
            mockedJson.when(() -> JSON.parseObject(anyString(), eq(ContrastScheduleJobTaskDto.class))).thenReturn(taskDto);
            
            // 模拟startContrastService返回成功
            StartResult startResult = new StartResult();
            startResult.setSuccess(true);
            when(startContrastService.startByTasks(any(), any())).thenReturn(startResult);
            
            // 执行测试方法
            scheduleJobHandler.contrastJobHandler();
            
            // 验证调用
            mockedXxlJobHelper.verify(XxlJobHelper::getJobParam);
            mockedXxlJobHelper.verify(XxlJobHelper::getJobId);
            mockedJson.verify(() -> JSON.parseObject(anyString(), eq(ContrastScheduleJobTaskDto.class)));
            mockedXxlJobHelper.verify(() -> XxlJobHelper.handleSuccess("task start is success!"));
        }
    }

    @Test
    @DisplayName("测试任务执行 - 执行时异常")
    void testContrastJobHandlerWithException() {
        try (MockedStatic<XxlJobHelper> mockedXxlJobHelper = mockStatic(XxlJobHelper.class);
             MockedStatic<JSON> mockedJson = mockStatic(JSON.class)) {
            
            // 创建测试数据
            ContrastScheduleJobTaskDto taskDto = new ContrastScheduleJobTaskDto();
            taskDto.setTaskId(1L);
            taskDto.setTaskName("test task");
            taskDto.setPlanId(100L);
            taskDto.setCreatorId(1L);
            taskDto.setCreateName("test user");
            
            // 设置Mock行为
            mockedXxlJobHelper.when(XxlJobHelper::getJobParam).thenReturn("{\"name\":\"test\"}");
            mockedXxlJobHelper.when(XxlJobHelper::getJobId).thenReturn(1L);
            mockedJson.when(() -> JSON.parseObject(anyString(), eq(ContrastScheduleJobTaskDto.class))).thenReturn(taskDto);
            
            // 模拟startContrastService抛出异常
            RuntimeException expectedException = new RuntimeException("Test Exception");
            when(startContrastService.startByTasks(any(), any())).thenThrow(expectedException);
            
            // 执行测试方法
            scheduleJobHandler.contrastJobHandler();
            
            // 验证调用
            mockedXxlJobHelper.verify(XxlJobHelper::getJobParam);
            mockedXxlJobHelper.verify(XxlJobHelper::getJobId);
            mockedJson.verify(() -> JSON.parseObject(anyString(), eq(ContrastScheduleJobTaskDto.class)));
            mockedXxlJobHelper.verify(() -> XxlJobHelper.handleFail("Test Exception"));
        }
    }
} 