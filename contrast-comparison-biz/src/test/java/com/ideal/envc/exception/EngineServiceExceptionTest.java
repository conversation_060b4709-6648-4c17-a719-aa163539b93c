package com.ideal.envc.exception;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EngineServiceException单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("引擎服务异常测试")
class EngineServiceExceptionTest {

    @Test
    @DisplayName("测试带消息的构造函数")
    void testConstructorWithMessage() {
        // given
        String message = "引擎服务异常消息";

        // when
        EngineServiceException exception = new EngineServiceException(message);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试带代码和消息的构造函数")
    void testConstructorWithCodeAndMessage() {
        // given
        String code = "ENGINE_001";
        String message = "引擎服务异常消息";
        String expectedMessage = "[" + code + "] " + message;

        // when
        EngineServiceException exception = new EngineServiceException(code, message);

        // then
        assertNotNull(exception);
        assertEquals(expectedMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试带代码、消息和原因的构造函数")
    void testConstructorWithCodeMessageAndCause() {
        // given
        String code = "ENGINE_002";
        String message = "引擎服务异常消息";
        String expectedMessage = "[" + code + "] " + message;
        Throwable cause = new RuntimeException("原始异常");

        // when
        EngineServiceException exception = new EngineServiceException(code, message, cause);

        // then
        assertNotNull(exception);
        assertEquals(expectedMessage, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带消息和Exception的构造函数")
    void testConstructorWithMessageAndException() {
        // given
        String message = "引擎服务异常消息";
        Exception cause = new RuntimeException("原始异常");

        // when
        EngineServiceException exception = new EngineServiceException(message, cause);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带Exception的构造函数")
    void testConstructorWithException() {
        // given
        Exception cause = new RuntimeException("原始异常");

        // when
        EngineServiceException exception = new EngineServiceException(cause);

        // then
        assertNotNull(exception);
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带Throwable的构造函数")
    void testConstructorWithThrowable() {
        // given
        Throwable cause = new Error("严重错误");

        // when
        EngineServiceException exception = new EngineServiceException(cause);

        // then
        assertNotNull(exception);
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试带NoSuchMethodException的构造函数")
    void testConstructorWithNoSuchMethodException() {
        // given
        NoSuchMethodException cause = new NoSuchMethodException("方法不存在");

        // when
        EngineServiceException exception = new EngineServiceException(cause);

        // then
        assertNotNull(exception);
        assertEquals(cause.toString(), exception.getMessage());
        assertEquals(cause, exception.getCause());
    }

    @Test
    @DisplayName("测试异常继承关系")
    void testExceptionInheritance() {
        // given
        EngineServiceException exception = new EngineServiceException("测试消息");

        // then
        assertTrue(exception instanceof Exception);
        assertTrue(exception instanceof Throwable);
    }

    @Test
    @DisplayName("测试空消息构造函数")
    void testConstructorWithNullMessage() {
        // given
        String message = null;

        // when
        EngineServiceException exception = new EngineServiceException(message);

        // then
        assertNotNull(exception);
        assertNull(exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试空代码和消息构造函数")
    void testConstructorWithNullCodeAndMessage() {
        // given
        String code = null;
        String message = null;
        String expectedMessage = "[null] null";

        // when
        EngineServiceException exception = new EngineServiceException(code, message);

        // then
        assertNotNull(exception);
        assertEquals(expectedMessage, exception.getMessage());
        assertNull(exception.getCause());
    }

    @Test
    @DisplayName("测试空原因构造函数")
    void testConstructorWithNullCause() {
        // given
        String message = "测试消息";
        Exception cause = null;

        // when
        EngineServiceException exception = new EngineServiceException(message, cause);

        // then
        assertNotNull(exception);
        assertEquals(message, exception.getMessage());
        assertNull(exception.getCause());
    }
} 