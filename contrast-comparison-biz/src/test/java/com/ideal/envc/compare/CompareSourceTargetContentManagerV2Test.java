package com.ideal.envc.compare;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * CompareSourceTargetContentManagerV2 测试类
 * 验证升级版本与原版本的功能一致性
 */
public class CompareSourceTargetContentManagerV2Test {

    private CompareSourceTargetContentManager originalManager;
    private CompareSourceTargetContentManagerV2 v2Manager;

    @BeforeEach
    void setUp() {
        originalManager = new CompareSourceTargetContentManager();
        v2Manager = new CompareSourceTargetContentManagerV2();
    }

    @Test
    @DisplayName("测试相同内容比对结果一致性")
    void testIdenticalContent() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertTrue((Boolean) v2Result.get("ret"), "相同内容应该返回true");
    }

    @Test
    @DisplayName("测试不同内容比对结果一致性")
    void testDifferentContent() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "modified_line2", "line3");

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertFalse((Boolean) v2Result.get("ret"), "不同内容应该返回false");
    }

    @Test
    @DisplayName("测试插入行比对结果一致性")
    void testInsertLines() {
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertFalse((Boolean) v2Result.get("ret"), "有插入行应该返回false");
    }

    @Test
    @DisplayName("测试删除行比对结果一致性")
    void testDeleteLines() {
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2");

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertFalse((Boolean) v2Result.get("ret"), "有删除行应该返回false");
    }

    @Test
    @DisplayName("测试文件格式比对功能")
    void testFileFormatComparison() {
        // 测试符合文件格式的内容
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024B)",
            "file2.txt (size: 2048B)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024B)",
            "file2.txt (size: 3072B)"  // 大小不同
        );

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertFalse((Boolean) v2Result.get("ret"), "文件大小不同应该返回false");
    }

    @Test
    @DisplayName("测试空列表比对")
    void testEmptyLists() {
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertTrue((Boolean) v2Result.get("ret"), "空列表比对应该返回true");
    }

    @Test
    @DisplayName("测试行数限制功能")
    void testLineLimitCheck() {
        // 创建超过10000行的列表
        List<String> aLines = new ArrayList<>();
        for (int i = 0; i < 10001; i++) {
            aLines.add("line" + i);
        }
        List<String> bLines = Arrays.asList("line1");

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        // 验证ret结果一致
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");
        assertFalse((Boolean) v2Result.get("ret"), "超过行数限制应该返回false");
        
        // 验证错误消息
        String originalMsg = (String) originalResult.get("compareResult");
        String v2Msg = (String) v2Result.get("compareResult");
        assertTrue(originalMsg.contains("exceeds 10,000"), "原版本应包含行数限制错误消息");
        assertTrue(v2Msg.contains("exceeds 10,000"), "V2版本应包含行数限制错误消息");
    }

    @Test
    @DisplayName("测试HTML输出格式基本结构")
    void testHtmlOutputStructure() {
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = Arrays.asList("line1", "modified_line2");

        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);

        String originalHtml = (String) originalResult.get("compareResult");
        String v2Html = (String) v2Result.get("compareResult");

        // 验证HTML基本结构
        assertTrue(v2Html.contains("<div class=\"comparison_space\">"), "应包含comparison_space div");
        assertTrue(v2Html.contains("<table"), "应包含table标签");
        assertTrue(v2Html.contains("Source"), "应包含Source标题");
        assertTrue(v2Html.contains("Target"), "应包含Target标题");
        assertTrue(v2Html.contains("</table></div>"), "应包含结束标签");
    }

    @Test
    @DisplayName("测试性能对比")
    void testPerformanceComparison() {
        // 创建较大的测试数据
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();
        
        for (int i = 0; i < 1000; i++) {
            aLines.add("line" + i);
            bLines.add(i % 10 == 0 ? "modified_line" + i : "line" + i); // 每10行修改一次
        }

        // 测试原版本性能
        long originalStart = System.currentTimeMillis();
        Map<String, Object> originalResult = originalManager.compare(aLines, bLines);
        long originalTime = System.currentTimeMillis() - originalStart;

        // 测试V2版本性能
        long v2Start = System.currentTimeMillis();
        Map<String, Object> v2Result = v2Manager.compare(aLines, bLines);
        long v2Time = System.currentTimeMillis() - v2Start;

        // 验证结果一致性
        assertEquals(originalResult.get("ret"), v2Result.get("ret"), "ret结果应该一致");

        // 输出性能对比信息
        System.out.println("原版本耗时: " + originalTime + "ms");
        System.out.println("V2版本耗时: " + v2Time + "ms");
        System.out.println("性能提升: " + (originalTime > v2Time ? "是" : "否"));
        
        // V2版本应该不会比原版本慢太多（允许一定的性能波动）
        assertTrue(v2Time <= originalTime * 2, "V2版本性能不应该比原版本慢太多");
    }
}
