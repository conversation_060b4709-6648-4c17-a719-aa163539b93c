package com.ideal.envc.compare;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CompareSourceTargetContentCompnent 优化版本测试
 * 确保优化后的功能与原版本完全一致
 * 
 * <AUTHOR>
 */
public class CompareSourceTargetContentCompnentOptimizationTest {

    private CompareSourceTargetContentCompnent component;

    @BeforeEach
    void setUp() {
        component = new CompareSourceTargetContentCompnent();
        // 清理缓存确保测试独立性
        CompareSourceTargetContentCompnent.clearCaches();
    }

    @Test
    @DisplayName("测试基本功能-相同内容")
    void testBasicFunctionality_SameContent() {
        // 准备测试数据
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");

        // 执行比对
        Map<String, Object> result = component.compare(aLines, bLines);

        // 验证结果
        assertNotNull(result);
        assertTrue((Boolean) result.get("ret"), "相同内容应该返回true");
        assertNotNull(result.get("compareResult"));
        assertNotNull(result.get("sourceContent"));
        assertNotNull(result.get("targetContent"));
        
        // 验证内容完整性
        assertEquals("line1\nline2\nline3", result.get("sourceContent"));
        assertEquals("line1\nline2\nline3", result.get("targetContent"));
    }

    @Test
    @DisplayName("测试基本功能-不同内容")
    void testBasicFunctionality_DifferentContent() {
        // 准备测试数据
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "modified_line2", "line3");

        // 执行比对
        Map<String, Object> result = component.compare(aLines, bLines);

        // 验证结果
        assertNotNull(result);
        assertFalse((Boolean) result.get("ret"), "不同内容应该返回false");
        
        String compareResult = (String) result.get("compareResult");
        assertNotNull(compareResult);
        assertTrue(compareResult.contains("Source"), "应该包含Source标题");
        assertTrue(compareResult.contains("Target"), "应该包含Target标题");
        assertTrue(compareResult.contains("comparison_space"), "应该包含比对样式");
    }

    @Test
    @DisplayName("测试文件格式验证功能")
    void testFileFormatValidation() {
        // 准备符合格式的测试数据
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024)",
            "file2.txt (size: 2048)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024)",
            "file3.txt (size: 3072)"
        );

        // 执行比对
        Map<String, Object> result = component.compare(aLines, bLines);

        // 验证结果
        assertNotNull(result);
        assertFalse((Boolean) result.get("ret"), "不同文件应该返回false");
        
        String compareResult = (String) result.get("compareResult");
        assertNotNull(compareResult);
        assertTrue(compareResult.length() > 0, "应该生成比对结果HTML");
    }

    @Test
    @DisplayName("测试行数限制")
    void testLineLimitation() {
        // 准备超过限制的测试数据
        List<String> aLines = new ArrayList<>();
        for (int i = 0; i < 10001; i++) {
            aLines.add("line" + i);
        }
        List<String> bLines = Arrays.asList("line1");

        // 执行比对
        Map<String, Object> result = component.compare(aLines, bLines);

        // 验证结果
        assertNotNull(result);
        assertFalse((Boolean) result.get("ret"), "超过行数限制应该返回false");
        
        String compareResult = (String) result.get("compareResult");
        assertTrue(compareResult.contains("exceeds 10,000"), "应该包含行数超限提示");
    }

    @Test
    @DisplayName("测试空输入处理")
    void testEmptyInput() {
        // 测试空列表
        List<String> emptyLines = new ArrayList<>();
        List<String> normalLines = Arrays.asList("line1");

        Map<String, Object> result1 = component.compare(emptyLines, normalLines);
        assertNotNull(result1);
        assertFalse((Boolean) result1.get("ret"));

        Map<String, Object> result2 = component.compare(normalLines, emptyLines);
        assertNotNull(result2);
        assertFalse((Boolean) result2.get("ret"));

        Map<String, Object> result3 = component.compare(emptyLines, emptyLines);
        assertNotNull(result3);
        assertTrue((Boolean) result3.get("ret"));
    }

    @Test
    @DisplayName("测试null输入处理")
    void testNullInput() {
        List<String> normalLines = Arrays.asList("line1");

        Map<String, Object> result1 = component.compare(null, normalLines);
        assertNotNull(result1);
        assertFalse((Boolean) result1.get("ret"));

        Map<String, Object> result2 = component.compare(normalLines, null);
        assertNotNull(result2);
        assertFalse((Boolean) result2.get("ret"));

        Map<String, Object> result3 = component.compare(null, null);
        assertNotNull(result3);
        assertFalse((Boolean) result3.get("ret"));
    }

    @Test
    @DisplayName("测试大文件分块处理")
    void testChunkProcessing() {
        // 准备大文件测试数据（超过分块阈值）
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();
        
        for (int i = 0; i < 1500; i++) {
            aLines.add("source_line_" + i);
            bLines.add("target_line_" + i);
        }
        
        // 修改一些行以产生差异
        bLines.set(500, "modified_target_line_500");
        bLines.set(1000, "modified_target_line_1000");

        // 执行比对
        long startTime = System.currentTimeMillis();
        Map<String, Object> result = component.compare(aLines, bLines);
        long endTime = System.currentTimeMillis();

        // 验证结果
        assertNotNull(result);
        assertFalse((Boolean) result.get("ret"), "有差异的文件应该返回false");
        
        String compareResult = (String) result.get("compareResult");
        assertNotNull(compareResult);
        assertTrue(compareResult.contains("Source"), "应该包含Source标题");
        assertTrue(compareResult.contains("Target"), "应该包含Target标题");
        
        System.out.println("大文件处理耗时: " + (endTime - startTime) + " 毫秒");
    }

    @Test
    @DisplayName("测试性能优化效果")
    void testPerformanceOptimization() {
        // 准备中等大小的测试数据
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();
        
        for (int i = 0; i < 500; i++) {
            aLines.add("line_" + i + "_content_with_some_<br>_tags");
            bLines.add("line_" + i + "_content_with_some_<br>_tags");
        }
        
        // 修改部分行
        for (int i = 0; i < 50; i++) {
            bLines.set(i * 10, "modified_line_" + (i * 10));
        }

        // 多次执行测试缓存效果
        long totalTime = 0;
        int iterations = 5;
        
        for (int i = 0; i < iterations; i++) {
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = component.compare(aLines, bLines);
            long endTime = System.currentTimeMillis();
            
            totalTime += (endTime - startTime);
            
            // 验证结果一致性
            assertNotNull(result);
            assertFalse((Boolean) result.get("ret"));
        }
        
        double avgTime = (double) totalTime / iterations;
        System.out.println("平均处理时间: " + String.format("%.2f", avgTime) + " 毫秒");
        
        // 验证缓存使用情况
        Map<String, Object> stats = component.getOptimizationStats();
        System.out.println("缓存统计: " + stats);
        
        assertTrue((Integer) stats.get("replaceCacheSize") > 0, "应该使用了字符串替换缓存");
    }

    @Test
    @DisplayName("测试HTML输出格式完整性")
    void testHtmlOutputIntegrity() {
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = Arrays.asList("line1", "modified_line2");

        Map<String, Object> result = component.compare(aLines, bLines);
        String html = (String) result.get("compareResult");

        // 验证HTML结构完整性
        assertTrue(html.startsWith("<div class=\"comparison_space\">"), "应该以正确的div开始");
        assertTrue(html.endsWith("</table></div>"), "应该以正确的标签结束");
        assertTrue(html.contains("<table"), "应该包含table标签");
        assertTrue(html.contains("Source"), "应该包含Source标题");
        assertTrue(html.contains("Target"), "应该包含Target标题");
        
        // 验证没有未闭合的标签
        int openTags = countOccurrences(html, "<");
        int closeTags = countOccurrences(html, ">");
        assertEquals(openTags, closeTags, "HTML标签应该正确闭合");
    }

    @Test
    @DisplayName("测试向后兼容性")
    void testBackwardCompatibility() {
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = Arrays.asList("line1", "line2");

        // 测试新方法
        Map<String, Object> result1 = component.compare(aLines, bLines);
        
        // 测试兼容方法
        Map<String, Object> result2 = component.compareFiles(aLines, bLines);

        // 验证结果一致
        assertEquals(result1.get("ret"), result2.get("ret"));
        assertEquals(result1.get("sourceContent"), result2.get("sourceContent"));
        assertEquals(result1.get("targetContent"), result2.get("targetContent"));
    }

    /**
     * 辅助方法：计算字符串中指定子串的出现次数
     */
    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
