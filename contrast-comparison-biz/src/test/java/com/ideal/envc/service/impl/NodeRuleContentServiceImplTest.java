package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.Page;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.model.dto.NodeRuleContentDto;
import com.ideal.envc.model.dto.NodeRuleContentQueryDto;
import com.ideal.envc.model.entity.NodeRuleContentEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.MockitoAnnotations;
import com.github.pagehelper.page.PageMethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 节点关系规则Service业务层处理测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("NodeRuleContentServiceImpl单元测试")
class NodeRuleContentServiceImplTest {

    @Mock
    private NodeRuleContentMapper nodeRuleContentMapper;

    @InjectMocks
    private NodeRuleContentServiceImpl nodeRuleContentService;

    private NodeRuleContentDto nodeRuleContentDto;
    private NodeRuleContentEntity nodeRuleContentEntity;
    private NodeRuleContentQueryDto nodeRuleContentQueryDto;
    private List<NodeRuleContentEntity> nodeRuleContentEntityList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        nodeRuleContentDto = new NodeRuleContentDto();
        nodeRuleContentDto.setId(1L);
        nodeRuleContentDto.setEnvcNodeRelationId(100L);
        nodeRuleContentDto.setRuleContent("测试规则内容");
        nodeRuleContentDto.setRuleName("测试规则名称");

        nodeRuleContentEntity = new NodeRuleContentEntity();
        nodeRuleContentEntity.setId(1L);
        nodeRuleContentEntity.setEnvcNodeRelationId(100L);
        nodeRuleContentEntity.setRuleContent("测试规则内容");
        nodeRuleContentEntity.setRuleName("测试规则名称");

        nodeRuleContentQueryDto = new NodeRuleContentQueryDto();
        nodeRuleContentQueryDto.setEnvcNodeRelationId(100L);
        
        nodeRuleContentEntityList = new ArrayList<>();
        nodeRuleContentEntityList.add(nodeRuleContentEntity);
    }

    @Test
    @DisplayName("根据ID查询节点规则 - 成功场景")
    void selectNodeRuleContentById_Success() {
        // 准备测试数据
        Long ruleId = 1L;
        NodeRuleContentEntity entity = createNodeRuleContentEntity(ruleId, "测试规则");

        // Mock依赖
        when(nodeRuleContentMapper.selectNodeRuleContentById(ruleId)).thenReturn(entity);

        // 执行测试
        NodeRuleContentDto result = nodeRuleContentService.selectNodeRuleContentById(ruleId);

        // 验证结果
        assertNotNull(result);
        assertEquals(ruleId, result.getId());
        assertEquals("测试规则", result.getRuleName());
        verify(nodeRuleContentMapper).selectNodeRuleContentById(ruleId);
    }

    @Test
    @DisplayName("查询节点规则列表 - 成功场景")
    void selectNodeRuleContentList_Success() {
        // 准备测试数据
        NodeRuleContentQueryDto queryDto = new NodeRuleContentQueryDto();
        Integer pageNum = 1;
        Integer pageSize = 10;

        // 创建一个Page对象，而不是普通List
        Page<NodeRuleContentEntity> page = new Page<>(pageNum, pageSize);
        page.add(createNodeRuleContentEntity(1L, "规则1"));
        page.add(createNodeRuleContentEntity(2L, "规则2"));
        page.setTotal(2);

        // Mock依赖
        try (MockedStatic<PageMethod> mockedPageMethod = mockStatic(PageMethod.class)) {
            // 模拟PageMethod.startPage的行为
            mockedPageMethod.when(() -> PageMethod.startPage(anyInt(), anyInt())).thenReturn(page);
            
            // 模拟mapper返回Page对象
            when(nodeRuleContentMapper.selectNodeRuleContentList(any())).thenReturn(page);
            
            // 模拟PageDataUtil.toDtoPage的行为
            try (MockedStatic<PageDataUtil> mockedPageDataUtil = mockStatic(PageDataUtil.class)) {
                List<NodeRuleContentDto> dtoList = Arrays.asList(
                    BeanUtils.copy(page.get(0), NodeRuleContentDto.class),
                    BeanUtils.copy(page.get(1), NodeRuleContentDto.class)
                );
                PageInfo<NodeRuleContentDto> expectedPageInfo = new PageInfo<>();
                expectedPageInfo.setList(dtoList);
                expectedPageInfo.setTotal(2);
                expectedPageInfo.setPageNum(pageNum);
                expectedPageInfo.setPageSize(pageSize);
                
                mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(eq(page), eq(NodeRuleContentDto.class)))
                    .thenReturn(expectedPageInfo);
                
                // 执行测试
                PageInfo<NodeRuleContentDto> result = nodeRuleContentService.selectNodeRuleContentList(queryDto, pageNum, pageSize);
                
                // 验证结果
                assertNotNull(result);
                assertEquals(2, result.getList().size());
                verify(nodeRuleContentMapper).selectNodeRuleContentList(any());
            }
        }
    }

    @Test
    @DisplayName("新增节点规则 - 成功场景")
    void insertNodeRuleContent_Success() {
        // 准备测试数据
        NodeRuleContentDto dto = new NodeRuleContentDto();
        dto.setRuleName("新规则");

        // Mock依赖
        when(nodeRuleContentMapper.insertNodeRuleContent(any())).thenReturn(1);

        // 执行测试
        int result = nodeRuleContentService.insertNodeRuleContent(dto);

        // 验证结果
        assertEquals(1, result);
        verify(nodeRuleContentMapper).insertNodeRuleContent(any());
    }

    @Test
    @DisplayName("修改节点规则 - 成功场景")
    void updateNodeRuleContent_Success() {
        // 准备测试数据
        NodeRuleContentDto dto = new NodeRuleContentDto();
        dto.setId(1L);
        dto.setRuleName("更新规则");

        // Mock依赖
        when(nodeRuleContentMapper.updateNodeRuleContent(any())).thenReturn(1);

        // 执行测试
        int result = nodeRuleContentService.updateNodeRuleContent(dto);

        // 验证结果
        assertEquals(1, result);
        verify(nodeRuleContentMapper).updateNodeRuleContent(any());
    }

    @Test
    @DisplayName("批量删除节点规则 - 成功场景")
    void deleteNodeRuleContentByIds_Success() {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // Mock依赖
        when(nodeRuleContentMapper.deleteNodeRuleContentByIds(ids)).thenReturn(2);

        // 执行测试
        int result = nodeRuleContentService.deleteNodeRuleContentByIds(ids);

        // 验证结果
        assertEquals(2, result);
        verify(nodeRuleContentMapper).deleteNodeRuleContentByIds(ids);
    }

    @Test
    @DisplayName("批量删除节点规则 - 空ID数组")
    void deleteNodeRuleContentByIds_EmptyIds() {
        // 准备测试数据
        Long[] ids = {};

        // Mock依赖
        when(nodeRuleContentMapper.deleteNodeRuleContentByIds(ids)).thenReturn(0);

        // 执行测试
        int result = nodeRuleContentService.deleteNodeRuleContentByIds(ids);

        // 验证结果
        assertEquals(0, result);
        verify(nodeRuleContentMapper).deleteNodeRuleContentByIds(ids);
    }

    // 辅助方法
    private NodeRuleContentEntity createNodeRuleContentEntity(Long id, String ruleName) {
        NodeRuleContentEntity entity = new NodeRuleContentEntity();
        entity.setId(id);
        entity.setRuleName(ruleName);
        return entity;
    }
} 