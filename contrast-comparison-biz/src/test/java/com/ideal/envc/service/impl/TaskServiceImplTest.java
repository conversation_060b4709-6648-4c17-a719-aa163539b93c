package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.TaskMapper;
import com.ideal.envc.model.bean.TaskListBean;
import com.ideal.envc.model.bean.TaskPlanListBean;
import com.ideal.envc.model.bean.TaskQueryBean;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.TaskCronUpdateDto;
import com.ideal.envc.model.dto.TaskDto;
import com.ideal.envc.model.dto.TaskOperateResultDto;
import com.ideal.envc.model.dto.TaskPlanListDto;
import com.ideal.envc.model.dto.TaskQueryDto;
import com.ideal.envc.model.dto.TaskStartOrStopDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.TaskEntity;
import com.ideal.envc.model.enums.JobHandlerEnum;
import com.ideal.envc.model.enums.TaskOperateEnums;
import com.ideal.envc.service.IJobOperateService;
import com.ideal.envc.common.ContrastToolUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.lang.reflect.InvocationTargetException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 任务Service业务层单元测试
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
public class TaskServiceImplTest {

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private SystemInteract systemInteract;

    @Mock
    private IJobOperateService jobOperateService;

    @Spy
    @InjectMocks
    private TaskServiceImpl taskService;

    private TaskDto taskDto;
    private TaskEntity taskEntity;
    private TaskQueryDto taskQueryDto;
    private UserDto userDto;
    private TaskListBean taskListBean;
    private TaskPlanListBean taskPlanListBean;
    private List<TaskListBean> taskListBeanList;
    private List<TaskPlanListBean> taskPlanListBeanList;
    private List<CenterDto> centerList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskDto = new TaskDto();
        taskDto.setId(1L);
        taskDto.setEnvcPlanId(100L);
        taskDto.setCron("0 0 12 * * ?");
        taskDto.setSourceCenterId(1L);
        taskDto.setTargetCenterId(2L);
        taskDto.setState(TaskOperateEnums.STOP.getCode());
        taskDto.setEnabled(1);

        taskEntity = new TaskEntity();
        taskEntity.setId(1L);
        taskEntity.setEnvcPlanId(100L);
        taskEntity.setCron("0 0 12 * * ?");
        taskEntity.setSourceCenterId(1L);
        taskEntity.setTargetCenterId(2L);
        taskEntity.setState(TaskOperateEnums.STOP.getCode());
        taskEntity.setEnabled(1);

        taskQueryDto = new TaskQueryDto();
        taskQueryDto.setId(1L);
        taskQueryDto.setEnvcPlanId(100L);

        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        taskListBean = new TaskListBean();
        taskListBean.setId(1L);
        taskListBean.setEnvcPlanId(100L);
        taskListBean.setEnvcPlanName("测试方案");
        taskListBean.setCron("0 0 12 * * ?");
        taskListBean.setSourceCenterId(1L);
        taskListBean.setTargetCenterId(2L);
        taskListBean.setState(TaskOperateEnums.STOP.getCode());

        taskListBeanList = new ArrayList<>();
        taskListBeanList.add(taskListBean);

        taskPlanListBean = new TaskPlanListBean();
        taskPlanListBean.setId(1L);
        taskPlanListBean.setEnvcPlanId(100L);
        taskPlanListBean.setPlanName("测试方案");
        taskPlanListBean.setSourceCenterId(1L);
        taskPlanListBean.setTargetCenterId(2L);

        taskPlanListBeanList = new ArrayList<>();
        taskPlanListBeanList.add(taskPlanListBean);

        centerList = new ArrayList<>();
        CenterDto center1 = new CenterDto();
        center1.setId(1L);
        center1.setName("源中心");
        centerList.add(center1);

        CenterDto center2 = new CenterDto();
        center2.setId(2L);
        center2.setName("目标中心");
        centerList.add(center2);
    }

    @Test
    @DisplayName("测试根据ID查询任务 - 成功")
    void testSelectTaskById_Success() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            when(taskMapper.selectTaskById(1L)).thenReturn(taskEntity);
            mockedBeanUtils.when(() -> BeanUtils.copy(taskEntity, TaskDto.class)).thenReturn(taskDto);

            // 执行测试方法
            TaskDto result = taskService.selectTaskById(1L);

            // 验证结果
            assertNotNull(result);
            assertEquals(1L, result.getId());

            // 验证方法调用
            verify(taskMapper, times(1)).selectTaskById(1L);
            mockedBeanUtils.verify(() -> BeanUtils.copy(taskEntity, TaskDto.class));
        }
    }

    @Test
    @DisplayName("测试根据ID查询任务 - 任务不存在")
    void testSelectTaskById_NotFound() {
        // 设置Mock行为
        when(taskMapper.selectTaskById(999L)).thenReturn(null);

        // 执行测试方法
        TaskDto result = taskService.selectTaskById(999L);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(taskMapper, times(1)).selectTaskById(999L);
    }

    @Test
    @DisplayName("测试查询任务列表 - 成功")
    void testSelectTaskList_Success() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskQueryDto, TaskQueryBean.class)).thenReturn(new TaskQueryBean());
            when(taskMapper.selectTaskListWithPlan(any(TaskQueryBean.class))).thenReturn(taskListBeanList);
            mockedBeanUtils.when(() -> BeanUtils.copy(taskListBeanList, TaskDto.class)).thenReturn(Arrays.asList(taskDto));
            when(systemInteract.getCenterList(any(CenterQueryDto.class))).thenReturn(centerList);

            // 执行测试方法
            PageInfo<TaskDto> result = taskService.selectTaskList(taskQueryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertFalse(result.getList().isEmpty());

            // 验证方法调用
            verify(taskMapper, times(1)).selectTaskListWithPlan(any(TaskQueryBean.class));
            verify(systemInteract, times(1)).getCenterList(any(CenterQueryDto.class));
        }
    }

    @Test
    @DisplayName("测试查询任务列表 - 参数为空")
    void testSelectTaskList_NullQueryDto() throws ContrastBusinessException {
        // 执行测试方法
        PageInfo<TaskDto> result = taskService.selectTaskList(null, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getList().isEmpty());
    }

    @Test
    @DisplayName("测试查询任务列表 - 异常")
    void testSelectTaskList_Exception() {
        // 设置Mock行为
        when(taskMapper.selectTaskListWithPlan(any(TaskQueryBean.class))).thenThrow(new RuntimeException("数据库异常"));

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.selectTaskList(taskQueryDto, 1, 10);
        });
    }

    @Test
    @DisplayName("测试新增任务 - 成功")
    void testInsertTask_Success() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.insertTask(taskEntity)).thenReturn(1);

            // 执行测试方法
            int result = taskService.insertTask(taskDto);

            // 验证结果
            assertEquals(1, result);

            // 验证方法调用
            verify(taskMapper, times(1)).insertTask(taskEntity);
        } catch (ContrastBusinessException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @DisplayName("测试创建任务 - 成功")
    void testCreateTask_Success() throws ScheduleJobOperateException, ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.insertTask(taskEntity)).thenReturn(1);
            when(taskMapper.updateTaskScheduledIdAndState(anyLong(), anyLong(), anyInt())).thenReturn(1);
            when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);

            // 执行测试方法
            Long result = taskService.createTask(taskDto, userDto);

            // 验证结果
            assertNotNull(result);

            // 验证方法调用
            verify(taskMapper, times(1)).insertTask(taskEntity);
            verify(jobOperateService, times(1)).createAndStartJob(any(ContrastScheduleJobTaskDto.class));
            verify(taskMapper, times(1)).updateTaskScheduledIdAndState(anyLong(), anyLong(), anyInt());
        }
    }

    @Test
    @DisplayName("测试创建任务 - 插入失败")
    void testCreateTask_InsertFailed() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.insertTask(taskEntity)).thenReturn(0);

            // 执行测试方法并验证异常
            assertThrows(ContrastBusinessException.class, () -> {
                taskService.createTask(taskDto, userDto);
            });
        }
    }

    @Test
    @DisplayName("测试创建任务 - 创建调度任务失败")
    void testCreateTask_ScheduleJobFailed() throws ScheduleJobOperateException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.insertTask(taskEntity)).thenReturn(1);
            when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(0);

            // 执行测试方法并验证异常
            assertThrows(ScheduleJobOperateException.class, () -> {
                taskService.createTask(taskDto, userDto);
            });
        }
    }

    @Test
    @DisplayName("测试修改任务 - 成功")
    void testModifyTask_Success() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            when(taskMapper.selectTaskById(1L)).thenReturn(taskEntity);
            when(taskMapper.checkTaskExistsExcludeId(1L, 100L, "0 0 12 * * ?")).thenReturn(0);
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.updateTask(taskEntity)).thenReturn(1);

            // 执行测试方法
            int result = taskService.modifyTask(taskDto, userDto);

            // 验证结果
            assertEquals(1, result);

            // 验证方法调用
            verify(taskMapper, times(1)).selectTaskById(1L);
            verify(taskMapper, times(1)).updateTask(taskEntity);
        }
    }

    @Test
    @DisplayName("测试修改任务 - 任务不存在")
    void testModifyTask_TaskNotFound() {
        // 设置Mock行为 - 使用taskDto的ID
        when(taskMapper.selectTaskById(taskDto.getId())).thenReturn(null);

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.modifyTask(taskDto, userDto);
        });
    }

    @Test
    @DisplayName("测试操作任务 - 启动任务成功")
    void testOperateTasks_StartSuccess() throws ContrastBusinessException {
        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskListBeanList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskByScheduleId(123L)).thenReturn(taskEntity);
        when(taskMapper.updateTaskStateByIds(Arrays.asList(1L), TaskOperateEnums.START.getCode())).thenReturn(1);
        when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(true);
        when(jobOperateService.startJob(123)).thenReturn(true);

        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(taskEntity, TaskDto.class)).thenReturn(taskDto);

            // 创建测试参数
            TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
            taskStartOrStopDto.setTaskIdList(Arrays.asList(1L));
            taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

            // 执行测试方法
            taskService.operateTasks(taskStartOrStopDto, userDto);

            // 验证方法调用
            verify(taskMapper, times(1)).selectTaskListByIds(Arrays.asList(1L));
            verify(taskMapper, times(1)).selectScheduledIdsByTaskIds(Arrays.asList(1L));
            verify(jobOperateService, times(1)).startJob(123);
            verify(taskMapper, times(1)).updateTaskStateByIds(Arrays.asList(1L), TaskOperateEnums.START.getCode());
        }
    }

    @Test
    @DisplayName("测试操作任务 - 停止任务成功")
    void testOperateTasks_StopSuccess() throws ContrastBusinessException {
        // 准备测试数据 - 确保任务状态为运行中
        TaskListBean runningTask = new TaskListBean();
        runningTask.setId(1L);
        runningTask.setEnvcPlanName("测试方案");
        runningTask.setCron("0 0 12 * * ?");
        runningTask.setState(TaskOperateEnums.START.getCode()); // 设置为运行状态
        runningTask.setScheduledId(123L);
        
        List<TaskListBean> taskList = Arrays.asList(runningTask);

        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskByScheduleId(123L)).thenReturn(taskEntity);
        when(jobOperateService.stopJob(123)).thenReturn(true);
        when(taskMapper.updateTaskStateByIds(Arrays.asList(1L), TaskOperateEnums.STOP.getCode())).thenReturn(1);

        // 准备操作参数
        TaskStartOrStopDto operateDto = new TaskStartOrStopDto();
        operateDto.setTaskIdList(Arrays.asList(1L));
        operateDto.setOperateType(TaskOperateEnums.STOP.getCode());

        // 执行测试方法
        taskService.operateTasks(operateDto, userDto);

        // 验证方法调用
        verify(taskMapper).selectTaskListByIds(Arrays.asList(1L));
        verify(taskMapper).selectScheduledIdsByTaskIds(Arrays.asList(1L));
        verify(taskMapper).selectTaskByScheduleId(123L);
        verify(jobOperateService).stopJob(123);
        verify(taskMapper).updateTaskStateByIds(Arrays.asList(1L), TaskOperateEnums.STOP.getCode());
    }

    @Test
    @DisplayName("测试操作任务 - 参数验证失败")
    void testOperateTasks_InvalidParameters() {
        // 创建无效的测试参数
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setTaskIdList(Collections.emptyList());
        taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.operateTasks(taskStartOrStopDto, userDto);
        });
    }

    @Test
    @DisplayName("测试操作任务 - 任务列表为空")
    void testOperateTasks_EmptyTaskList() {
        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(Collections.emptyList());

        // 创建测试参数
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L));
        taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.operateTasks(taskStartOrStopDto, userDto);
        });
    }

    @Test
    @DisplayName("测试更新任务 - 成功")
    void testUpdateTask_Success() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            when(taskMapper.checkTaskExistsExcludeId(1L, 100L, "0 0 12 * * ?")).thenReturn(0);
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.updateTask(taskEntity)).thenReturn(1);

            // 执行测试方法
            int result = taskService.updateTask(taskDto);

            // 验证结果
            assertEquals(1, result);

            // 验证方法调用
            verify(taskMapper, times(1)).updateTask(taskEntity);
        }
    }

    @Test
    @DisplayName("测试批量删除任务 - 成功")
    void testDeleteTaskByIds_Success() {
        // 设置Mock行为
        when(taskMapper.deleteTaskByIds(new Long[]{1L, 2L})).thenReturn(2);

        // 执行测试方法
        int result = taskService.deleteTaskByIds(new Long[]{1L, 2L});

        // 验证结果
        assertEquals(2, result);

        // 验证方法调用
        verify(taskMapper, times(1)).deleteTaskByIds(new Long[]{1L, 2L});
    }

    @Test
    @DisplayName("测试检查任务是否存在 - 存在")
    void testCheckTaskExists_Exists() {
        // 设置Mock行为
        when(taskMapper.checkTaskExists(100L, "0 0 12 * * ?")).thenReturn(1);

        // 执行测试方法
        boolean result = taskService.checkTaskExists(100L, "0 0 12 * * ?");

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(taskMapper, times(1)).checkTaskExists(100L, "0 0 12 * * ?");
    }

    @Test
    @DisplayName("测试检查任务是否存在 - 不存在")
    void testCheckTaskExists_NotExists() {
        // 设置Mock行为
        when(taskMapper.checkTaskExists(100L, "0 0 12 * * ?")).thenReturn(0);

        // 执行测试方法
        boolean result = taskService.checkTaskExists(100L, "0 0 12 * * ?");

        // 验证结果
        assertFalse(result);
    }

    @Test
    @DisplayName("测试检查任务是否存在 - 参数为空")
    void testCheckTaskExists_NullParameters() {
        // 执行测试方法
        boolean result1 = taskService.checkTaskExists(null, "0 0 12 * * ?");
        boolean result2 = taskService.checkTaskExists(100L, null);
        boolean result3 = taskService.checkTaskExists(100L, "");

        // 验证结果
        assertFalse(result1);
        assertFalse(result2);
        assertFalse(result3);
    }

    @Test
    @DisplayName("测试检查任务是否存在（排除指定ID） - 存在")
    void testCheckTaskExistsExcludeId_Exists() {
        // 设置Mock行为
        when(taskMapper.checkTaskExistsExcludeId(1L, 100L, "0 0 12 * * ?")).thenReturn(1);

        // 执行测试方法
        boolean result = taskService.checkTaskExistsExcludeId(1L, 100L, "0 0 12 * * ?");

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(taskMapper, times(1)).checkTaskExistsExcludeId(1L, 100L, "0 0 12 * * ?");
    }

    @Test
    @DisplayName("测试更新任务cron表达式 - 成功")
    void testUpdateTaskCron_Success() throws ContrastBusinessException {
        // 准备测试数据
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 2 * * ?");

        // Mock方法调用
        when(taskMapper.selectTaskById(1L)).thenReturn(taskEntity);
        when(taskMapper.updateTaskCron(1L, "0 0 2 * * ?")).thenReturn(1);
        when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(true);
        doReturn(false).when(taskService).checkTaskExistsExcludeId(1L, taskEntity.getEnvcPlanId(), "0 0 2 * * ?");

        // 执行测试方法
        int result = taskService.updateTaskCron(taskCronUpdateDto, userDto);

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(taskMapper, times(1)).selectTaskById(1L);
        verify(taskMapper, times(1)).updateTaskCron(1L, "0 0 2 * * ?");
        verify(jobOperateService, times(1)).modifyJob(any(ContrastScheduleJobTaskDto.class));
    }

    @Test
    @DisplayName("测试查询任务方案列表 - 成功")
    void testSelectTaskPlanList_Success() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            when(taskMapper.selectTaskPlanList(any(TaskEntity.class))).thenReturn(taskPlanListBeanList);
            when(systemInteract.getCenterList(null)).thenReturn(centerList);
            mockedBeanUtils.when(() -> BeanUtils.copy(taskPlanListBeanList, TaskPlanListDto.class)).thenReturn(Arrays.asList(new TaskPlanListDto()));

            // 执行测试方法
            PageInfo<TaskPlanListDto> result = taskService.selectTaskPlanList(taskQueryDto, 1, 10);

            // 验证结果
            assertNotNull(result);

            // 验证方法调用
            verify(taskMapper, times(1)).selectTaskPlanList(any(TaskEntity.class));
            verify(systemInteract, times(1)).getCenterList(null);
        }
    }

    @Test
    @DisplayName("测试根据任务ID集合查询定时ID集合 - 成功")
    void testSelectScheduledIdsByTaskIds_Success() {
        // 设置Mock行为
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L, 2L))).thenReturn(Arrays.asList(123L, 456L));

        // 执行测试方法
        List<Long> result = taskService.selectScheduledIdsByTaskIds(Arrays.asList(1L, 2L));

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(123L, result.get(0));
        assertEquals(456L, result.get(1));

        // 验证方法调用
        verify(taskMapper, times(1)).selectScheduledIdsByTaskIds(Arrays.asList(1L, 2L));
    }

    @Test
    @DisplayName("测试根据任务ID集合查询定时ID集合 - 空列表")
    void testSelectScheduledIdsByTaskIds_EmptyList() {
        // 执行测试方法
        List<Long> result = taskService.selectScheduledIdsByTaskIds(Collections.emptyList());

        // 验证结果
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(taskMapper, never()).selectScheduledIdsByTaskIds(anyList());
    }

    @Test
    @DisplayName("测试更新任务定时ID和状态 - 成功")
    void testUpdateTaskScheduledIdAndState_Success() {
        // 设置Mock行为
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode())).thenReturn(1);

        // 执行测试方法
        int result = taskService.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());

        // 验证结果
        assertEquals(1, result);

        // 验证方法调用
        verify(taskMapper, times(1)).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());
    }

    @Test
    @DisplayName("测试更新任务定时ID和状态 - 任务ID为空")
    void testUpdateTaskScheduledIdAndState_NullTaskId() {
        // 执行测试方法
        int result = taskService.updateTaskScheduledIdAndState(null, 123L, TaskOperateEnums.START.getCode());

        // 验证结果
        assertEquals(0, result);

        // 验证方法调用
        verify(taskMapper, never()).updateTaskScheduledIdAndState(anyLong(), anyLong(), anyInt());
    }

    @Test
    @DisplayName("测试删除定时任务 - 成功")
    void testRemoveScheduleJob_Success() throws ContrastBusinessException {
        // 准备测试数据 - 确保任务状态为运行中
        TaskListBean task1 = new TaskListBean();
        task1.setId(1L);
        task1.setPlanName("测试方案1");
        task1.setCron("0 0 12 * * ?");
        task1.setScheduledId(123L);
        
        TaskListBean task2 = new TaskListBean();
        task2.setId(2L);
        task2.setPlanName("测试方案2");
        task2.setCron("0 0 2 * * ?");
        task2.setScheduledId(456L);
        
        List<TaskListBean> taskList = Arrays.asList(task1, task2);
        
        // 设置Mock行为
        when(taskMapper.selectTaskNameInfoByIds(Arrays.asList(1L, 2L))).thenReturn(taskList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L, 2L))).thenReturn(Arrays.asList(123L, 456L));
        when(taskMapper.selectTaskScheduledIdInfoByIds(Arrays.asList(1L, 2L))).thenReturn(taskList);
        when(jobOperateService.removeJob(123)).thenReturn(true);
        when(jobOperateService.removeJob(456)).thenReturn(true);
        when(taskMapper.deleteTaskByIds(new Long[]{1L, 2L})).thenReturn(2);

        // 执行测试方法
        TaskOperateResultDto result = taskService.removeScheduleJob(Arrays.asList(1L, 2L), userDto);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isAllSuccess());

        // 验证方法调用
        verify(taskMapper, times(1)).selectTaskNameInfoByIds(Arrays.asList(1L, 2L));
        verify(taskMapper, times(1)).selectTaskScheduledIdInfoByIds(Arrays.asList(1L, 2L));
        verify(jobOperateService, times(2)).removeJob(anyInt());
        verify(taskMapper, times(1)).deleteTaskByIds(new Long[]{1L, 2L});
    }

    @Test
    @DisplayName("测试查询任务详情 - 成功")
    void testSelectTaskDetailById_Success() throws ContrastBusinessException {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            when(taskMapper.selectTaskDetailById(1L)).thenReturn(taskListBean);
            mockedBeanUtils.when(() -> BeanUtils.copy(taskListBean, TaskDto.class)).thenReturn(taskDto);
            when(systemInteract.getCenterList(any(CenterQueryDto.class))).thenReturn(centerList);

            // 执行测试方法
            TaskDto result = taskService.selectTaskDetailById(1L);

            // 验证结果
            assertNotNull(result);

            // 验证方法调用
            verify(taskMapper, times(1)).selectTaskDetailById(1L);
            verify(systemInteract, times(1)).getCenterList(any(CenterQueryDto.class));
        }
    }

    @Test
    @DisplayName("测试查询任务详情 - 任务不存在")
    void testSelectTaskDetailById_NotFound() {
        // 设置Mock行为
        when(taskMapper.selectTaskDetailById(999L)).thenReturn(null);

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.selectTaskDetailById(999L);
        });
    }

    @Test
    @DisplayName("测试检查任务规则 - 参数验证失败")
    void testCheckTaskRule_InvalidParameters() {
        // 测试方案ID为空
        TaskDto invalidTask1 = new TaskDto();
        invalidTask1.setCron("0 0 12 * * ?");
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTask1);
        });

        // 测试cron表达式为空
        TaskDto invalidTask2 = new TaskDto();
        invalidTask2.setEnvcPlanId(100L);
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTask2);
        });

        // 测试任务已存在
        TaskDto invalidTask3 = new TaskDto();
        invalidTask3.setEnvcPlanId(100L);
        invalidTask3.setCron("0 0 12 * * ?");
        when(taskMapper.checkTaskExists(100L, "0 0 12 * * ?")).thenReturn(1);
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTask3);
        });
    }

    @Test
    @DisplayName("测试获取对比调度任务DTO - 成功")
    void testGetContrastScheduleJobTaskDto_Success() {
        // 执行测试方法
        ContrastScheduleJobTaskDto result = taskService.getContrastScheduleJobTaskDto(1L, taskDto, userDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getTaskId());
        assertEquals(100L, result.getPlanId());
        assertEquals("0 0 12 * * ?", result.getCron());
        assertEquals("对比任务_1", result.getTaskName());
        assertEquals(JobHandlerEnum.CONTRAST_JOB_HANDLER.getHandlerName(), result.getJobHandlerName());
        assertEquals(1L, result.getCreatorId());
        assertEquals("测试用户", result.getCreateName());
    }

    @Test
    @DisplayName("测试获取对比调度任务DTO - 任务DTO为空")
    void testGetContrastScheduleJobTaskDto_NullTaskDto() {
        // 执行测试方法
        ContrastScheduleJobTaskDto result = taskService.getContrastScheduleJobTaskDto(1L, null, userDto);

        // 验证结果
        assertNull(result);
    }

    @Test
    @DisplayName("测试创建任务 - 补偿机制成功场景")
    void testCreateTask_CompensationSuccess() throws Exception {
        // 准备测试数据
        TaskEntity savedTask = new TaskEntity();
        savedTask.setId(1L);
        savedTask.setEnvcPlanId(100L);
        savedTask.setCron("0 0 12 * * ?");
        savedTask.setCreatorId(1L);
        savedTask.setCreatorName("测试用户");
        savedTask.setUpdatorId(1L);
        savedTask.setUpdatorName("测试用户");
        savedTask.setEnabled(1);
        savedTask.setState(TaskOperateEnums.STOP.getCode());

        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode())).thenReturn(1);

        // 执行测试方法
        Long result = taskService.createTask(taskDto, userDto);

        // 验证结果
        assertEquals(1L, result);
        verify(taskMapper).insertTask(any(TaskEntity.class));
        verify(jobOperateService).createAndStartJob(any(ContrastScheduleJobTaskDto.class));
        verify(taskMapper).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());
    }

    @Test
    @DisplayName("测试创建任务 - 更新状态失败触发补偿机制")
    void testCreateTask_UpdateFailedWithCompensation() throws Exception {
        // 准备测试数据
        TaskEntity savedTask = new TaskEntity();
        savedTask.setId(1L);

        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenReturn(true);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));

        // 验证补偿操作被调用
        verify(jobOperateService).removeJob(123);
        verify(taskMapper, times(3)).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()); // 重试3次
    }

    @Test
    @DisplayName("测试创建任务 - 补偿操作失败")
    void testCreateTask_CompensationFailed() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenReturn(false);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));

        // 验证补偿操作被调用多次（重试机制）
        verify(jobOperateService, times(2)).removeJob(123); // 补偿重试2次
    }

    @Test
    @DisplayName("测试创建任务 - 补偿操作异常")
    void testCreateTask_CompensationException() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenThrow(new RuntimeException("补偿操作异常"));

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));

        // 验证补偿操作被调用
        verify(jobOperateService, times(2)).removeJob(123); // 补偿重试2次
    }

    @Test
    @DisplayName("测试创建任务 - 外部定时任务创建失败")
    void testCreateTask_ExternalJobCreationFailed() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class)))
                .thenThrow(new ScheduleJobOperateException("外部定时任务创建失败"));

        // 执行测试方法并验证异常
        ScheduleJobOperateException exception = assertThrows(ScheduleJobOperateException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertEquals("外部定时任务创建失败", exception.getMessage());

        // 验证没有调用补偿操作（因为外部任务创建失败，不需要补偿）
        verify(jobOperateService, never()).removeJob(anyInt());
        verify(taskMapper, never()).updateTaskScheduledIdAndState(anyLong(), anyLong(), anyInt());
    }

    @Test
    @DisplayName("测试创建任务 - 外部定时任务返回无效ID")
    void testCreateTask_ExternalJobInvalidId() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(0); // 返回无效ID

        // 执行测试方法并验证异常
        ScheduleJobOperateException exception = assertThrows(ScheduleJobOperateException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertEquals("创建任务失败", exception.getMessage());

        // 验证没有调用补偿操作
        verify(jobOperateService, never()).removeJob(anyInt());
        verify(taskMapper, never()).updateTaskScheduledIdAndState(anyLong(), anyLong(), anyInt());
    }

    @Test
    @DisplayName("测试创建任务 - 本地任务创建失败")
    void testCreateTask_LocalTaskCreationFailed() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenReturn(0); // 返回0表示插入失败

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertEquals("创建任务失败", exception.getMessage());

        // 验证没有调用外部服务和补偿操作
        verify(jobOperateService, never()).createAndStartJob(any(ContrastScheduleJobTaskDto.class));
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试创建任务 - 重试机制成功")
    void testCreateTask_RetrySuccess() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);

        // 第一次和第二次调用失败，第三次成功
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("第一次失败"))
                .thenThrow(new RuntimeException("第二次失败"))
                .thenReturn(1); // 第三次成功

        // 执行测试方法
        Long result = taskService.createTask(taskDto, userDto);

        // 验证结果
        assertEquals(1L, result);

        // 验证重试了3次
        verify(taskMapper, times(3)).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());

        // 验证没有调用补偿操作（因为最终成功了）
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试创建任务 - 线程中断场景")
    void testCreateTask_ThreadInterrupted() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenReturn(true);

        // 在测试执行前中断当前线程
        Thread.currentThread().interrupt();

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));

        // 验证线程中断状态被保持
        assertTrue(Thread.interrupted()); // 清除中断状态
    }

    @Test
    @DisplayName("测试创建任务 - cron表达式验证失败")
    void testCreateTask_InvalidCronExpression() throws Exception {
        // 准备无效的cron表达式
        taskDto.setCron("invalid cron");

        // Mock静态方法调用抛出异常
        try (MockedStatic mockedStatic = mockStatic(ContrastToolUtils.class)) {
            mockedStatic.when(() -> ContrastToolUtils.validateCronExpression("invalid cron"))
                    .thenThrow(new RuntimeException("无效的cron表达式"));

            // 执行测试方法并验证异常 - 期望RuntimeException被包装为ContrastBusinessException
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                taskService.createTask(taskDto, userDto);
            });

            // 验证异常信息
            assertTrue(exception.getMessage().contains("无效的cron表达式"));

            // 验证没有调用数据库操作
            verify(taskMapper, never()).insertTask(any(TaskEntity.class));
            verify(jobOperateService, never()).createAndStartJob(any(ContrastScheduleJobTaskDto.class));
        }
    }

    @Test
    @DisplayName("测试创建任务 - 任务规则验证失败")
    void testCreateTask_TaskRuleValidationFailed() throws Exception {
        // Mock方法调用
        doThrow(new ContrastBusinessException("任务规则验证失败")).when(taskService).checkTaskRule(taskDto);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertEquals("任务规则验证失败", exception.getMessage());

        // 验证没有调用后续操作
        verify(taskMapper, never()).insertTask(any(TaskEntity.class));
        verify(jobOperateService, never()).createAndStartJob(any(ContrastScheduleJobTaskDto.class));
    }

    @Test
    @DisplayName("测试创建任务 - 补偿操作部分成功")
    void testCreateTask_CompensationPartialSuccess() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));

        // 第一次补偿失败，第二次成功
        when(jobOperateService.removeJob(123))
                .thenReturn(false)  // 第一次失败
                .thenReturn(true);  // 第二次成功

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));

        // 验证补偿操作被调用2次
        verify(jobOperateService, times(2)).removeJob(123);
    }

    @Test
    @DisplayName("测试检查任务必须参数 - 参数验证失败")
    void testCheckTaskMustParam_InvalidParameters() {
        // 测试任务ID为空
        TaskDto invalidTask1 = new TaskDto();
        invalidTask1.setEnvcPlanId(100L);
        invalidTask1.setCron("0 0 12 * * ?");
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskMustParam(invalidTask1);
        });

        // 测试方案ID为空
        TaskDto invalidTask2 = new TaskDto();
        invalidTask2.setId(1L);
        invalidTask2.setCron("0 0 12 * * ?");
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskMustParam(invalidTask2);
        });

        // 测试cron表达式为空
        TaskDto invalidTask3 = new TaskDto();
        invalidTask3.setId(1L);
        invalidTask3.setEnvcPlanId(100L);
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskMustParam(invalidTask3);
        });
    }

    @Test
    @DisplayName("测试更新任务cron表达式 - 任务不存在")
    void testUpdateTaskCron_TaskNotFound() {
        // 准备测试数据
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(999L);
        taskCronUpdateDto.setCron("0 0 2 * * ?");

        // Mock方法调用
        when(taskMapper.selectTaskById(999L)).thenReturn(null);

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.updateTaskCron(taskCronUpdateDto, userDto);
        });
    }

    @Test
    @DisplayName("测试更新任务cron表达式 - 相同cron表达式已存在")
    void testUpdateTaskCron_ExistingCron() {
        // 准备测试数据
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 2 * * ?");

        // Mock方法调用
        when(taskMapper.selectTaskById(1L)).thenReturn(taskEntity);
        doReturn(true).when(taskService).checkTaskExistsExcludeId(1L, taskEntity.getEnvcPlanId(), "0 0 2 * * ?");

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.updateTaskCron(taskCronUpdateDto, userDto);
        });
    }

    @Test
    @DisplayName("测试更新任务cron表达式 - 更新表达式失败")
    void testUpdateTaskCron_ModifyJobFailed() {
        // 准备测试数据
        TaskCronUpdateDto taskCronUpdateDto = new TaskCronUpdateDto();
        taskCronUpdateDto.setId(1L);
        taskCronUpdateDto.setCron("0 0 2 * * ?");

        // Mock方法调用
        when(taskMapper.selectTaskById(1L)).thenReturn(taskEntity);
        doReturn(false).when(taskService).checkTaskExistsExcludeId(1L, taskEntity.getEnvcPlanId(), "0 0 2 * * ?");
        when(taskMapper.updateTaskCron(1L, "0 0 2 * * ?")).thenReturn(1);
        when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(false);

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.updateTaskCron(taskCronUpdateDto, userDto);
        });
    }

    @Test
    @DisplayName("测试删除定时任务 - 参数验证失败")
    void testRemoveScheduleJob_InvalidParameters() {
        // 测试任务ID列表为空
        TaskOperateResultDto result1 = taskService.removeScheduleJob(null, userDto);
        assertFalse(result1.isAllSuccess());
        assertEquals("任务ID列表不能为空", result1.getFailReason());

        // 测试用户信息为空
        TaskOperateResultDto result2 = taskService.removeScheduleJob(Arrays.asList(1L, 2L), null);
        assertFalse(result2.isAllSuccess());
        assertEquals("用户信息不能为空", result2.getFailReason());
    }

    @Test
    @DisplayName("测试删除定时任务 - 删除定时任务失败")
    void testRemoveScheduleJob_RemoveJobFailed() {
        // 准备测试数据
        TaskListBean task1 = new TaskListBean();
        task1.setId(1L);
        task1.setPlanName("测试方案1");
        task1.setCron("0 0 12 * * ?");
        task1.setScheduledId(123L);
        
        List<TaskListBean> taskList = Arrays.asList(task1);
        
        // 设置Mock行为
        when(taskMapper.selectTaskNameInfoByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskScheduledIdInfoByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(jobOperateService.removeJob(123)).thenReturn(false);

        // 执行测试方法
        TaskOperateResultDto result = taskService.removeScheduleJob(Arrays.asList(1L), userDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isAllSuccess());
        assertEquals(1, result.getFailedTaskIds().size());
        assertEquals(1L, result.getFailedTaskIds().get(0));
    }

    @Test
    @DisplayName("测试删除定时任务 - 删除任务记录失败")
    void testRemoveScheduleJob_DeleteTaskFailed() {
        // 准备测试数据
        TaskListBean task1 = new TaskListBean();
        task1.setId(1L);
        task1.setPlanName("测试方案1");
        task1.setCron("0 0 12 * * ?");
        task1.setScheduledId(123L);
        
        List<TaskListBean> taskList = Arrays.asList(task1);
        
        // 设置Mock行为
        when(taskMapper.selectTaskNameInfoByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskScheduledIdInfoByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(jobOperateService.removeJob(123)).thenReturn(true);
        when(taskMapper.deleteTaskByIds(new Long[]{1L})).thenReturn(0);

        // 执行测试方法
        TaskOperateResultDto result = taskService.removeScheduleJob(Arrays.asList(1L), userDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isAllSuccess());
        assertEquals("删除任务失败", result.getFailReason());
    }

    @Test
    @DisplayName("测试删除定时任务 - 删除任务记录异常")
    void testRemoveScheduleJob_DeleteTaskException() {
        // 准备测试数据
        TaskListBean task1 = new TaskListBean();
        task1.setId(1L);
        task1.setPlanName("测试方案1");
        task1.setCron("0 0 12 * * ?");
        task1.setScheduledId(123L);
        
        List<TaskListBean> taskList = Arrays.asList(task1);
        
        // 设置Mock行为
        when(taskMapper.selectTaskNameInfoByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskScheduledIdInfoByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(jobOperateService.removeJob(123)).thenReturn(true);
        when(taskMapper.deleteTaskByIds(any(Long[].class))).thenThrow(new RuntimeException("数据库异常"));

        // 执行测试方法
        TaskOperateResultDto result = taskService.removeScheduleJob(Arrays.asList(1L), userDto);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isAllSuccess());
        assertTrue(result.getFailReason().contains("删除任务异常"));
    }

    @Test
    @DisplayName("测试操作任务 - 没有找到对应的定时任务ID")
    void testOperateTasks_NoScheduledIdsFound() {
        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskListBeanList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Collections.emptyList());

        // 创建测试参数
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L));
        taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.operateTasks(taskStartOrStopDto, userDto);
        });
    }

    @Test
    @DisplayName("测试操作任务 - 启动任务失败")
    void testOperateTasks_StartFailed() {
        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskListBeanList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskByScheduleId(123L)).thenReturn(taskEntity);
        when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(true);
        when(jobOperateService.startJob(123)).thenReturn(false);

        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(taskEntity, TaskDto.class)).thenReturn(taskDto);

            // 创建测试参数
            TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
            taskStartOrStopDto.setTaskIdList(Arrays.asList(1L));
            taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

            // 执行测试方法并验证异常
            assertThrows(ContrastBusinessException.class, () -> {
                taskService.operateTasks(taskStartOrStopDto, userDto);
            });
        }
    }

    @Test
    @DisplayName("测试操作任务 - 停止任务失败")
    void testOperateTasks_StopFailed() {
        // 准备测试数据 - 确保任务状态为运行中
        TaskListBean runningTask = new TaskListBean();
        runningTask.setId(1L);
        runningTask.setEnvcPlanName("测试方案");
        runningTask.setCron("0 0 12 * * ?");
        runningTask.setState(TaskOperateEnums.START.getCode()); // 设置为运行状态
        runningTask.setScheduledId(123L);
        
        List<TaskListBean> taskList = Arrays.asList(runningTask);

        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskByScheduleId(123L)).thenReturn(taskEntity);
        when(jobOperateService.stopJob(123)).thenReturn(false);

        // 准备操作参数
        TaskStartOrStopDto operateDto = new TaskStartOrStopDto();
        operateDto.setTaskIdList(Arrays.asList(1L));
        operateDto.setOperateType(TaskOperateEnums.STOP.getCode());

        // 执行测试方法并验证异常
        assertThrows(ContrastBusinessException.class, () -> {
            taskService.operateTasks(operateDto, userDto);
        });
    }

    @Test
    @DisplayName("测试操作任务 - 更新任务状态失败")
    void testOperateTasks_UpdateStateFailed() {
        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskListBeanList);
        when(taskMapper.selectScheduledIdsByTaskIds(Arrays.asList(1L))).thenReturn(Arrays.asList(123L));
        when(taskMapper.selectTaskByScheduleId(123L)).thenReturn(taskEntity);
        when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(true);
        when(jobOperateService.startJob(123)).thenReturn(true);
        when(taskMapper.updateTaskStateByIds(Arrays.asList(1L), TaskOperateEnums.START.getCode())).thenReturn(0);

        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(taskEntity, TaskDto.class)).thenReturn(taskDto);

            // 创建测试参数
            TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
            taskStartOrStopDto.setTaskIdList(Arrays.asList(1L));
            taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

            // 执行测试方法并验证异常
            assertThrows(ContrastBusinessException.class, () -> {
                taskService.operateTasks(taskStartOrStopDto, userDto);
            });
        }
    }

    @Test
    @DisplayName("测试操作任务 - 所有任务已经处于目标状态")
    void testOperateTasks_AllTasksAlreadyInTargetState() {
        // 准备测试数据 - 确保任务状态为目标状态
        TaskListBean startedTask = new TaskListBean();
        startedTask.setId(1L);
        startedTask.setEnvcPlanName("测试方案");
        startedTask.setCron("0 0 12 * * ?");
        startedTask.setState(TaskOperateEnums.START.getCode()); // 设置为启动状态
        
        List<TaskListBean> taskList = Arrays.asList(startedTask);

        // 设置Mock行为
        when(taskMapper.selectTaskListByIds(Arrays.asList(1L))).thenReturn(taskList);

        // 创建测试参数 - 尝试启动已经启动的任务
        TaskStartOrStopDto taskStartOrStopDto = new TaskStartOrStopDto();
        taskStartOrStopDto.setTaskIdList(Arrays.asList(1L));
        taskStartOrStopDto.setOperateType(TaskOperateEnums.START.getCode());

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.operateTasks(taskStartOrStopDto, userDto);
        });
        
        // 验证异常消息包含特定内容
        assertTrue(exception.getMessage().contains("本次操作未执行"));
    }

    @Test
    @DisplayName("测试查询任务详情 - 查询异常")
    void testSelectTaskDetailById_Exception() {
        // 设置Mock行为
        when(taskMapper.selectTaskDetailById(1L)).thenThrow(new RuntimeException("数据库异常"));

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.selectTaskDetailById(1L);
        });
        
        // 验证异常消息包含特定内容
        assertTrue(exception.getMessage().contains("查询任务详情失败"));
    }

    @Test
    @DisplayName("测试更新任务状态 - 异常")
    void testUpdateTasksState_Exception() throws Exception {
        // 使用反射访问私有方法
        Method updateTasksStateMethod = TaskServiceImpl.class.getDeclaredMethod(
            "updateTasksState", List.class, Integer.class);
        updateTasksStateMethod.setAccessible(true);
        
        // 设置Mock行为
        when(taskMapper.updateTaskStateByIds(anyList(), anyInt())).thenThrow(new RuntimeException("数据库异常"));
        
        // 执行测试方法并验证异常
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            updateTasksStateMethod.invoke(taskService, Arrays.asList(1L), TaskOperateEnums.START.getCode());
        });
        
        // 验证InvocationTargetException的cause是ContrastBusinessException
        assertNotNull(exception.getCause());
        assertEquals(ContrastBusinessException.class, exception.getCause().getClass());
        assertTrue(exception.getCause().getMessage().contains("更新任务状态时发生异常"));
    }

    @Test
    @DisplayName("测试分离任务状态")
    void testSeparateTasksByTargetState() throws Exception {
        // 准备测试数据
        TaskListBean task1 = new TaskListBean();
        task1.setId(1L);
        task1.setState(TaskOperateEnums.START.getCode());
        
        TaskListBean task2 = new TaskListBean();
        task2.setId(2L);
        task2.setState(TaskOperateEnums.STOP.getCode());
        
        List<TaskListBean> taskList = Arrays.asList(task1, task2);
        
        // 使用反射访问私有方法
        Method separateTasksMethod = TaskServiceImpl.class.getDeclaredMethod(
            "separateTasksByTargetState", List.class, Integer.class);
        separateTasksMethod.setAccessible(true);
        
        // 执行测试方法 - 目标状态为启动
        Object result1 = separateTasksMethod.invoke(taskService, taskList, TaskOperateEnums.START.getCode());
        
        // 获取结果字段
        Field tasksToOperateField = result1.getClass().getDeclaredField("tasksToOperate");
        tasksToOperateField.setAccessible(true);
        List<TaskListBean> tasksToOperate1 = (List<TaskListBean>) tasksToOperateField.get(result1);
        
        Field tasksAlreadyInStateField = result1.getClass().getDeclaredField("tasksAlreadyInState");
        tasksAlreadyInStateField.setAccessible(true);
        List<TaskListBean> tasksAlreadyInState1 = (List<TaskListBean>) tasksAlreadyInStateField.get(result1);
        
        // 验证结果
        assertEquals(1, tasksToOperate1.size());
        assertEquals(2L, tasksToOperate1.get(0).getId());
        assertEquals(1, tasksAlreadyInState1.size());
        assertEquals(1L, tasksAlreadyInState1.get(0).getId());
        
        // 执行测试方法 - 目标状态为停止
        Object result2 = separateTasksMethod.invoke(taskService, taskList, TaskOperateEnums.STOP.getCode());
        
        // 获取结果字段
        List<TaskListBean> tasksToOperate2 = (List<TaskListBean>) tasksToOperateField.get(result2);
        List<TaskListBean> tasksAlreadyInState2 = (List<TaskListBean>) tasksAlreadyInStateField.get(result2);
        
        // 验证结果
        assertEquals(1, tasksToOperate2.size());
        assertEquals(1L, tasksToOperate2.get(0).getId());
        assertEquals(1, tasksAlreadyInState2.size());
        assertEquals(2L, tasksAlreadyInState2.get(0).getId());
    }

    @Test
    @DisplayName("测试获取任务名称或默认值")
    void testGetTaskNameOrDefault() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        
        // 使用反射访问私有方法
        Method getTaskNameMethod = TaskServiceImpl.class.getDeclaredMethod(
            "getTaskNameOrDefault", Map.class, Long.class);
        getTaskNameMethod.setAccessible(true);
        
        // 执行测试方法 - 存在的任务
        String result1 = (String) getTaskNameMethod.invoke(taskService, taskNameMap, 1L);
        
        // 验证结果
        assertEquals("测试任务1", result1);
        
        // 执行测试方法 - 不存在的任务
        String result2 = (String) getTaskNameMethod.invoke(taskService, taskNameMap, 999L);
        
        // 验证结果
        assertEquals("未知任务", result2);
    }

    @Test
    @DisplayName("测试添加成功和失败的任务")
    void testAddSuccessAndFailedTasks() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        taskNameMap.put(2L, "测试任务2");
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method addSuccessTasksMethod = TaskServiceImpl.class.getDeclaredMethod(
            "addSuccessTasks", List.class, Map.class, TaskOperateResultDto.class);
        addSuccessTasksMethod.setAccessible(true);
        
        Method addFailedTasksMethod = TaskServiceImpl.class.getDeclaredMethod(
            "addFailedTasks", List.class, Map.class, TaskOperateResultDto.class);
        addFailedTasksMethod.setAccessible(true);
        
        // 执行测试方法
        addSuccessTasksMethod.invoke(taskService, Arrays.asList(1L), taskNameMap, resultDto);
        addFailedTasksMethod.invoke(taskService, Arrays.asList(2L), taskNameMap, resultDto);
        
        // 验证结果
        assertEquals(1, resultDto.getSuccessTaskIds().size());
        assertEquals(1L, resultDto.getSuccessTaskIds().get(0));
        assertEquals("测试任务1", resultDto.getSuccessTaskNames().get(0));
        
        assertEquals(1, resultDto.getFailedTaskIds().size());
        assertEquals(2L, resultDto.getFailedTaskIds().get(0));
        assertEquals("测试任务2", resultDto.getFailedTaskNames().get(0));
    }

    @Test
    @DisplayName("测试处理任务删除结果")
    void testHandleTaskDeletionResult() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        taskNameMap.put(2L, "测试任务2");
        
        List<Long> tasksToDelete = Arrays.asList(1L, 2L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method handleTaskDeletionResultMethod = TaskServiceImpl.class.getDeclaredMethod(
            "handleTaskDeletionResult", int.class, List.class, Map.class, TaskOperateResultDto.class);
        handleTaskDeletionResultMethod.setAccessible(true);
        
        // 执行测试方法 - 删除成功
        handleTaskDeletionResultMethod.invoke(taskService, 2, tasksToDelete, taskNameMap, resultDto);
        
        // 验证结果
        assertEquals(2, resultDto.getSuccessTaskIds().size());
        assertTrue(resultDto.getSuccessTaskIds().contains(1L));
        assertTrue(resultDto.getSuccessTaskIds().contains(2L));
        
        // 重置结果对象
        resultDto = new TaskOperateResultDto();
        
        // 执行测试方法 - 删除失败
        handleTaskDeletionResultMethod.invoke(taskService, 0, tasksToDelete, taskNameMap, resultDto);
        
        // 验证结果
        assertEquals(2, resultDto.getFailedTaskIds().size());
        assertTrue(resultDto.getFailedTaskIds().contains(1L));
        assertTrue(resultDto.getFailedTaskIds().contains(2L));
        assertEquals("删除任务失败", resultDto.getFailReason());
    }

    @Test
    @DisplayName("测试验证删除定时任务的参数")
    void testValidateRemoveScheduleJobParameters() throws Exception {
        // 准备测试数据
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method validateMethod = TaskServiceImpl.class.getDeclaredMethod(
            "validateRemoveScheduleJobParameters", List.class, UserDto.class, TaskOperateResultDto.class);
        validateMethod.setAccessible(true);
        
        // 执行测试方法 - 有效参数
        boolean result1 = (boolean) validateMethod.invoke(taskService, Arrays.asList(1L), userDto, resultDto);
        
        // 验证结果
        assertTrue(result1);
        
        // 重置结果对象
        resultDto = new TaskOperateResultDto();
        
        // 执行测试方法 - 任务ID列表为空
        boolean result2 = (boolean) validateMethod.invoke(taskService, null, userDto, resultDto);
        
        // 验证结果
        assertFalse(result2);
        assertEquals("任务ID列表不能为空", resultDto.getFailReason());
        
        // 重置结果对象
        resultDto = new TaskOperateResultDto();
        
        // 执行测试方法 - 用户信息为空
        boolean result3 = (boolean) validateMethod.invoke(taskService, Arrays.asList(1L), null, resultDto);
        
        // 验证结果
        assertFalse(result3);
        assertEquals("用户信息不能为空", resultDto.getFailReason());
    }

    @Test
    @DisplayName("测试处理任务记录删除")
    void testProcessTaskDeletion() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        taskNameMap.put(2L, "测试任务2");
        
        List<Long> taskIdList = Arrays.asList(1L, 2L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        resultDto.addFailedTask(2L, "测试任务2"); // 任务2已经失败
        
        // 使用反射访问私有方法
        Method processTaskDeletionMethod = TaskServiceImpl.class.getDeclaredMethod(
            "processTaskDeletion", List.class, Map.class, TaskOperateResultDto.class);
        processTaskDeletionMethod.setAccessible(true);
        
        // 设置Mock行为 - 删除成功
        when(taskMapper.deleteTaskByIds(new Long[]{1L})).thenReturn(1);
        
        // 执行测试方法
        processTaskDeletionMethod.invoke(taskService, taskIdList, taskNameMap, resultDto);
        
        // 验证结果
        assertEquals(1, resultDto.getSuccessTaskIds().size());
        assertEquals(1L, resultDto.getSuccessTaskIds().get(0));
        assertEquals(1, resultDto.getFailedTaskIds().size());
        assertEquals(2L, resultDto.getFailedTaskIds().get(0));
        
        // 验证方法调用
        verify(taskMapper, times(1)).deleteTaskByIds(new Long[]{1L});
    }

    @Test
    @DisplayName("测试处理定时任务删除")
    void testProcessScheduleJobDeletion() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        
        List<Long> taskIdList = Arrays.asList(1L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method processScheduleJobDeletionMethod = TaskServiceImpl.class.getDeclaredMethod(
            "processScheduleJobDeletion", List.class, Map.class, TaskOperateResultDto.class);
        processScheduleJobDeletionMethod.setAccessible(true);
        
        // 设置Mock行为
        when(taskMapper.selectScheduledIdsByTaskIds(taskIdList)).thenReturn(Arrays.asList(123L));
        
        TaskListBean taskListBean = new TaskListBean();
        taskListBean.setId(1L);
        taskListBean.setScheduledId(123L);
        when(taskMapper.selectTaskScheduledIdInfoByIds(taskIdList)).thenReturn(Arrays.asList(taskListBean));
        
        when(jobOperateService.removeJob(123)).thenReturn(true);
        
        // 执行测试方法
        processScheduleJobDeletionMethod.invoke(taskService, taskIdList, taskNameMap, resultDto);
        
        // 验证方法调用
        verify(taskMapper, times(1)).selectScheduledIdsByTaskIds(taskIdList);
        verify(taskMapper, times(1)).selectTaskScheduledIdInfoByIds(taskIdList);
        verify(jobOperateService, times(1)).removeJob(123);
    }

    @Test
    @DisplayName("测试处理定时任务删除 - 没有定时ID")
    void testProcessScheduleJobDeletion_NoScheduledIds() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        
        List<Long> taskIdList = Arrays.asList(1L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method processScheduleJobDeletionMethod = TaskServiceImpl.class.getDeclaredMethod(
            "processScheduleJobDeletion", List.class, Map.class, TaskOperateResultDto.class);
        processScheduleJobDeletionMethod.setAccessible(true);
        
        // 设置Mock行为 - 没有定时ID
        when(taskMapper.selectScheduledIdsByTaskIds(taskIdList)).thenReturn(Collections.emptyList());
        
        // 执行测试方法
        processScheduleJobDeletionMethod.invoke(taskService, taskIdList, taskNameMap, resultDto);
        
        // 验证方法调用
        verify(taskMapper, times(1)).selectScheduledIdsByTaskIds(taskIdList);
        verify(taskMapper, never()).selectTaskScheduledIdInfoByIds(anyList());
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试删除定时任务")
    void testRemoveScheduleJobs() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        taskNameMap.put(2L, "测试任务2");
        
        Map<Long, Long> taskToScheduledMap = new HashMap<>();
        taskToScheduledMap.put(1L, 123L);
        taskToScheduledMap.put(2L, 456L);
        
        List<Long> taskIdList = Arrays.asList(1L, 2L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method removeScheduleJobsMethod = TaskServiceImpl.class.getDeclaredMethod(
            "removeScheduleJobs", List.class, Map.class, Map.class, TaskOperateResultDto.class);
        removeScheduleJobsMethod.setAccessible(true);
        
        // 设置Mock行为
        when(jobOperateService.removeJob(123)).thenReturn(true);
        when(jobOperateService.removeJob(456)).thenReturn(false);
        
        // 执行测试方法
        removeScheduleJobsMethod.invoke(taskService, taskIdList, taskNameMap, taskToScheduledMap, resultDto);
        
        // 验证结果
        assertEquals(1, resultDto.getFailedTaskIds().size());
        assertEquals(2L, resultDto.getFailedTaskIds().get(0));
        
        // 验证方法调用
        verify(jobOperateService, times(1)).removeJob(123);
        verify(jobOperateService, times(1)).removeJob(456);
    }

    @Test
    @DisplayName("测试删除定时任务 - 无效的定时任务ID")
    void testRemoveScheduleJobs_InvalidScheduledId() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        
        Map<Long, Long> taskToScheduledMap = new HashMap<>();
        taskToScheduledMap.put(1L, -1L); // 无效的定时任务ID
        
        List<Long> taskIdList = Arrays.asList(1L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method removeScheduleJobsMethod = TaskServiceImpl.class.getDeclaredMethod(
            "removeScheduleJobs", List.class, Map.class, Map.class, TaskOperateResultDto.class);
        removeScheduleJobsMethod.setAccessible(true);
        
        // 执行测试方法
        removeScheduleJobsMethod.invoke(taskService, taskIdList, taskNameMap, taskToScheduledMap, resultDto);
        
        // 验证方法调用
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试删除定时任务 - 没有对应的定时任务ID")
    void testRemoveScheduleJobs_NoScheduledId() throws Exception {
        // 准备测试数据
        Map<Long, String> taskNameMap = new HashMap<>();
        taskNameMap.put(1L, "测试任务1");
        
        Map<Long, Long> taskToScheduledMap = new HashMap<>();
        // 没有为任务1设置定时任务ID
        
        List<Long> taskIdList = Arrays.asList(1L);
        
        TaskOperateResultDto resultDto = new TaskOperateResultDto();
        
        // 使用反射访问私有方法
        Method removeScheduleJobsMethod = TaskServiceImpl.class.getDeclaredMethod(
            "removeScheduleJobs", List.class, Map.class, Map.class, TaskOperateResultDto.class);
        removeScheduleJobsMethod.setAccessible(true);
        
        // 执行测试方法
        removeScheduleJobsMethod.invoke(taskService, taskIdList, taskNameMap, taskToScheduledMap, resultDto);
        
        // 验证方法调用
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试验证操作任务的参数")
    void testValidateOperateTasksParameters() throws Exception {
        // 使用反射访问私有方法
        Method validateMethod = TaskServiceImpl.class.getDeclaredMethod(
            "validateOperateTasksParameters", TaskStartOrStopDto.class, UserDto.class);
        validateMethod.setAccessible(true);
        
        // 准备有效参数
        TaskStartOrStopDto validDto = new TaskStartOrStopDto();
        validDto.setTaskIdList(Arrays.asList(1L));
        validDto.setOperateType(TaskOperateEnums.START.getCode());
        
        // 执行测试方法 - 有效参数
        validateMethod.invoke(taskService, validDto, userDto);
        
        // 准备无效参数 - DTO为空
        InvocationTargetException exception1 = assertThrows(InvocationTargetException.class, () -> {
            validateMethod.invoke(taskService, null, userDto);
        });
        assertNotNull(exception1.getCause());
        assertEquals(ContrastBusinessException.class, exception1.getCause().getClass());
        assertTrue(exception1.getCause().getMessage().contains("任务操作参数为空"));
        
        // 准备无效参数 - 操作类型无效
        TaskStartOrStopDto invalidTypeDto = new TaskStartOrStopDto();
        invalidTypeDto.setTaskIdList(Arrays.asList(1L));
        invalidTypeDto.setOperateType(999); // 无效的操作类型
        
        InvocationTargetException exception2 = assertThrows(InvocationTargetException.class, () -> {
            validateMethod.invoke(taskService, invalidTypeDto, userDto);
        });
        assertNotNull(exception2.getCause());
        assertEquals(ContrastBusinessException.class, exception2.getCause().getClass());
        assertTrue(exception2.getCause().getMessage().contains("操作类型不在范围内"));
        
        // 准备无效参数 - 任务ID列表为空
        TaskStartOrStopDto emptyListDto = new TaskStartOrStopDto();
        emptyListDto.setTaskIdList(Collections.emptyList());
        emptyListDto.setOperateType(TaskOperateEnums.START.getCode());
        
        InvocationTargetException exception3 = assertThrows(InvocationTargetException.class, () -> {
            validateMethod.invoke(taskService, emptyListDto, userDto);
        });
        assertNotNull(exception3.getCause());
        assertEquals(ContrastBusinessException.class, exception3.getCause().getClass());
        assertTrue(exception3.getCause().getMessage().contains("任务ID列表不能为空"));
        
        // 准备无效参数 - 用户信息为空
        InvocationTargetException exception4 = assertThrows(InvocationTargetException.class, () -> {
            validateMethod.invoke(taskService, validDto, null);
        });
        assertNotNull(exception4.getCause());
        assertEquals(ContrastBusinessException.class, exception4.getCause().getClass());
        assertTrue(exception4.getCause().getMessage().contains("用户信息不能为空"));
    }

    @Test
    @DisplayName("测试执行任务操作 - 不支持的操作类型")
    void testExecuteTaskOperation_UnsupportedType() throws Exception {
        // 使用反射访问私有方法
        Method executeTaskOperationMethod = TaskServiceImpl.class.getDeclaredMethod(
            "executeTaskOperation", Integer.class, List.class, UserDto.class);
        executeTaskOperationMethod.setAccessible(true);
        
        // 执行测试方法 - 不支持的操作类型
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            executeTaskOperationMethod.invoke(taskService, 999, Arrays.asList(123L), userDto);
        });
        
        assertNotNull(exception.getCause());
        assertEquals(ContrastBusinessException.class, exception.getCause().getClass());
        assertTrue(exception.getCause().getMessage().contains("不支持的操作类型"));
    }

    @Test
    @DisplayName("测试处理已经处于目标状态的任务")
    void testHandleTasksAlreadyInTargetState() throws Exception {
        // 准备测试数据
        TaskListBean task1 = new TaskListBean();
        task1.setId(1L);
        task1.setEnvcPlanName("测试方案1");
        
        TaskListBean task2 = new TaskListBean();
        task2.setId(2L);
        task2.setEnvcPlanName("测试方案2");
        
        List<TaskListBean> tasksAlreadyInState = Arrays.asList(task1, task2);
        
        // 使用反射访问私有方法
        Method handleTasksMethod = TaskServiceImpl.class.getDeclaredMethod(
            "handleTasksAlreadyInTargetState", List.class, Integer.class, boolean.class);
        handleTasksMethod.setAccessible(true);
        
        // 执行测试方法 - 所有任务都已经是目标状态
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            handleTasksMethod.invoke(taskService, tasksAlreadyInState, TaskOperateEnums.START.getCode(), true);
        });
        
        assertNotNull(exception.getCause());
        assertEquals(ContrastBusinessException.class, exception.getCause().getClass());
        assertTrue(exception.getCause().getMessage().contains("本次操作未执行"));
        
        // 执行测试方法 - 部分任务已经是目标状态
        handleTasksMethod.invoke(taskService, tasksAlreadyInState, TaskOperateEnums.START.getCode(), false);
    }

    @Test
    @DisplayName("测试修改任务 - 更新表达式失败")
    void testModifyTask_UpdateExpressionFailed() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 修改cron表达式
            TaskDto modifiedTaskDto = new TaskDto();
            modifiedTaskDto.setId(1L);
            modifiedTaskDto.setEnvcPlanId(100L);
            modifiedTaskDto.setCron("0 0 2 * * ?"); // 不同于原来的cron表达式

            // 设置原始任务实体
            TaskEntity originalEntity = new TaskEntity();
            originalEntity.setId(1L);
            originalEntity.setEnvcPlanId(100L);
            originalEntity.setCron("0 0 12 * * ?"); // 原始cron表达式

            // 设置修改后的任务实体
            TaskEntity modifiedEntity = new TaskEntity();
            modifiedEntity.setId(1L);
            modifiedEntity.setEnvcPlanId(100L);
            modifiedEntity.setCron("0 0 2 * * ?");

            // 设置Mock行为
            when(taskMapper.selectTaskById(1L)).thenReturn(originalEntity);
            when(taskMapper.checkTaskExistsExcludeId(1L, 100L, "0 0 2 * * ?")).thenReturn(0);
            mockedBeanUtils.when(() -> BeanUtils.copy(modifiedTaskDto, TaskEntity.class)).thenReturn(modifiedEntity);
            when(taskMapper.updateTask(modifiedEntity)).thenReturn(1);
            when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(false);

            // 执行测试方法并验证异常
            assertThrows(ContrastBusinessException.class, () -> {
                taskService.modifyTask(modifiedTaskDto, userDto);
            });
        }
    }

    @Test
    @DisplayName("测试异常类型具体化 - checkTaskRule方法参数验证")
    void testCheckTaskRule_ParameterValidation() {
        // 测试taskDto为null
        ContrastBusinessException exception1 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(null);
        });
        assertEquals("任务参数不能为空", exception1.getMessage());

        // 测试方案ID为null
        TaskDto invalidTaskDto1 = new TaskDto();
        invalidTaskDto1.setEnvcPlanId(null);
        invalidTaskDto1.setCron("0 0 12 * * ?");

        ContrastBusinessException exception2 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTaskDto1);
        });
        assertEquals("任务方案ID不能为空", exception2.getMessage());

        // 测试方案ID为0
        TaskDto invalidTaskDto2 = new TaskDto();
        invalidTaskDto2.setEnvcPlanId(0L);
        invalidTaskDto2.setCron("0 0 12 * * ?");

        ContrastBusinessException exception3 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTaskDto2);
        });
        assertEquals("任务方案ID不能为空", exception3.getMessage());

        // 测试cron表达式为null
        TaskDto invalidTaskDto3 = new TaskDto();
        invalidTaskDto3.setEnvcPlanId(100L);
        invalidTaskDto3.setCron(null);

        ContrastBusinessException exception4 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTaskDto3);
        });
        assertEquals("任务cron表达式不能为空", exception4.getMessage());

        // 测试cron表达式为空字符串
        TaskDto invalidTaskDto4 = new TaskDto();
        invalidTaskDto4.setEnvcPlanId(100L);
        invalidTaskDto4.setCron("   ");

        ContrastBusinessException exception5 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(invalidTaskDto4);
        });
        assertEquals("任务cron表达式不能为空", exception5.getMessage());
    }

    @Test
    @DisplayName("测试异常类型具体化 - checkTaskRule任务已存在")
    void testCheckTaskRule_TaskExists() {
        // 准备测试数据
        TaskDto validTaskDto = new TaskDto();
        validTaskDto.setEnvcPlanId(100L);
        validTaskDto.setCron("0 0 12 * * ?");

        // Mock任务已存在
        when(taskMapper.checkTaskExists(100L, "0 0 12 * * ?")).thenReturn(1);

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(validTaskDto);
        });
        assertEquals("同一方案下已存在相同cron表达式的任务，请重新输入", exception.getMessage());
    }

    @Test
    @DisplayName("测试异常类型具体化 - checkTaskRule数据库异常")
    void testCheckTaskRule_DatabaseException() {
        // 准备测试数据
        TaskDto validTaskDto = new TaskDto();
        validTaskDto.setEnvcPlanId(100L);
        validTaskDto.setCron("0 0 12 * * ?");

        // Mock数据库异常
        when(taskMapper.checkTaskExists(100L, "0 0 12 * * ?")).thenThrow(new RuntimeException("数据库连接失败"));

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskRule(validTaskDto);
        });
        assertTrue(exception.getMessage().contains("检查任务规则时发生系统异常"));
        assertTrue(exception.getCause() instanceof RuntimeException);
    }

    @Test
    @DisplayName("测试异常类型具体化 - checkTaskMustParam方法参数验证")
    void testCheckTaskMustParam_ParameterValidation() {
        // 测试taskDto为null
        ContrastBusinessException exception1 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskMustParam(null);
        });
        assertEquals("任务参数不能为空", exception1.getMessage());

        // 测试任务ID为null
        TaskDto invalidTaskDto1 = new TaskDto();
        invalidTaskDto1.setId(null);
        invalidTaskDto1.setEnvcPlanId(100L);
        invalidTaskDto1.setCron("0 0 12 * * ?");

        ContrastBusinessException exception2 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskMustParam(invalidTaskDto1);
        });
        assertEquals("任务ID不能为空", exception2.getMessage());

        // 测试任务ID为0
        TaskDto invalidTaskDto2 = new TaskDto();
        invalidTaskDto2.setId(0L);
        invalidTaskDto2.setEnvcPlanId(100L);
        invalidTaskDto2.setCron("0 0 12 * * ?");

        ContrastBusinessException exception3 = assertThrows(ContrastBusinessException.class, () -> {
            taskService.checkTaskMustParam(invalidTaskDto2);
        });
        assertEquals("任务ID不能为空", exception3.getMessage());
    }

    @Test
    @DisplayName("测试insertTask方法异常处理 - 数据库异常")
    void testInsertTask_DatabaseException() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.insertTask(taskEntity)).thenThrow(new RuntimeException("数据库连接失败"));

            // 执行测试方法并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                taskService.insertTask(taskDto);
            });

            // 验证异常信息
            assertTrue(exception.getMessage().contains("新增任务时发生系统异常"));
            assertTrue(exception.getCause() instanceof RuntimeException);
        }
    }

    @Test
    @DisplayName("测试insertTask方法异常处理 - checkTaskRule失败")
    void testInsertTask_CheckTaskRuleFailed() throws ContrastBusinessException {
        // 设置Mock行为 - checkTaskRule抛出业务异常
        doThrow(new ContrastBusinessException("任务方案ID不能为空")).when(taskService).checkTaskRule(taskDto);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.insertTask(taskDto);
        });

        // 验证异常信息
        assertEquals("任务方案ID不能为空", exception.getMessage());

        // 验证没有调用数据库操作
        verify(taskMapper, never()).insertTask(any(TaskEntity.class));
    }

    @Test
    @DisplayName("测试updateTask方法异常处理 - 数据库异常")
    void testUpdateTask_DatabaseException() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 设置Mock行为
            mockedBeanUtils.when(() -> BeanUtils.copy(taskDto, TaskEntity.class)).thenReturn(taskEntity);
            when(taskMapper.updateTask(taskEntity)).thenThrow(new RuntimeException("数据库更新失败"));

            // 执行测试方法并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                taskService.updateTask(taskDto);
            });

            // 验证异常信息
            assertTrue(exception.getMessage().contains("修改任务时发生系统异常"));
            assertTrue(exception.getCause() instanceof RuntimeException);
        }
    }

    @Test
    @DisplayName("测试updateTask方法异常处理 - 任务重复")
    void testUpdateTask_TaskDuplicate() {
        // 准备测试数据
        taskDto.setId(1L);
        taskDto.setEnvcPlanId(100L);
        taskDto.setCron("0 0 12 * * ?");

        // Mock任务重复检查 - checkTaskExistsExcludeId返回int，大于0表示存在
        when(taskMapper.checkTaskExistsExcludeId(1L, 100L, "0 0 12 * * ?")).thenReturn(1);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.updateTask(taskDto);
        });

        // 验证异常信息
        assertEquals("同一方案下已存在相同cron表达式的任务，请重新输入", exception.getMessage());

        // 验证没有调用数据库更新操作
        verify(taskMapper, never()).updateTask(any(TaskEntity.class));
    }

    @Test
    @DisplayName("测试modifyTask方法异常处理 - 任务表达式更新失败")
    void testModifyTask_JobModifyFailed() {
        try (MockedStatic<BeanUtils> mockedBeanUtils = mockStatic(BeanUtils.class)) {
            // 准备测试数据
            TaskDto modifiedTaskDto = new TaskDto();
            modifiedTaskDto.setId(1L);
            modifiedTaskDto.setEnvcPlanId(100L);
            modifiedTaskDto.setCron("0 0 14 * * ?"); // 修改后的cron表达式

            TaskEntity originalEntity = new TaskEntity();
            originalEntity.setId(1L);
            originalEntity.setEnvcPlanId(100L);
            originalEntity.setCron("0 0 12 * * ?"); // 原始cron表达式

            TaskEntity modifiedEntity = new TaskEntity();
            modifiedEntity.setId(1L);
            modifiedEntity.setEnvcPlanId(100L);
            modifiedEntity.setCron("0 0 14 * * ?");

            // 设置Mock行为
            when(taskMapper.selectTaskById(1L)).thenReturn(originalEntity);
            when(taskMapper.checkTaskExistsExcludeId(1L, 100L, "0 0 14 * * ?")).thenReturn(0);
            mockedBeanUtils.when(() -> BeanUtils.copy(modifiedTaskDto, TaskEntity.class)).thenReturn(modifiedEntity);
            when(taskMapper.updateTask(modifiedEntity)).thenReturn(1);
            when(jobOperateService.modifyJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(false);

            // 执行测试方法并验证异常
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
                taskService.modifyTask(modifiedTaskDto, userDto);
            });

            // 验证异常信息
            assertEquals("更新任务表达式失败", exception.getMessage());
        }
    }
}