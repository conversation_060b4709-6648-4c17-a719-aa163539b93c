package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.PlanMapper;
import com.ideal.envc.model.dto.PlanDto;
import com.ideal.envc.model.dto.PlanQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.PlanEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.mockito.MockedStatic;

/**
 * PlanServiceImpl单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("方案Service实现类测试")
class PlanServiceImplTest {

    @Mock
    private PlanMapper planMapper;

    private PlanServiceImpl planService;

    private UserDto userDto;
    private PlanDto planDto;
    private PlanEntity planEntity;
    private PlanQueryDto planQueryDto;

    @BeforeEach
    void setUp() {
        planService = new PlanServiceImpl(planMapper);

        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("testUser");
        userDto.setFullName("测试用户");

        // 初始化方案DTO
        planDto = new PlanDto();
        planDto.setId(1L);
        planDto.setName("测试方案");
        planDto.setPlanDesc("测试方案描述");

        // 初始化方案实体
        planEntity = new PlanEntity();
        planEntity.setId(1L);
        planEntity.setName("测试方案");
        planEntity.setPlanDesc("测试方案描述");
        planEntity.setCreatorId(1L);
        planEntity.setCreatorName("测试用户");

        // 初始化查询条件
        planQueryDto = new PlanQueryDto();
        planQueryDto.setName("测试方案");
    }

    @Test
    @DisplayName("根据ID查询方案-成功")
    void testSelectPlanById_Success() {
        // given
        Long id = 1L;
        when(planMapper.selectPlanById(id)).thenReturn(planEntity);

        // when
        PlanDto result = planService.selectPlanById(id);

        // then
        assertNotNull(result);
        assertEquals(planEntity.getId(), result.getId());
        assertEquals(planEntity.getName(), result.getName());

        verify(planMapper).selectPlanById(id);
    }

    @Test
    @DisplayName("根据ID查询方案-数据不存在")
    void testSelectPlanById_NotFound() {
        // given
        Long id = 999L;
        when(planMapper.selectPlanById(id)).thenReturn(null);

        // when
        PlanDto result = planService.selectPlanById(id);

        // then
        // BeanUtils.copy在源对象为null时会返回一个新的空对象，而不是null
        assertNotNull(result);
        assertNull(result.getId());
        assertNull(result.getName());

        verify(planMapper).selectPlanById(id);
    }

    @Test
    @DisplayName("查询方案列表-成功")
    void testSelectPlanList_Success() {
        // given
        List<PlanEntity> mockEntities = new ArrayList<>();
        mockEntities.add(planEntity);
        
        // 使用try-with-resources来mock静态方法PageMethod.startPage
        try (MockedStatic<com.github.pagehelper.page.PageMethod> mockedPageMethod = mockStatic(com.github.pagehelper.page.PageMethod.class);
             MockedStatic<com.ideal.common.util.PageDataUtil> mockedPageDataUtil = mockStatic(com.ideal.common.util.PageDataUtil.class)) {
            
            // 创建Page对象而不是ArrayList
            com.github.pagehelper.Page<PlanEntity> page = new com.github.pagehelper.Page<>(1, 10);
            page.addAll(mockEntities);
            page.setTotal(1);
            
            when(planMapper.selectPlanList(any(PlanEntity.class))).thenReturn(page);
            
            // Mock PageDataUtil.toDtoPage方法
            PageInfo<PlanDto> expectedPageInfo = new PageInfo<>();
            List<PlanDto> dtoList = new ArrayList<>();
            PlanDto dto = new PlanDto();
            dto.setId(1L);
            dto.setName("测试方案");
            dtoList.add(dto);
            expectedPageInfo.setList(dtoList);
            expectedPageInfo.setTotal(1);
            
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(PlanDto.class)))
                    .thenReturn(expectedPageInfo);

            // when
            PageInfo<PlanDto> result = planService.selectPlanList(planQueryDto, 1, 10);

            // then
            assertNotNull(result);
            assertNotNull(result.getList());
            assertFalse(result.getList().isEmpty());

            verify(planMapper).selectPlanList(any(PlanEntity.class));
        }
    }

    @ParameterizedTest
    @MethodSource("provideInsertPlanScenarios")
    @DisplayName("新增方案")
    void testInsertPlan(PlanDto dto, UserDto user, Integer nameExistsCount, Integer mockResult,
                       Class<? extends Exception> expectedException, String expectedMessage) {
        // given
        if (dto != null && dto.getName() != null && !dto.getName().trim().isEmpty()) {
            when(planMapper.checkPlanNameExists(dto.getName().trim())).thenReturn(nameExistsCount != null ? nameExistsCount : 0);
        }
        
        if (mockResult != null) {
            when(planMapper.insertPlan(any(PlanEntity.class))).thenReturn(mockResult);
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planService.insertPlan(dto, user));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                int result = planService.insertPlan(dto, user);
                assertEquals(mockResult.intValue(), result);
            });
        }

        // verify
        if (dto != null && dto.getName() != null && !dto.getName().trim().isEmpty() && 
            (nameExistsCount == null || nameExistsCount == 0) && mockResult != null) {
            verify(planMapper).insertPlan(any(PlanEntity.class));
        }
    }

    static Stream<Arguments> provideInsertPlanScenarios() {
        PlanDto validDto = new PlanDto();
        validDto.setName("测试方案");
        validDto.setPlanDesc("测试描述");

        PlanDto emptyNameDto = new PlanDto();
        emptyNameDto.setName("");

        PlanDto nullNameDto = new PlanDto();
        nullNameDto.setName(null);

        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        return Stream.of(
                // DTO为null的情况
                Arguments.of(null, validUser, null, null, ContrastBusinessException.class, "方案信息不能为空"),
                // 方案名称为null的情况
                Arguments.of(nullNameDto, validUser, null, null, ContrastBusinessException.class, "方案名称不能为空"),
                // 方案名称为空字符串的情况
                Arguments.of(emptyNameDto, validUser, null, null, ContrastBusinessException.class, "方案名称不能为空"),
                // 方案名称已存在的情况
                Arguments.of(validDto, validUser, 1, null, ContrastBusinessException.class, "方案名称已存在"),
                // 新增成功的情况
                Arguments.of(validDto, validUser, 0, 1, null, null)
        );
    }

    @Test
    @DisplayName("新增方案-系统异常")
    void testInsertPlan_SystemException() {
        // given
        when(planMapper.checkPlanNameExists(planDto.getName())).thenReturn(0);
        when(planMapper.insertPlan(any(PlanEntity.class))).thenThrow(new RuntimeException("数据库异常"));

        // when & then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> planService.insertPlan(planDto, userDto));
        assertTrue(exception.getMessage().contains("新增方案失败"));

        verify(planMapper).checkPlanNameExists(planDto.getName());
        verify(planMapper).insertPlan(any(PlanEntity.class));
    }

    @ParameterizedTest
    @MethodSource("provideUpdatePlanScenarios")
    @DisplayName("修改方案")
    void testUpdatePlan(PlanDto dto, UserDto user, PlanEntity existingEntity, Integer nameExistsCount,
                       Integer mockResult, Class<? extends Exception> expectedException, String expectedMessage) {
        // given - 只在需要时设置mock
        if (dto != null && dto.getId() != null) {
            when(planMapper.selectPlanById(dto.getId())).thenReturn(existingEntity);
            
            // 只在实体存在且名称不为空时设置名称检查mock
            if (existingEntity != null && dto.getName() != null && !dto.getName().trim().isEmpty()) {
                when(planMapper.checkPlanNameExistsExcludeId(dto.getName().trim(), dto.getId()))
                        .thenReturn(nameExistsCount != null ? nameExistsCount : 0);
                
                // 只在名称不存在且没有异常时设置更新mock
                if ((nameExistsCount == null || nameExistsCount == 0) && expectedException == null && mockResult != null) {
                    when(planMapper.updatePlan(any(PlanEntity.class))).thenReturn(mockResult);
                }
            }
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planService.updatePlan(dto, user));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                int result = planService.updatePlan(dto, user);
                assertEquals(mockResult.intValue(), result);
            });
        }

        // verify
        if (dto != null && dto.getId() != null && existingEntity != null && 
            (nameExistsCount == null || nameExistsCount == 0) && expectedException == null && mockResult != null) {
            verify(planMapper).updatePlan(any(PlanEntity.class));
        }
    }

    static Stream<Arguments> provideUpdatePlanScenarios() {
        PlanDto validDto = new PlanDto();
        validDto.setId(1L);
        validDto.setName("测试方案");
        validDto.setPlanDesc("测试描述");

        PlanDto nullIdDto = new PlanDto();
        nullIdDto.setName("测试方案");

        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        PlanEntity existingEntity = new PlanEntity();
        existingEntity.setId(1L);
        existingEntity.setName("原方案名称");

        return Stream.of(
                // DTO为null的情况
                Arguments.of(null, validUser, null, null, null, ContrastBusinessException.class, "方案信息不能为空"),
                // ID为null的情况
                Arguments.of(nullIdDto, validUser, null, null, null, ContrastBusinessException.class, "方案ID不能为空"),
                // 方案不存在的情况
                Arguments.of(validDto, validUser, null, null, null, ContrastBusinessException.class, "方案不存在"),
                // 方案名称已存在的情况
                Arguments.of(validDto, validUser, existingEntity, 1, null, ContrastBusinessException.class, "方案名称已存在"),
                // 修改成功的情况
                Arguments.of(validDto, validUser, existingEntity, 0, 1, null, null)
        );
    }

    @Test
    @DisplayName("修改方案-系统异常")
    void testUpdatePlan_SystemException() {
        // given
        when(planMapper.selectPlanById(planDto.getId())).thenReturn(planEntity);
        when(planMapper.checkPlanNameExistsExcludeId(planDto.getName(), planDto.getId())).thenReturn(0);
        when(planMapper.updatePlan(any(PlanEntity.class))).thenThrow(new RuntimeException("数据库异常"));

        // when & then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> planService.updatePlan(planDto, userDto));
        assertTrue(exception.getMessage().contains("修改方案失败"));

        verify(planMapper).selectPlanById(planDto.getId());
        verify(planMapper).checkPlanNameExistsExcludeId(planDto.getName(), planDto.getId());
        verify(planMapper).updatePlan(any(PlanEntity.class));
    }

    @ParameterizedTest
    @MethodSource("provideDeletePlanByIdsScenarios")
    @DisplayName("批量删除方案")
    void testDeletePlanByIds(Long[] ids, UserDto user, List<PlanEntity> existingEntities,
                            Integer mockResult, Class<? extends Exception> expectedException, String expectedMessage) {
        // given
        if (ids != null && ids.length > 0) {
            for (int i = 0; i < ids.length; i++) {
                PlanEntity entity = (existingEntities != null && i < existingEntities.size()) 
                    ? existingEntities.get(i) : null;
                when(planMapper.selectPlanById(ids[i])).thenReturn(entity);
            }
        }
        
        if (mockResult != null) {
            when(planMapper.deletePlanByIds(ids)).thenReturn(mockResult);
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planService.deletePlanByIds(ids, user));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                int result = planService.deletePlanByIds(ids, user);
                assertEquals(mockResult.intValue(), result);
            });
        }

        // verify
        if (ids != null && ids.length > 0 && existingEntities != null && 
            existingEntities.size() >= ids.length && mockResult != null) {
            verify(planMapper).deletePlanByIds(ids);
        }
    }

    static Stream<Arguments> provideDeletePlanByIdsScenarios() {
        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        Long[] validIds = {1L, 2L};
        List<PlanEntity> existingEntities = new ArrayList<>();
        existingEntities.add(new PlanEntity());
        existingEntities.add(new PlanEntity());

        return Stream.of(
                // IDs为null的情况
                Arguments.of(null, validUser, null, null, ContrastBusinessException.class, "删除ID不能为空"),
                // IDs为空数组的情况
                Arguments.of(new Long[]{}, validUser, null, null, ContrastBusinessException.class, "删除ID不能为空"),
                // 方案不存在的情况
                Arguments.of(new Long[]{999L}, validUser, Arrays.asList((PlanEntity) null), null, 
                    ContrastBusinessException.class, "的方案不存在"),
                // 删除成功的情况
                Arguments.of(validIds, validUser, existingEntities, 2, null, null)
        );
    }

    @Test
    @DisplayName("删除方案-系统异常")
    void testDeletePlanByIds_SystemException() {
        // given
        Long[] ids = {1L, 2L};
        when(planMapper.selectPlanById(1L)).thenReturn(planEntity);
        when(planMapper.selectPlanById(2L)).thenReturn(planEntity);
        when(planMapper.deletePlanByIds(ids)).thenThrow(new RuntimeException("数据库异常"));

        // when & then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> planService.deletePlanByIds(ids, userDto));
        assertTrue(exception.getMessage().contains("删除方案失败"));

        verify(planMapper).selectPlanById(1L);
        verify(planMapper).selectPlanById(2L);
        verify(planMapper).deletePlanByIds(ids);
    }
} 