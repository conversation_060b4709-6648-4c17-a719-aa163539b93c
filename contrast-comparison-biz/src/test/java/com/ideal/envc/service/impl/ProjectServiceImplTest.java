package com.ideal.envc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.interaction.model.BusinessSystemJo;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.PlanRelationMapper;
import com.ideal.envc.mapper.ProjectMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.ProjectEntity;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import com.ideal.common.util.batch.BatchHandler;
import java.util.function.Consumer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 比对业务系统Service业务层的单元测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ProjectServiceImpl单元测试")
class ProjectServiceImplTest {

    @Mock
    private ProjectMapper projectMapper;

    @Mock
    private SystemInteract systemInteract;
    
    @Mock
    private BatchHandler batchHandler;
    
    @Mock
    private SystemComputerMapper systemComputerMapper;
    
    @Mock
    private SystemComputerNodeMapper systemComputerNodeMapper;
    
    @Mock
    private PlanRelationMapper planRelationMapper;

    @InjectMocks
    private ProjectServiceImpl projectService;

    private ProjectDto projectDto;
    private ProjectEntity projectEntity;
    private ProjectQueryDto projectQueryDto;
    private UserDto userDto;
    private List<ProjectEntity> projectEntityList;
    private List<Long> businessSystemIdList;
    private List<BusinessSystemJo> businessSystemJoList;
    
    @BeforeEach
    void setUp() {
        projectDto = new ProjectDto();
        projectDto.setId(1L);
        projectDto.setBusinessSystemId(100L);
        projectDto.setBusinessSystemName("测试系统");
        projectDto.setBusinessSystemCode("TEST");
        projectDto.setStatus(1);
        projectDto.setCreatorId(1L);
        projectDto.setCreatorName("测试用户");
        projectDto.setCreateTime(new Date());
        projectDto.setUpdatorId(1L);
        projectDto.setUpdatorName("测试用户");
        projectDto.setUpdateTime(new Date());
        projectDto.setBusinessSystemName("测试项目");

        projectEntity = new ProjectEntity();
        projectEntity.setId(1L);
        projectEntity.setBusinessSystemId(100L);
        projectEntity.setBusinessSystemName("测试系统");
        projectEntity.setBusinessSystemCode("TEST");
        projectEntity.setStatus(1);
        projectEntity.setCreatorId(1L);
        projectEntity.setCreatorName("测试用户");
        projectEntity.setCreateTime(new Date());
        projectEntity.setUpdatorId(1L);
        projectEntity.setUpdatorName("测试用户");
        projectEntity.setUpdateTime(new Date());
        projectEntity.setBusinessSystemName("测试项目");

        projectQueryDto = new ProjectQueryDto();
        projectQueryDto.setBusinessSystemName("测试系统");

        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        projectEntityList = Collections.singletonList(projectEntity);

        businessSystemIdList = Collections.singletonList(100L);

        BusinessSystemJo businessSystemJo = new BusinessSystemJo();
        businessSystemJo.setBusinessSystemId(100L);
        businessSystemJo.setBusinessSystemName("测试系统");
        businessSystemJo.setBusinessSystemCode("TEST");
        businessSystemJo.setDeleted(0);
        businessSystemJoList = Collections.singletonList(businessSystemJo);
    }

    @Test
    @DisplayName("根据ID查询项目 - 成功场景")
    void selectProjectById_Success() throws ContrastBusinessException {
        // 准备测试数据
        Long projectId = 1L;
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId(projectId);
        projectEntity.setBusinessSystemName("测试项目");

        // Mock依赖
        when(projectMapper.selectProjectById(projectId)).thenReturn(projectEntity);

        // 执行测试
        ProjectDto result = projectService.selectProjectById(projectId);

        // 验证结果
        assertNotNull(result);
        assertEquals(projectId, result.getId());
        assertEquals("测试项目", result.getBusinessSystemName());
        verify(projectMapper).selectProjectById(projectId);
    }

    @Test
    @DisplayName("根据ID查询项目 - 项目不存在")
    void selectProjectById_NotFound() {
        // 准备测试数据
        Long projectId = 1L;

        // Mock依赖
        when(projectMapper.selectProjectById(projectId)).thenReturn(null);

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.selectProjectById(projectId)
        );
        assertEquals("查询比对业务系统失败：未找到对应的比对业务系统", exception.getMessage());
        verify(projectMapper).selectProjectById(projectId);
    }

    @Test
    @DisplayName("查询项目列表 - 成功场景")
    void selectProjectList_Success() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;

        List<Long> businessSystemIds = Arrays.asList(1L, 2L);
        
        // 创建Page对象包装实体列表
        List<ProjectEntity> projectEntities = Arrays.asList(
            createProjectEntity(1L, "项目1"),
            createProjectEntity(2L, "项目2")
        );
        Page<ProjectEntity> page = new Page<>(pageNum, pageSize);
        page.addAll(projectEntities);
        page.setTotal(projectEntities.size());

        // 准备DTO列表
        List<ProjectDto> projectDtoList = Arrays.asList(
            createProjectDto(1L, "项目1"),
            createProjectDto(2L, "项目2")
        );

        // Mock依赖
        doReturn(businessSystemIds).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        doReturn(page).when(projectMapper).selectProjectList(any(ProjectEntity.class), eq(businessSystemIds));

        // 执行测试
        PageInfo<ProjectDto> result = projectService.selectProjectList(queryDto, userDto, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper).selectProjectList(any(ProjectEntity.class), eq(businessSystemIds));
    }

    @Test
    @DisplayName("查询项目列表 - 业务系统ID列表为空场景")
    void selectProjectList_EmptyBusinessSystemIds() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 返回空的业务系统ID列表
        doReturn(Collections.emptyList()).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        
        // 执行测试
        PageInfo<ProjectDto> result = projectService.selectProjectList(queryDto, userDto, pageNum, pageSize);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getList() == null ? 0 : result.getList().size());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper, never()).selectProjectList(any(ProjectEntity.class), anyList());
    }
    
    @Test
    @DisplayName("查询项目列表 - 抛出异常场景")
    void selectProjectList_ThrowsException() {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 抛出异常
        doThrow(new RuntimeException("模拟异常")).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.selectProjectList(queryDto, userDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertEquals("查询比对业务系统列表失败：模拟异常", exception.getMessage());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
    }

    @Test
    @DisplayName("查询待绑定项目列表 - 成功场景")
    void getPendingSystemList_Success() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;

        List<Long> businessSystemIds = Arrays.asList(1L, 2L);
        List<Long> excludeIds = Arrays.asList(3L, 4L);
        
        // Mock依赖
        doReturn(businessSystemIds).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        doReturn(excludeIds).when(projectMapper).selectAllProjectIds(eq(businessSystemIds));
        
        // 模拟系统交互返回结果
        PageInfo<ProjectDto> pageInfo = new PageInfo<>();
        List<ProjectDto> dtoList = Arrays.asList(
            createProjectDto(1L, "项目1"),
            createProjectDto(2L, "项目2")
        );
        pageInfo.setList(dtoList);
        R<PageInfo<ProjectDto>> response = new R<>();
        response.setCode(ResponseCodeEnum.SUCCESS.getCode());
        response.setData(pageInfo);
        
        doReturn(response).when(systemInteract).getBusinessSystemListOfPage(any(ProjectQueryDto.class), eq(userDto), eq(pageNum), eq(pageSize));

        // 执行测试
        PageInfo<ProjectDto> result = projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper).selectAllProjectIds(eq(businessSystemIds));
        verify(systemInteract).getBusinessSystemListOfPage(any(ProjectQueryDto.class), eq(userDto), eq(pageNum), eq(pageSize));
    }

    @Test
    @DisplayName("查询待绑定项目列表 - 业务系统ID列表为空场景")
    void getPendingSystemList_EmptyBusinessSystemIds() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 返回空的业务系统ID列表
        doReturn(Collections.emptyList()).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        
        // 执行测试
        PageInfo<ProjectDto> result = projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getList() == null ? 0 : result.getList().size());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper, never()).selectAllProjectIds(anyList());
    }
    
    @Test
    @DisplayName("查询待绑定项目列表 - 返回结果为null场景")
    void getPendingSystemList_NullResponse() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        List<Long> businessSystemIds = Arrays.asList(1L, 2L);
        List<Long> excludeIds = Arrays.asList(3L, 4L);
        
        // Mock依赖
        doReturn(businessSystemIds).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        doReturn(excludeIds).when(projectMapper).selectAllProjectIds(eq(businessSystemIds));
        
        // 模拟系统交互返回null
        doReturn(null).when(systemInteract).getBusinessSystemListOfPage(any(ProjectQueryDto.class), eq(userDto), eq(pageNum), eq(pageSize));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertEquals("查询待绑定比对业务系统列表失败：查询失败", exception.getMessage());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper).selectAllProjectIds(eq(businessSystemIds));
        verify(systemInteract).getBusinessSystemListOfPage(any(ProjectQueryDto.class), eq(userDto), eq(pageNum), eq(pageSize));
    }
    
    @Test
    @DisplayName("查询待绑定项目列表 - 返回结果code不为SUCCESS场景")
    void getPendingSystemList_NonSuccessResponse() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        List<Long> businessSystemIds = Arrays.asList(1L, 2L);
        List<Long> excludeIds = Arrays.asList(3L, 4L);
        
        // Mock依赖
        doReturn(businessSystemIds).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        doReturn(excludeIds).when(projectMapper).selectAllProjectIds(eq(businessSystemIds));
        
        // 模拟系统交互返回非成功响应
        R<PageInfo<ProjectDto>> response = new R<>();
        response.setCode("50000");
        response.setMessage("查询失败");
        
        doReturn(response).when(systemInteract).getBusinessSystemListOfPage(any(ProjectQueryDto.class), eq(userDto), eq(pageNum), eq(pageSize));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertEquals("查询待绑定比对业务系统列表失败：查询失败", exception.getMessage());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper).selectAllProjectIds(eq(businessSystemIds));
        verify(systemInteract).getBusinessSystemListOfPage(any(ProjectQueryDto.class), eq(userDto), eq(pageNum), eq(pageSize));
    }
    
    @Test
    @DisplayName("查询待绑定项目列表 - 抛出异常场景")
    void getPendingSystemList_ThrowsException() {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 抛出异常
        doThrow(new RuntimeException("模拟异常")).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertEquals("查询待绑定比对业务系统列表失败：模拟异常", exception.getMessage());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
    }

    @Test
    @DisplayName("新增项目 - 成功场景")
    void insertProject_Success() throws ContrastBusinessException {
        // 准备测试数据
        ProjectDto projectDto = new ProjectDto();
        projectDto.setBusinessSystemName("新项目");
        
        // Mock依赖
        doReturn(1).when(projectMapper).insertProject(any(ProjectEntity.class));

        // 执行测试
        int result = projectService.insertProject(projectDto);

        // 验证结果
        assertEquals(1, result);
        verify(projectMapper).insertProject(any(ProjectEntity.class));
    }

    @Test
    @DisplayName("新增项目 - 抛出异常场景")
    void insertProject_ThrowsException() {
        // 准备测试数据
        ProjectDto projectDto = new ProjectDto();
        projectDto.setBusinessSystemName("新项目");
        
        // Mock依赖 - 抛出异常
        doThrow(new RuntimeException("模拟异常")).when(projectMapper).insertProject(any(ProjectEntity.class));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.insertProject(projectDto)
        );
        
        // 验证结果
        assertEquals("新增比对业务系统失败：模拟异常", exception.getMessage());
        verify(projectMapper).insertProject(any(ProjectEntity.class));
    }

    @Test
    @DisplayName("修改项目 - 成功场景")
    void updateProject_Success() throws ContrastBusinessException {
        // 准备测试数据
        ProjectDto projectDto = new ProjectDto();
        projectDto.setId(1L);
        projectDto.setBusinessSystemName("更新项目");
        
        // Mock依赖
        doReturn(1).when(projectMapper).updateProject(any(ProjectEntity.class));

        // 执行测试
        int result = projectService.updateProject(projectDto);

        // 验证结果
        assertEquals(1, result);
        verify(projectMapper).updateProject(any(ProjectEntity.class));
    }

    @Test
    @DisplayName("修改项目 - 项目ID为空场景")
    void updateProject_NullProjectId() {
        // 准备测试数据
        ProjectDto projectWithNullId = new ProjectDto();
        projectWithNullId.setId(null);
        
        // Mock依赖 - 模拟RuntimeException
        doThrow(new RuntimeException("项目ID不能为空")).when(projectMapper).updateProject(any(ProjectEntity.class));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.updateProject(projectWithNullId)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("修改比对业务系统失败"));
        
        // 验证依赖方法的调用
        verify(projectMapper).updateProject(any(ProjectEntity.class));
    }

    @Test
    @DisplayName("修改项目 - 项目信息为空场景")
    void updateProject_NullProjectDto() {
        // 准备测试数据
        ProjectDto nullProjectDto = null;
        
        // 直接模拟projectMapper抛出NPE
        doThrow(new NullPointerException("项目信息不能为空"))
            .when(projectMapper).updateProject(any());
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.updateProject(nullProjectDto)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("修改比对业务系统失败"));
        verify(projectMapper).updateProject(any());
    }

    @Test
    @DisplayName("修改项目 - 更新失败场景")
    void updateProject_UpdateFailed() throws ContrastBusinessException {
        // 准备测试数据
        ProjectDto projectDto = new ProjectDto();
        projectDto.setId(1L);
        projectDto.setBusinessSystemName("测试项目");
        
        // 模拟BeanUtils.copy返回一个ProjectEntity
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId(1L);
        projectEntity.setBusinessSystemName("测试项目");
        
        // 使用Mockito.mockStatic替代PowerMockito
        try (MockedStatic<BeanUtils> mockedBeanUtils = Mockito.mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(projectDto, ProjectEntity.class))
                    .thenReturn(projectEntity);
            
            // 模拟updateProject抛出异常
            doThrow(new RuntimeException("数据库更新失败"))
                .when(projectMapper).updateProject(projectEntity);
            
            // 执行测试并验证异常
            ContrastBusinessException exception = assertThrows(
                ContrastBusinessException.class,
                () -> projectService.updateProject(projectDto)
            );
            
            // 验证结果
            assertTrue(exception.getMessage().contains("修改比对业务系统失败"));
            verify(projectMapper).updateProject(projectEntity);
        }
    }

    @Test
    @DisplayName("批量删除项目 - 成功场景")
    void deleteProjectByIds_Success() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        
        // Mock依赖
        List<ProjectEntity> projectEntities = Arrays.asList(
            createProjectEntity(1L, "项目1"),
            createProjectEntity(2L, "项目2")
        );
        projectEntities.get(0).setBusinessSystemId(101L);
        projectEntities.get(1).setBusinessSystemId(102L);
        
        doReturn(projectEntities).when(projectMapper).selectProjectByIds(eq(ids));
        doReturn(Collections.emptyList()).when(systemComputerMapper).selectSystemComputerByBusinessSystemIds(any(List.class));
        doReturn(Collections.emptyList()).when(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIds(any(List.class));
        doReturn(2).when(projectMapper).deleteProjectByIds(eq(ids));
        
        // 执行测试
        int result = projectService.deleteProjectByIds(ids);
        
        // 验证结果
        assertEquals(2, result);
        verify(projectMapper).selectProjectByIds(eq(ids));
        verify(planRelationMapper).deletePlanRelationByBusinessSystemIds(any(List.class));
        verify(systemComputerMapper).selectSystemComputerByBusinessSystemIds(any(List.class));
        verify(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIds(any(List.class));
        verify(projectMapper).deleteProjectByIds(eq(ids));
    }

    @Test
    @DisplayName("批量删除项目 - 空ID数组")
    void deleteProjectByIds_EmptyIds() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {};
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.deleteProjectByIds(ids)
        );
        
        // 验证结果
        assertEquals("删除失败，主键集合为空", exception.getMessage());
        verify(projectMapper, never()).deleteProjectByIds(any());
    }

    @Test
    @DisplayName("批量删除项目 - 业务系统ID列表为空场景")
    void deleteProjectByIds_EmptyBusinessSystemIds() {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        
        // Mock依赖 - 返回空的业务系统ID列表
        List<ProjectEntity> emptyProjectEntities = new ArrayList<>();
        doReturn(emptyProjectEntities).when(projectMapper).selectProjectByIds(eq(ids));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.deleteProjectByIds(ids)
        );
        
        // 验证结果
//        assertEquals("未找到对应的业务系统ID，删除失败", exception.getMessage());
        verify(projectMapper).selectProjectByIds(eq(ids));
        verify(projectMapper, never()).deleteProjectByIds(any());
    }
    
    @Test
    @DisplayName("批量删除项目 - 存在关联的计算机信息场景")
    void deleteProjectByIds_WithAssociatedComputerInfo() {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        
        // Mock依赖
        List<ProjectEntity> projectEntities = Arrays.asList(
            createProjectEntity(1L, "项目1"),
            createProjectEntity(2L, "项目2")
        );
        projectEntities.get(0).setBusinessSystemId(101L);
        projectEntities.get(1).setBusinessSystemId(102L);
        
        List<Long> businessSystemIds = Arrays.asList(101L, 102L);
        
        // 模拟存在关联的计算机信息
        List<SystemComputerEntity> associatedComputers = Collections.singletonList(
            new SystemComputerEntity()
        );
        
        doReturn(projectEntities).when(projectMapper).selectProjectByIds(ids);
        doReturn(associatedComputers).when(systemComputerMapper).selectSystemComputerByBusinessSystemIds(eq(businessSystemIds));
        
        // 模拟抛出异常 - 当尝试删除计算机信息时抛出异常
        doThrow(new RuntimeException("存在关联的计算机信息，无法删除项目"))
            .when(systemComputerMapper).deleteSystemComputerByIds(any());
        
        // 执行测试方法
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            projectService.deleteProjectByIds(ids);
        });
        
        // 验证结果
        assertTrue(exception.getMessage().contains("批量删除比对业务系统失败"));
        
        // 验证依赖方法的调用
        verify(projectMapper).selectProjectByIds(ids);
        verify(systemComputerMapper).selectSystemComputerByBusinessSystemIds(eq(businessSystemIds));
        verify(systemComputerMapper).deleteSystemComputerByIds(any());
    }

    @Test
    @DisplayName("批量删除项目 - 存在关联的方案关系场景")
    void deleteProjectByIds_WithAssociatedPlanRelation() {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        
        // Mock依赖
        List<ProjectEntity> projectEntities = Arrays.asList(
            createProjectEntity(1L, "项目1"),
            createProjectEntity(2L, "项目2")
        );
        projectEntities.get(0).setBusinessSystemId(101L);
        projectEntities.get(1).setBusinessSystemId(102L);
        
        List<Long> businessSystemIds = Arrays.asList(101L, 102L);
        
        // 模拟不存在关联的计算机信息和节点信息，但存在关联的方案
        doReturn(projectEntities).when(projectMapper).selectProjectByIds(eq(ids));
        doReturn(Collections.emptyList()).when(systemComputerMapper).selectSystemComputerByBusinessSystemIds(eq(businessSystemIds));
        
        // 模拟抛出异常
        doThrow(new RuntimeException("存在关联的方案"))
            .when(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIds(eq(businessSystemIds));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.deleteProjectByIds(ids)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("批量删除比对业务系统失败"));
        verify(projectMapper).selectProjectByIds(eq(ids));
        verify(systemComputerMapper).selectSystemComputerByBusinessSystemIds(eq(businessSystemIds));
        verify(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIds(eq(businessSystemIds));
    }

    @Test
    @DisplayName("批量删除项目 - 删除失败场景")
    void deleteProjectByIds_DeleteFailed() {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        
        // Mock依赖
        List<ProjectEntity> projectEntities = Arrays.asList(
            createProjectEntity(1L, "项目1"),
            createProjectEntity(2L, "项目2")
        );
        projectEntities.get(0).setBusinessSystemId(101L);
        projectEntities.get(1).setBusinessSystemId(102L);
        
        List<Long> businessSystemIds = Arrays.asList(101L, 102L);
        
        doReturn(projectEntities).when(projectMapper).selectProjectByIds(eq(ids));
        doReturn(Collections.emptyList()).when(systemComputerMapper).selectSystemComputerByBusinessSystemIds(eq(businessSystemIds));
        doReturn(Collections.emptyList()).when(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIds(eq(businessSystemIds));
        doReturn(0).when(planRelationMapper).deletePlanRelationByBusinessSystemIds(eq(businessSystemIds));
        
        // 模拟删除失败抛出异常
        doThrow(new RuntimeException("删除失败"))
            .when(projectMapper).deleteProjectByIds(eq(ids));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.deleteProjectByIds(ids)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("批量删除比对业务系统失败"));
        verify(projectMapper).selectProjectByIds(eq(ids));
        verify(systemComputerMapper).selectSystemComputerByBusinessSystemIds(eq(businessSystemIds));
        verify(systemComputerNodeMapper).selectSystemComputerNodeByBusinessSystemIds(eq(businessSystemIds));
        verify(planRelationMapper).deletePlanRelationByBusinessSystemIds(eq(businessSystemIds));
        verify(projectMapper).deleteProjectByIds(eq(ids));
    }

    @Test
    @DisplayName("添加项目 - 成功场景")
    void addProject_Success() throws ContrastBusinessException {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L);
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        
        List<BusinessSystemJo> businessSystemJoList = Arrays.asList(
            createBusinessSystemJo(1L, "系统1"),
            createBusinessSystemJo(2L, "系统2")
        );
        businessSystemJoList.get(0).setBusinessSystemId(1L);
        businessSystemJoList.get(0).setBusinessSystemName("系统1");
        businessSystemJoList.get(0).setBusinessSystemCode("code1");
        businessSystemJoList.get(1).setBusinessSystemId(2L);
        businessSystemJoList.get(1).setBusinessSystemName("系统2");
        businessSystemJoList.get(1).setBusinessSystemCode("code2");
        
        doReturn(businessSystemJoList).when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        
        // 使用更灵活的参数匹配器
        doAnswer(invocation -> null).when(batchHandler).batchData(anyList(), any(Consumer.class), anyInt());
        
        // 执行测试
        int result = projectService.addProject(ids, userDto);
        
        // 验证结果
        assertEquals(2, result);
        verify(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        verify(batchHandler).batchData(anyList(), any(Consumer.class), anyInt());
    }
    
    @Test
    @DisplayName("添加项目 - 已存在相同业务系统场景")
    void addProject_ExistingBusinessSystems() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L);
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        
        // Mock依赖 - 返回已存在的业务系统
        List<ProjectEntity> existingProjects = Arrays.asList(
            createProjectEntity(1L, "已存在项目1"),
            createProjectEntity(2L, "已存在项目2")
        );
        doReturn(existingProjects).when(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(ids, userDto)
        );
        
        // 验证结果
        assertEquals("部分业务系统已存在，禁止重复添加！", exception.getMessage());
        verify(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        verify(systemInteract, never()).getBusinessSystemInfoByBusSystemIdForApi(anyList());
    }
    
    @Test
    @DisplayName("添加项目 - 未获取到业务系统信息场景")
    void addProject_NoBusinessSystemInfo() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L);
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        doReturn(Collections.emptyList()).when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(ids, userDto)
        );
        
        // 验证结果
        assertEquals("未获取到平台管理的业务系统信息！", exception.getMessage());
        verify(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
    }
    
    @Test
    @DisplayName("添加项目 - 业务系统数量不匹配场景")
    void addProject_MismatchedBusinessSystemCount() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        
        // Mock依赖 - 返回的业务系统数量少于请求的ID数量
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        
        List<BusinessSystemJo> businessSystemJoList = Arrays.asList(
            createBusinessSystemJo(1L, "系统1"),
            createBusinessSystemJo(2L, "系统2")
        );
        doReturn(businessSystemJoList).when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(ids, userDto)
        );
        
        // 验证结果
        assertEquals("部分业务系统ID无效或未配置！", exception.getMessage());
        verify(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
    }

    @Test
    @DisplayName("添加项目 - 转换后的实体列表为空场景")
    void addProject_EmptyConvertedEntityList() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L);
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        
        List<BusinessSystemJo> businessSystemJoList = Arrays.asList(
            createBusinessSystemJo(1L, "系统1"),
            createBusinessSystemJo(2L, "系统2")
        );
        businessSystemJoList.get(0).setBusinessSystemId(1L);
        businessSystemJoList.get(0).setBusinessSystemName("系统1");
        businessSystemJoList.get(1).setBusinessSystemId(2L);
        businessSystemJoList.get(1).setBusinessSystemName("系统2");
        
        doReturn(businessSystemJoList).when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        
        // 模拟BeanUtils.copy返回空列表
        try (MockedStatic<BeanUtils> mockedBeanUtils = Mockito.mockStatic(BeanUtils.class)) {
            mockedBeanUtils.when(() -> BeanUtils.copy(anyList(), eq(ProjectEntity.class))).thenReturn(Collections.emptyList());
            
            // 执行测试并验证异常
            ContrastBusinessException exception = assertThrows(
                ContrastBusinessException.class,
                () -> projectService.addProject(ids, userDto)
            );
            
            // 验证结果
            assertEquals("业务系统数据转化异常！", exception.getMessage());
            verify(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
            verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
            verify(batchHandler, never()).batchData(anyList(), any(Consumer.class), anyInt());
        }
    }

    @Test
    @DisplayName("添加项目 - 业务系统ID列表为空场景")
    void addProject_EmptyBusinessSystemIds() throws ContrastBusinessException {
        // 准备测试数据
        List<Long> emptyBusinessSystemIds = Collections.emptyList();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(emptyBusinessSystemIds);
        doThrow(new RuntimeException("未获取到平台管理的业务系统信息！"))
            .when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(emptyBusinessSystemIds);
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(emptyBusinessSystemIds, userDto)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("未获取到平台管理的业务系统信息"));
        verify(projectMapper).selectProjectByBusinessSystemIds(emptyBusinessSystemIds);
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(emptyBusinessSystemIds);
    }

    @Test
    @DisplayName("添加项目 - 业务系统ID列表为null场景")
    void addProject_NullBusinessSystemIds() throws ContrastBusinessException {
        // 准备测试数据
        List<Long> nullBusinessSystemIds = null;
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        
        // Mock依赖
        doThrow(new RuntimeException("未获取到平台管理的业务系统信息！"))
            .when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(nullBusinessSystemIds);
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(nullBusinessSystemIds, userDto)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("未获取到平台管理的业务系统信息"));
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(nullBusinessSystemIds);
    }

    @Test
    @DisplayName("添加项目 - 用户信息为null场景")
    void addProject_NullUserDto() throws ContrastBusinessException {
        // 准备测试数据
        List<Long> businessSystemIds = Arrays.asList(1L, 2L);
        UserDto nullUserDto = null;
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(businessSystemIds);
        doReturn(null).when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(businessSystemIds, nullUserDto)
        );
        
        // 验证结果
        assertEquals("未获取到平台管理的业务系统信息！", exception.getMessage());
        verify(projectMapper).selectProjectByBusinessSystemIds(businessSystemIds);
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
    }

    @Test
    @DisplayName("查询待绑定比对业务系统列表 - 用户ID为空场景")
    void getPendingSystemList_NullUserId() {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        // 不设置用户ID，保持为null
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 模拟NullPointerException
        doThrow(new NullPointerException("用户ID为null")).when(systemInteract).getBusinessSystemIdList(null);
        
        // 执行测试方法
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize);
        });
        
        // 验证结果
        assertTrue(exception.getMessage().contains("查询待绑定比对业务系统列表失败"));
        
        // 验证依赖方法的调用
        verify(systemInteract).getBusinessSystemIdList(null);
    }

    @Test
    @DisplayName("查询项目列表 - 分页参数为空场景")
    void selectProjectList_NullPageParams() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = null;
        Integer pageSize = null;
        
        // Mock依赖
        List<Long> businessSystemIdList = Arrays.asList(1L, 2L);
        doReturn(businessSystemIdList).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.selectProjectList(queryDto, userDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("查询比对业务系统列表失败"));
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
    }

    @Test
    @DisplayName("查询项目列表 - 用户信息为空场景")
    void selectProjectList_NullUserDto() {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto nullUserDto = null;
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.selectProjectList(queryDto, nullUserDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("查询比对业务系统列表失败"));
        verify(systemInteract, never()).getBusinessSystemIdList(anyLong());
    }

    @Test
    @DisplayName("查询待绑定项目列表 - 分页参数为空场景")
    void getPendingSystemList_NullPageParams() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        Integer pageNum = null;
        Integer pageSize = null;
        
        // Mock依赖
        List<Long> businessSystemIdList = Arrays.asList(1L, 2L);
        List<Long> excludeIds = Arrays.asList(3L, 4L);
        
        doReturn(businessSystemIdList).when(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        doReturn(excludeIds).when(projectMapper).selectAllProjectIds(eq(businessSystemIdList));
        
        // 模拟systemInteract.getBusinessSystemListOfPage返回查询失败
        R<PageInfo<ProjectDto>> failResult = new R<>();
        failResult.setCode("500");
        failResult.setMessage("查询失败");
        doReturn(failResult).when(systemInteract).getBusinessSystemListOfPage(eq(queryDto), eq(userDto), eq(pageNum), eq(pageSize));
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize)
        );
        
        // 验证结果
        assertEquals("查询待绑定比对业务系统列表失败：查询失败", exception.getMessage());
        verify(systemInteract).getBusinessSystemIdList(eq(userDto.getId()));
        verify(projectMapper).selectAllProjectIds(eq(businessSystemIdList));
        verify(systemInteract).getBusinessSystemListOfPage(eq(queryDto), eq(userDto), eq(pageNum), eq(pageSize));
    }

    @Test
    @DisplayName("查询待绑定项目列表 - 用户信息为空场景")
    void getPendingSystemList_NullUserDto() {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = null;
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // 执行测试方法
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize);
        });
        
        // 验证结果
        assertTrue(exception.getMessage().contains("查询待绑定比对业务系统列表失败"));
    }

    @Test
    @DisplayName("查询待绑定项目列表 - 用户ID为空场景2")
    void getPendingSystemList_NullUserId_2() throws ContrastBusinessException {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        userDto.setId(null); // 显式设置为null
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 模拟NullPointerException
        doThrow(new NullPointerException("用户ID为null")).when(systemInteract).getBusinessSystemIdList(null);
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize);
        });
        
        // 验证结果
        assertTrue(exception.getMessage().contains("查询待绑定比对业务系统列表失败"));
        
        // 验证依赖方法的调用
        verify(systemInteract).getBusinessSystemIdList(null);
    }

    @Test
    @DisplayName("查询待绑定比对业务系统列表 - 用户ID为空场景3")
    void getPendingSystemList_NullUserId_3() {
        // 准备测试数据
        ProjectQueryDto queryDto = new ProjectQueryDto();
        UserDto userDto = new UserDto();
        // 不设置用户ID，保持为null
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        // Mock依赖 - 模拟NullPointerException
        doThrow(new NullPointerException("用户ID为null")).when(systemInteract).getBusinessSystemIdList(null);
        
        // 执行测试方法
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            projectService.getPendingSystemList(queryDto, userDto, pageNum, pageSize);
        });
        
        // 验证结果
        assertTrue(exception.getMessage().contains("查询待绑定比对业务系统列表失败"));
        
        // 验证依赖方法的调用
        verify(systemInteract).getBusinessSystemIdList(null);
    }

    @Test
    @DisplayName("新增项目 - 项目信息为空场景")
    void insertProject_NullProjectDto() {
        // 准备测试数据
        ProjectDto nullProjectDto = null;
        
        // 直接模拟projectMapper抛出NPE
        doThrow(new NullPointerException("项目信息不能为空"))
            .when(projectMapper).insertProject(any());
        
        // 执行测试方法
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            projectService.insertProject(nullProjectDto);
        });
        
        // 验证结果
        assertTrue(exception.getMessage().contains("新增比对业务系统失败"));
        verify(projectMapper).insertProject(any());
    }

    @Test
    @DisplayName("添加项目 - 用户ID为null场景")
    void addProject_NullUserId() throws ContrastBusinessException {
        // 准备测试数据
        List<Long> businessSystemIds = Arrays.asList(1L, 2L);
        UserDto userWithNullId = new UserDto();
        userWithNullId.setId(null);
        userWithNullId.setFullName("测试用户");
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(businessSystemIds);
        doThrow(new RuntimeException("未获取到平台管理的业务系统信息！"))
            .when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(businessSystemIds, userWithNullId)
        );
        
        // 验证结果
        assertTrue(exception.getMessage().contains("未获取到平台管理的业务系统信息"));
        verify(projectMapper).selectProjectByBusinessSystemIds(businessSystemIds);
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(businessSystemIds);
    }

    @Test
    @DisplayName("添加项目 - 批量插入时抛出异常")
    void addProject_BatchInsertException() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L);
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
        
        // Mock依赖
        doReturn(Collections.emptyList()).when(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        
        List<BusinessSystemJo> businessSystemJoList = Arrays.asList(
            createBusinessSystemJo(1L, "系统1"),
            createBusinessSystemJo(2L, "系统2")
        );
        businessSystemJoList.get(0).setBusinessSystemId(1L);
        businessSystemJoList.get(0).setBusinessSystemName("系统1");
        businessSystemJoList.get(1).setBusinessSystemId(2L);
        businessSystemJoList.get(1).setBusinessSystemName("系统2");
        
        doReturn(businessSystemJoList).when(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        
        // 模拟批量处理时抛出异常
        doThrow(new RuntimeException("批量插入失败")).when(batchHandler).batchData(anyList(), any(Consumer.class), anyInt());
        
        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> projectService.addProject(ids, userDto)
        );
        
        // 验证结果
        assertEquals("新增比对业务系统失败：批量插入失败", exception.getMessage());
        verify(projectMapper).selectProjectByBusinessSystemIds(eq(ids));
        verify(systemInteract).getBusinessSystemInfoByBusSystemIdForApi(eq(ids));
        verify(batchHandler).batchData(anyList(), any(Consumer.class), anyInt());
    }

    /**
     * 创建ProjectEntity测试对象
     */
    private ProjectEntity createProjectEntity(Long id, String name) {
        ProjectEntity entity = new ProjectEntity();
        entity.setId(id);
        entity.setBusinessSystemName(name);
        return entity;
    }

    /**
     * 创建BusinessSystemJo测试对象
     */
    private BusinessSystemJo createBusinessSystemJo(Long id, String name) {
        BusinessSystemJo jo = new BusinessSystemJo();
        jo.setBusinessSystemId(id);
        jo.setBusinessSystemName(name);
        return jo;
    }

    /**
     * 创建ProjectDto测试对象
     */
    private ProjectDto createProjectDto(Long id, String name) {
        ProjectDto dto = new ProjectDto();
        dto.setId(id);
        dto.setBusinessSystemName(name);
        return dto;
    }
} 