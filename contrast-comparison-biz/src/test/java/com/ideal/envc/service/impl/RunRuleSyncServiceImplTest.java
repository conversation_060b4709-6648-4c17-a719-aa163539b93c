package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.RunRuleSyncMapper;
import com.ideal.envc.model.dto.RunRuleSyncDto;
import com.ideal.envc.model.dto.RunRuleSyncQueryDto;
import com.ideal.envc.model.entity.RunRuleSyncEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 节点规则同步结果Service实现类测试
 */
@ExtendWith(MockitoExtension.class)
public class RunRuleSyncServiceImplTest {

    @Mock
    private RunRuleSyncMapper runRuleSyncMapper;

    @InjectMocks
    private RunRuleSyncServiceImpl runRuleSyncService;

    private RunRuleSyncEntity runRuleSyncEntity;
    private RunRuleSyncDto runRuleSyncDto;
    private List<RunRuleSyncEntity> runRuleSyncEntityList;
    private PageInfo<RunRuleSyncDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化实体对象
        runRuleSyncEntity = new RunRuleSyncEntity();
        runRuleSyncEntity.setId(1L);
        runRuleSyncEntity.setEnvcRunRuleId(100L);
        runRuleSyncEntity.setCreatorId(1001L);
        runRuleSyncEntity.setCreatorName("测试用户");
        runRuleSyncEntity.setCreateTime(new Date());
        runRuleSyncEntity.setEndTime(new Date());
        runRuleSyncEntity.setResult(0); // 成功
        runRuleSyncEntity.setState(1); // 已完成
        runRuleSyncEntity.setElapsedTime(1000L);

        // 初始化DTO对象
        runRuleSyncDto = new RunRuleSyncDto();
        runRuleSyncDto.setId(1L);
        runRuleSyncDto.setEnvcRunRuleId(100L);
        runRuleSyncDto.setCreatorId(1001L);
        runRuleSyncDto.setCreatorName("测试用户");
        runRuleSyncDto.setCreateTime(new Date());
        runRuleSyncDto.setEndTime(new Date());
        runRuleSyncDto.setResult(0);
        runRuleSyncDto.setState(1);
        runRuleSyncDto.setElapsedTime(1000L);

        // 初始化实体列表
        runRuleSyncEntityList = new ArrayList<>();
        runRuleSyncEntityList.add(runRuleSyncEntity);

        // 初始化分页信息
        List<RunRuleSyncDto> runRuleSyncDtoList = new ArrayList<>();
        runRuleSyncDtoList.add(runRuleSyncDto);
        pageInfo = new PageInfo<>(runRuleSyncDtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("测试根据ID查询节点规则同步结果")
    void testSelectRunRuleSyncById() {
        // 模拟Mapper层方法返回
        when(runRuleSyncMapper.selectRunRuleSyncById(anyLong())).thenReturn(runRuleSyncEntity);

        // 执行测试方法
        RunRuleSyncDto result = runRuleSyncService.selectRunRuleSyncById(1L);

        // 断言结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(100L, result.getEnvcRunRuleId());
        assertEquals(1001L, result.getCreatorId());
        assertEquals("测试用户", result.getCreatorName());
        assertEquals(0, result.getResult());
        assertEquals(1, result.getState());
        assertEquals(1000L, result.getElapsedTime());

        // 验证方法调用
        verify(runRuleSyncMapper, times(1)).selectRunRuleSyncById(1L);
    }

    @Test
    @DisplayName("测试查询节点规则同步结果列表")
    void testSelectRunRuleSyncList() {
        // 创建查询条件
        RunRuleSyncQueryDto queryDto = new RunRuleSyncQueryDto();
        queryDto.setEnvcRunRuleId(100L);
        queryDto.setResult(0);
        queryDto.setState(1);

        // 模拟Mapper层方法返回
        when(runRuleSyncMapper.selectRunRuleSyncList(any(RunRuleSyncEntity.class))).thenReturn(runRuleSyncEntityList);

        try (MockedStatic<PageDataUtil> pageDataUtilMockedStatic = mockStatic(PageDataUtil.class)) {
            // 模拟PageDataUtil.toDtoPage方法
            pageDataUtilMockedStatic.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunRuleSyncDto.class)))
                    .thenReturn(pageInfo);

            // 执行测试方法
            PageInfo<RunRuleSyncDto> result = runRuleSyncService.selectRunRuleSyncList(queryDto, 1, 10);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());

            // 验证方法调用
            verify(runRuleSyncMapper, times(1)).selectRunRuleSyncList(any(RunRuleSyncEntity.class));
            pageDataUtilMockedStatic.verify(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunRuleSyncDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试新增节点规则同步结果")
    void testInsertRunRuleSync() {
        // 模拟Mapper层方法返回
        when(runRuleSyncMapper.insertRunRuleSync(any(RunRuleSyncEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = runRuleSyncService.insertRunRuleSync(runRuleSyncDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleSyncMapper, times(1)).insertRunRuleSync(any(RunRuleSyncEntity.class));
    }

    @Test
    @DisplayName("测试修改节点规则同步结果")
    void testUpdateRunRuleSync() {
        // 模拟Mapper层方法返回
        when(runRuleSyncMapper.updateRunRuleSync(any(RunRuleSyncEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = runRuleSyncService.updateRunRuleSync(runRuleSyncDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runRuleSyncMapper, times(1)).updateRunRuleSync(any(RunRuleSyncEntity.class));
    }

    @Test
    @DisplayName("测试批量删除节点规则同步结果")
    void testDeleteRunRuleSyncByIds() {
        // 准备测试参数
        Long[] ids = new Long[]{1L, 2L, 3L};

        // 模拟Mapper层方法返回
        doReturn(3).when(runRuleSyncMapper).deleteRunRuleSyncByIds(any(Long[].class));

        // 执行测试方法
        int result = runRuleSyncService.deleteRunRuleSyncByIds(ids);

        // 断言结果
        assertEquals(3, result);

        // 验证方法调用
        verify(runRuleSyncMapper, times(1)).deleteRunRuleSyncByIds(ids);
    }
} 