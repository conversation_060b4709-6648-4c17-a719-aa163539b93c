package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.DictionaryDetailMapper;
import com.ideal.envc.model.dto.DictionaryDetailDto;
import com.ideal.envc.model.dto.DictionaryDetailQueryDto;
import com.ideal.envc.model.entity.DictionaryDetailEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 字典详情Service单元测试
 */
@ExtendWith(MockitoExtension.class)
public class DictionaryDetailServiceImplTest {

    @Mock
    private DictionaryDetailMapper dictionaryDetailMapper;

    @InjectMocks
    private DictionaryDetailServiceImpl dictionaryDetailService;

    private DictionaryDetailEntity mockEntity;
    private DictionaryDetailDto mockDto;
    private List<DictionaryDetailEntity> mockEntityList;
    private PageInfo<DictionaryDetailDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试对象
        mockEntity = new DictionaryDetailEntity();
        mockEntity.setId(1L);
        mockEntity.setEnvcDictionaryId(101L);
        mockEntity.setCode("TEST_CODE");
        mockEntity.setLable("测试标签");
        mockEntity.setValue("测试值");
        mockEntity.setSort(1L);
        mockEntity.setDeleted(0);
        mockEntity.setArrayFlag(0);
        mockEntity.setValueType("string");
        mockEntity.setCreatorName("测试用户");
        mockEntity.setCreatorId(1001L);
        mockEntity.setCreateTime(new Date());
        mockEntity.setUpdatorId(1001L);
        mockEntity.setUpdatorName("测试用户");
        mockEntity.setUpdateTime(new Date());

        // 初始化DTO对象
        mockDto = new DictionaryDetailDto();
        mockDto.setId(1L);
        mockDto.setEnvcDictionaryId(101L);
        mockDto.setCode("TEST_CODE");
        mockDto.setLable("测试标签");
        mockDto.setValue("测试值");
        mockDto.setSort(1L);
        mockDto.setDeleted(0);
        mockDto.setArrayFlag(0);
        mockDto.setValueType("string");
        mockDto.setCreatorName("测试用户");
        mockDto.setCreatorId(1001L);
        mockDto.setCreateTime(new Date());
        mockDto.setUpdatorId(1001L);
        mockDto.setUpdatorName("测试用户");
        mockDto.setUpdateTime(new Date());

        // 初始化实体列表
        mockEntityList = new ArrayList<>();
        mockEntityList.add(mockEntity);

        // 初始化分页信息
        List<DictionaryDetailDto> dtoList = new ArrayList<>();
        dtoList.add(mockDto);
        pageInfo = new PageInfo<>(dtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("测试根据ID查询字典详情")
    void testSelectDictionaryDetailById() {
        // 模拟Mapper层方法返回
        when(dictionaryDetailMapper.selectDictionaryDetailById(anyLong())).thenReturn(mockEntity);

        // 执行测试方法
        DictionaryDetailDto result = dictionaryDetailService.selectDictionaryDetailById(1L);

        // 断言结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(101L, result.getEnvcDictionaryId());
        assertEquals("TEST_CODE", result.getCode());
        assertEquals("测试标签", result.getLable());
        assertEquals("测试值", result.getValue());

        // 验证方法调用
        verify(dictionaryDetailMapper, times(1)).selectDictionaryDetailById(1L);
    }

    @Test
    @DisplayName("测试查询字典详情列表")
    void testSelectDictionaryDetailList() {
        // 创建查询条件
        DictionaryDetailQueryDto queryDto = new DictionaryDetailQueryDto();
        queryDto.setCode("TEST_CODE");
        Integer pageNum = 1;
        Integer pageSize = 10;

        // 模拟Mapper层方法返回
        when(dictionaryDetailMapper.selectDictionaryDetailList(any(DictionaryDetailEntity.class))).thenReturn(mockEntityList);

        try (MockedStatic<PageDataUtil> pageDataUtilMockedStatic = mockStatic(PageDataUtil.class)) {
            // 模拟PageDataUtil.toDtoPage方法
            pageDataUtilMockedStatic.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(DictionaryDetailDto.class)))
                    .thenReturn(pageInfo);

            // 执行测试方法
            PageInfo<DictionaryDetailDto> result = dictionaryDetailService.selectDictionaryDetailList(queryDto, pageNum, pageSize);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());

            // 验证方法调用
            verify(dictionaryDetailMapper, times(1)).selectDictionaryDetailList(any(DictionaryDetailEntity.class));
            pageDataUtilMockedStatic.verify(() -> PageDataUtil.toDtoPage(any(List.class), eq(DictionaryDetailDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试新增字典详情")
    void testInsertDictionaryDetail() {
        // 模拟Mapper层方法返回
        when(dictionaryDetailMapper.insertDictionaryDetail(any(DictionaryDetailEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = dictionaryDetailService.insertDictionaryDetail(mockDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(dictionaryDetailMapper, times(1)).insertDictionaryDetail(any(DictionaryDetailEntity.class));
    }

    @Test
    @DisplayName("测试修改字典详情")
    void testUpdateDictionaryDetail() {
        // 模拟Mapper层方法返回
        when(dictionaryDetailMapper.updateDictionaryDetail(any(DictionaryDetailEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = dictionaryDetailService.updateDictionaryDetail(mockDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(dictionaryDetailMapper, times(1)).updateDictionaryDetail(any(DictionaryDetailEntity.class));
    }

    @Test
    @DisplayName("测试批量删除字典详情")
    void testDeleteDictionaryDetailByIds() {
        // 准备测试参数
        Long[] ids = new Long[]{1L, 2L, 3L};

        // 模拟Mapper层方法返回
        doReturn(3).when(dictionaryDetailMapper).deleteDictionaryDetailByIds(any(Long[].class));

        // 执行测试方法
        int result = dictionaryDetailService.deleteDictionaryDetailByIds(ids);

        // 断言结果
        assertEquals(3, result);

        // 验证方法调用
        verify(dictionaryDetailMapper, times(1)).deleteDictionaryDetailByIds(ids);
    }

    @Test
    @DisplayName("测试根据字典码查询字典详情列表")
    void testFindDictionaryDetailListByCode() {
        // 准备测试参数
        String code = "TEST_CODE";

        // 模拟Mapper层方法返回
        when(dictionaryDetailMapper.selectDictionaryDetailListByCode(anyString())).thenReturn(mockEntityList);

        // 执行测试方法
        List<DictionaryDetailDto> resultList = dictionaryDetailService.findDictionaryDetailListByCode(code);

        // 断言结果
        assertNotNull(resultList);
        assertEquals(1, resultList.size());
        assertEquals(1L, resultList.get(0).getId());
        assertEquals("TEST_CODE", resultList.get(0).getCode());

        // 验证方法调用
        verify(dictionaryDetailMapper, times(1)).selectDictionaryDetailListByCode(code);
    }

    @Test
    @DisplayName("测试根据字典码查询字典详情列表 - 空码值")
    void testFindDictionaryDetailListByCodeWithEmptyCode() {
        // 准备测试参数 - 空码值
        String code = "";

        // 执行测试方法
        List<DictionaryDetailDto> resultList = dictionaryDetailService.findDictionaryDetailListByCode(code);

        // 断言结果
        assertNotNull(resultList);
        assertEquals(0, resultList.size());

        // 验证方法未被调用
        verify(dictionaryDetailMapper, times(0)).selectDictionaryDetailListByCode(anyString());
    }

    @Test
    @DisplayName("测试根据字典码查询字典详情列表 - null码值")
    void testFindDictionaryDetailListByCodeWithNullCode() {
        // 准备测试参数 - null码值
        String code = null;

        // 执行测试方法
        List<DictionaryDetailDto> resultList = dictionaryDetailService.findDictionaryDetailListByCode(code);

        // 断言结果
        assertNotNull(resultList);
        assertEquals(0, resultList.size());

        // 验证方法未被调用
        verify(dictionaryDetailMapper, times(0)).selectDictionaryDetailListByCode(anyString());
    }
} 