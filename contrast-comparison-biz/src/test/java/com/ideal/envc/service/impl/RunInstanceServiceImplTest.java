package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.model.dto.RunInstanceDto;
import com.ideal.envc.model.dto.RunInstanceQueryDto;
import com.ideal.envc.model.entity.RunInstanceEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * 实例Service实现类测试
 */
@ExtendWith(MockitoExtension.class)
public class RunInstanceServiceImplTest {

    @Mock
    private RunInstanceMapper runInstanceMapper;

    @InjectMocks
    private RunInstanceServiceImpl runInstanceService;

    private RunInstanceEntity runInstanceEntity;
    private RunInstanceDto runInstanceDto;
    private List<RunInstanceEntity> runInstanceEntityList;
    private PageInfo<RunInstanceDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化实体对象
        runInstanceEntity = new RunInstanceEntity();
        runInstanceEntity.setId(1L);
        runInstanceEntity.setEnvcPlanId(100L);
        runInstanceEntity.setEnvcTaskId(200L);
        runInstanceEntity.setResult(0); // 成功
        runInstanceEntity.setState(1); // 已完成
        runInstanceEntity.setFrom(2); // 手动触发
        runInstanceEntity.setStarterName("测试用户");
        runInstanceEntity.setStarterId(1001L);
        runInstanceEntity.setStartTime(new Date());
        runInstanceEntity.setEndTime(new Date());
        runInstanceEntity.setElapsedTime(1000L);

        // 初始化DTO对象
        runInstanceDto = new RunInstanceDto();
        runInstanceDto.setId(1L);
        runInstanceDto.setEnvcPlanId(100L);
        runInstanceDto.setEnvcTaskId(200L);
        runInstanceDto.setResult(0);
        runInstanceDto.setState(1);
        runInstanceDto.setFrom(2);
        runInstanceDto.setStarterName("测试用户");
        runInstanceDto.setStarterId(1001L);
        runInstanceDto.setStartTime(new Date());
        runInstanceDto.setEndTime(new Date());
        runInstanceDto.setElapsedTime(1000L);

        // 初始化实体列表
        runInstanceEntityList = new ArrayList<>();
        runInstanceEntityList.add(runInstanceEntity);

        // 初始化分页信息
        List<RunInstanceDto> runInstanceDtoList = new ArrayList<>();
        runInstanceDtoList.add(runInstanceDto);
        pageInfo = new PageInfo<>(runInstanceDtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
    }

    @Test
    @DisplayName("测试根据ID查询实例")
    void testSelectRunInstanceById() {
        // 模拟Mapper层方法返回
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(runInstanceEntity);

        // 执行测试方法
        RunInstanceDto result = runInstanceService.selectRunInstanceById(1L);

        // 断言结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(100L, result.getEnvcPlanId());
        assertEquals(200L, result.getEnvcTaskId());
        assertEquals(0, result.getResult());
        assertEquals(1, result.getState());
        assertEquals(2, result.getFrom());
        assertEquals("测试用户", result.getStarterName());
        assertEquals(1001L, result.getStarterId());
        assertEquals(1000L, result.getElapsedTime());

        // 验证方法调用
        verify(runInstanceMapper, times(1)).selectRunInstanceById(1L);
    }

    @Test
    @DisplayName("测试查询实例列表")
    void testSelectRunInstanceList() {
        // 创建查询条件
        RunInstanceQueryDto queryDto = new RunInstanceQueryDto();
        queryDto.setEnvcPlanId(100L);
        queryDto.setResult(0);
        queryDto.setState(1);

        // 模拟Mapper层方法返回
        when(runInstanceMapper.selectRunInstanceList(any(RunInstanceEntity.class))).thenReturn(runInstanceEntityList);

        try (MockedStatic<PageDataUtil> pageDataUtilMockedStatic = mockStatic(PageDataUtil.class)) {
            // 模拟PageDataUtil.toDtoPage方法
            pageDataUtilMockedStatic.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunInstanceDto.class)))
                    .thenReturn(pageInfo);

            // 执行测试方法
            PageInfo<RunInstanceDto> result = runInstanceService.selectRunInstanceList(queryDto, 1, 10);

            // 断言结果
            assertNotNull(result);
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getPageNum());
            assertEquals(10, result.getPageSize());

            // 验证方法调用
            verify(runInstanceMapper, times(1)).selectRunInstanceList(any(RunInstanceEntity.class));
            pageDataUtilMockedStatic.verify(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunInstanceDto.class)), times(1));
        }
    }

    @Test
    @DisplayName("测试新增实例")
    void testInsertRunInstance() {
        // 模拟Mapper层方法返回
        when(runInstanceMapper.insertRunInstance(any(RunInstanceEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = runInstanceService.insertRunInstance(runInstanceDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).insertRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试修改实例")
    void testUpdateRunInstance() {
        // 模拟Mapper层方法返回
        when(runInstanceMapper.updateRunInstance(any(RunInstanceEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = runInstanceService.updateRunInstance(runInstanceDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).updateRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试批量删除实例")
    void testDeleteRunInstanceByIds() {
        // 准备测试参数
        Long[] ids = new Long[]{1L, 2L, 3L};

        // 模拟Mapper层方法返回
        doReturn(3).when(runInstanceMapper).deleteRunInstanceByIds(any(Long[].class));

        // 执行测试方法
        int result = runInstanceService.deleteRunInstanceByIds(ids);

        // 断言结果
        assertEquals(3, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).deleteRunInstanceByIds(ids);
    }

    @Test
    @DisplayName("测试根据ID查询实例 - 返回null")
    void testSelectRunInstanceById_ReturnNull() {
        // 模拟Mapper层方法返回null
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(null);

        // 执行测试方法
        RunInstanceDto result = runInstanceService.selectRunInstanceById(999L);

        // 断言结果 - BeanUtils.copy(null, class)会返回一个空对象而不是null
        assertNotNull(result);
        assertEquals(null, result.getId());

        // 验证方法调用
        verify(runInstanceMapper, times(1)).selectRunInstanceById(999L);
    }

    @Test
    @DisplayName("测试查询实例列表 - 空查询条件")
    void testSelectRunInstanceList_NullQuery() {
        // 模拟Mapper层方法返回
        when(runInstanceMapper.selectRunInstanceList(any(RunInstanceEntity.class))).thenReturn(runInstanceEntityList);

        try (MockedStatic<PageDataUtil> pageDataUtilMockedStatic = mockStatic(PageDataUtil.class)) {
            // 模拟PageDataUtil.toDtoPage方法
            pageDataUtilMockedStatic.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunInstanceDto.class)))
                    .thenReturn(pageInfo);

            // 执行测试方法 - 传入null查询条件
            PageInfo<RunInstanceDto> result = runInstanceService.selectRunInstanceList(null, 1, 10);

            // 断言结果
            assertNotNull(result);

            // 验证方法调用
            verify(runInstanceMapper, times(1)).selectRunInstanceList(any(RunInstanceEntity.class));
        }
    }

    @Test
    @DisplayName("测试查询实例列表 - 空结果")
    void testSelectRunInstanceList_EmptyResult() {
        // 创建查询条件
        RunInstanceQueryDto queryDto = new RunInstanceQueryDto();
        queryDto.setEnvcPlanId(999L);

        // 模拟Mapper层方法返回空列表
        when(runInstanceMapper.selectRunInstanceList(any(RunInstanceEntity.class))).thenReturn(new ArrayList<>());

        try (MockedStatic<PageDataUtil> pageDataUtilMockedStatic = mockStatic(PageDataUtil.class)) {
            // 模拟PageDataUtil.toDtoPage方法返回空分页
            PageInfo<RunInstanceDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
            pageDataUtilMockedStatic.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunInstanceDto.class)))
                    .thenReturn(emptyPageInfo);

            // 执行测试方法
            PageInfo<RunInstanceDto> result = runInstanceService.selectRunInstanceList(queryDto, 1, 10);

            // 断言结果
            assertNotNull(result);
            assertEquals(0, result.getList().size());

            // 验证方法调用
            verify(runInstanceMapper, times(1)).selectRunInstanceList(any(RunInstanceEntity.class));
        }
    }

    @Test
    @DisplayName("测试新增实例 - 插入失败")
    void testInsertRunInstance_Fail() {
        // 模拟Mapper层方法返回0表示失败
        when(runInstanceMapper.insertRunInstance(any(RunInstanceEntity.class))).thenReturn(0);

        // 执行测试方法
        int result = runInstanceService.insertRunInstance(runInstanceDto);

        // 断言结果
        assertEquals(0, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).insertRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试新增实例 - 空对象")
    void testInsertRunInstance_NullDto() {
        // 模拟Mapper层方法
        when(runInstanceMapper.insertRunInstance(any(RunInstanceEntity.class))).thenReturn(1);

        // 执行测试方法 - 传入null
        int result = runInstanceService.insertRunInstance(null);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).insertRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试修改实例 - 更新失败")
    void testUpdateRunInstance_Fail() {
        // 模拟Mapper层方法返回0表示失败
        when(runInstanceMapper.updateRunInstance(any(RunInstanceEntity.class))).thenReturn(0);

        // 执行测试方法
        int result = runInstanceService.updateRunInstance(runInstanceDto);

        // 断言结果
        assertEquals(0, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).updateRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试修改实例 - 空对象")
    void testUpdateRunInstance_NullDto() {
        // 模拟Mapper层方法
        when(runInstanceMapper.updateRunInstance(any(RunInstanceEntity.class))).thenReturn(1);

        // 执行测试方法 - 传入null
        int result = runInstanceService.updateRunInstance(null);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).updateRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试批量删除实例 - 空ID数组")
    void testDeleteRunInstanceByIds_EmptyArray() {
        // 准备测试参数 - 空数组
        Long[] ids = new Long[]{};

        // 模拟Mapper层方法返回
        doReturn(0).when(runInstanceMapper).deleteRunInstanceByIds(any(Long[].class));

        // 执行测试方法
        int result = runInstanceService.deleteRunInstanceByIds(ids);

        // 断言结果
        assertEquals(0, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).deleteRunInstanceByIds(ids);
    }

    @Test
    @DisplayName("测试批量删除实例 - null数组")
    void testDeleteRunInstanceByIds_NullArray() {
        // 模拟Mapper层方法返回
        doReturn(0).when(runInstanceMapper).deleteRunInstanceByIds(any());

        // 执行测试方法 - 传入null
        int result = runInstanceService.deleteRunInstanceByIds(null);

        // 断言结果
        assertEquals(0, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).deleteRunInstanceByIds(null);
    }

    @Test
    @DisplayName("测试查询实例列表 - 大分页参数")
    void testSelectRunInstanceList_LargePageParams() {
        // 创建查询条件
        RunInstanceQueryDto queryDto = new RunInstanceQueryDto();
        queryDto.setEnvcPlanId(100L);

        // 模拟Mapper层方法返回
        when(runInstanceMapper.selectRunInstanceList(any(RunInstanceEntity.class))).thenReturn(runInstanceEntityList);

        try (MockedStatic<PageDataUtil> pageDataUtilMockedStatic = mockStatic(PageDataUtil.class)) {
            // 模拟PageDataUtil.toDtoPage方法
            pageDataUtilMockedStatic.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(RunInstanceDto.class)))
                    .thenReturn(pageInfo);

            // 执行测试方法 - 使用大分页参数
            PageInfo<RunInstanceDto> result = runInstanceService.selectRunInstanceList(queryDto, 999, 1000);

            // 断言结果
            assertNotNull(result);

            // 验证方法调用
            verify(runInstanceMapper, times(1)).selectRunInstanceList(any(RunInstanceEntity.class));
        }
    }

    @Test
    @DisplayName("测试根据ID查询实例 - 无效ID")
    void testSelectRunInstanceById_InvalidId() {
        // 模拟Mapper层方法返回null
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(null);

        // 执行测试方法 - 传入负数ID
        RunInstanceDto result = runInstanceService.selectRunInstanceById(-1L);

        // 断言结果 - BeanUtils.copy(null, class)会返回一个空对象而不是null
        assertNotNull(result);
        assertEquals(null, result.getId());

        // 验证方法调用
        verify(runInstanceMapper, times(1)).selectRunInstanceById(-1L);
    }

    @Test
    @DisplayName("测试新增实例 - 部分字段为空")
    void testInsertRunInstance_PartialFields() {
        // 创建部分字段为空的DTO
        RunInstanceDto partialDto = new RunInstanceDto();
        partialDto.setId(1L);
        partialDto.setEnvcPlanId(100L);
        // 其他字段为null

        // 模拟Mapper层方法
        when(runInstanceMapper.insertRunInstance(any(RunInstanceEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = runInstanceService.insertRunInstance(partialDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).insertRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试修改实例 - 部分字段为空")
    void testUpdateRunInstance_PartialFields() {
        // 创建部分字段为空的DTO
        RunInstanceDto partialDto = new RunInstanceDto();
        partialDto.setId(1L);
        partialDto.setResult(1); // 只设置部分字段

        // 模拟Mapper层方法
        when(runInstanceMapper.updateRunInstance(any(RunInstanceEntity.class))).thenReturn(1);

        // 执行测试方法
        int result = runInstanceService.updateRunInstance(partialDto);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).updateRunInstance(any(RunInstanceEntity.class));
    }

    @Test
    @DisplayName("测试批量删除实例 - 单个ID")
    void testDeleteRunInstanceByIds_SingleId() {
        // 准备测试参数 - 单个ID
        Long[] ids = new Long[]{1L};

        // 模拟Mapper层方法返回
        doReturn(1).when(runInstanceMapper).deleteRunInstanceByIds(any(Long[].class));

        // 执行测试方法
        int result = runInstanceService.deleteRunInstanceByIds(ids);

        // 断言结果
        assertEquals(1, result);

        // 验证方法调用
        verify(runInstanceMapper, times(1)).deleteRunInstanceByIds(ids);
    }
} 