package com.ideal.envc.service.impl;

import com.ideal.envc.component.TaskCounterComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.model.SystemComputerListQueryIo;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.*;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.service.IStartContrastBaseService;
import com.ideal.envc.service.IStartContrastCommonBaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StartContrastCommonServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StartContrastCommonServiceImpl单元测试")
class StartContrastCommonServiceImplTest {

    @Mock
    private IStartContrastBaseService startContrastBaseService;

    @Mock
    private SystemInteract systemInteract;

    @Mock
    private IStartContrastCommonBaseService startContrastCommonBaseService;

    @Mock
    private TaskCounterComponent taskCounterComponent;

    @Mock
    private RunInstanceMapper runInstanceMapper;

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private RunFlowMapper runFlowMapper;

    private StartContrastCommonServiceImpl startContrastCommonService;

    private List<StartPlanBean> startPlanList;
    private Long userId;
    private String userName;
    private Integer from;
    private String startType;
    private UserDto userDto;
    private List<HierarchicalRunInstanceBean> hierarchicalInstanceList;
    private Map<Long, String> centerMap;
    private Map<Long, ComputerInfoDto> computerInfoDtoMap;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        userId = 1L;
        userName = "测试用户";
        from = 2; // 手动触发
        startType = "测试启动";
        
        userDto = new UserDto();
        userDto.setId(userId);
        userDto.setFullName(userName);
        
        // 初始化方案列表
        startPlanList = Arrays.asList(
            createStartPlanBean(1L, "方案1"),
            createStartPlanBean(2L, "方案2")
        );
        
        // 初始化层次化实例列表
        hierarchicalInstanceList = Arrays.asList(
            createHierarchicalRunInstanceBean(101L),
            createHierarchicalRunInstanceBean(102L)
        );
        
        // 初始化中心映射
        centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        centerMap.put(2L, "中心2");
        
        // 初始化计算机信息映射
        computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));
        computerInfoDtoMap.put(102L, createComputerInfoDto(102L, "*************", "Windows"));

        startContrastCommonService = new StartContrastCommonServiceImpl(
            startContrastBaseService,
            systemInteract,
            startContrastCommonBaseService,
            taskCounterComponent,
            runInstanceMapper,
            runInstanceInfoMapper,
            runRuleMapper,
            runFlowMapper
        );
    }

    @Test
    @DisplayName("通用启动处理方法 - 成功场景")
    void processStart_Success() throws EngineServiceException, ContrastBusinessException {
        // 准备测试数据
        List<StartTaskFlowDto> startTaskFlowDtoList = Arrays.asList(
            createStartTaskFlowDto(101L)
        );
        
        // Mock依赖
        when(systemInteract.getCenterMap(any(CenterQueryDto.class))).thenReturn(centerMap);
        when(systemInteract.getComputerInfoMap(any(SystemComputerListQueryIo.class))).thenReturn(computerInfoDtoMap);
        when(startContrastBaseService.saveRunInstanceData(eq(startPlanList), eq(userId), eq(userName), eq(from), eq(-1L), any(), any()))
            .thenReturn(hierarchicalInstanceList);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(eq(hierarchicalInstanceList), eq(userDto)))
            .thenReturn(startTaskFlowDtoList);
        
        // 确保 startContrastSendAndUpdateState 方法只被调用一次
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(anyList(), eq(startType)))
            .thenReturn(true);
        
        // 执行测试
        StartResult result = startContrastCommonService.processStart(startPlanList, userId, userName, from, startType, userDto);
        
        // 验证结果
        assertTrue(result.isSuccess());
        verify(startContrastBaseService).saveRunInstanceData(eq(startPlanList), eq(userId), eq(userName), eq(from), eq(-1L), any(), any());
        verify(startContrastCommonBaseService).buildTaskFlowDtoList(eq(hierarchicalInstanceList), eq(userDto));
        verify(startContrastCommonBaseService).startContrastSendAndUpdateState(anyList(), eq(startType));
        verify(taskCounterComponent, times(1)).initInstanceCounter(anyLong(), anyInt());
        verify(taskCounterComponent, atLeastOnce()).initInstanceInfoCounter(anyLong(), anyInt());
    }
    
    @Test
    @DisplayName("通用启动处理方法 - 方案列表为空")
    void processStart_EmptyPlanList() throws EngineServiceException {
        // 执行测试
        StartResult result = startContrastCommonService.processStart(Collections.emptyList(), userId, userName, from, startType, userDto);
        
        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("未找到启动信息", result.getMessage());
        verify(startContrastBaseService, never()).saveRunInstanceData(any(), any(), any(), any(), any(), any(), any());
    }
    
    @Test
    @DisplayName("通用启动处理方法 - 中心信息为空")
    void processStart_EmptyCenterMap() throws EngineServiceException {
        // Mock依赖
        when(systemInteract.getCenterMap(any(CenterQueryDto.class))).thenReturn(Collections.emptyMap());
        
        // 执行测试
        StartResult result = startContrastCommonService.processStart(startPlanList, userId, userName, from, startType, userDto);
        
        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("平台管理服务未返回中心信息", result.getMessage());
        verify(startContrastBaseService, never()).saveRunInstanceData(any(), any(), any(), any(), any(), any(), any());
    }
    
    @Test
    @DisplayName("通用启动处理方法 - 设备信息为空")
    void processStart_EmptyComputerInfoMap() throws EngineServiceException {
        // Mock依赖
        when(systemInteract.getCenterMap(any(CenterQueryDto.class))).thenReturn(centerMap);
        when(systemInteract.getComputerInfoMap(any(SystemComputerListQueryIo.class))).thenReturn(Collections.emptyMap());
        
        // 执行测试
        StartResult result = startContrastCommonService.processStart(startPlanList, userId, userName, from, startType, userDto);
        
        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals("平台管理服务未返回设备信息", result.getMessage());
        verify(startContrastBaseService, never()).saveRunInstanceData(any(), any(), any(), any(), any(), any(), any());
    }
    
    @Test
    @DisplayName("通用启动处理方法 - 启动发送MQ失败")
    void processStart_SendMqFailed() throws EngineServiceException, ContrastBusinessException {
        // 准备测试数据
        List<StartTaskFlowDto> startTaskFlowDtoList = Arrays.asList(
            createStartTaskFlowDto(101L)
        );
        
        // Mock依赖
        when(systemInteract.getCenterMap(any(CenterQueryDto.class))).thenReturn(centerMap);
        when(systemInteract.getComputerInfoMap(any(SystemComputerListQueryIo.class))).thenReturn(computerInfoDtoMap);
        when(startContrastBaseService.saveRunInstanceData(eq(startPlanList), eq(userId), eq(userName), eq(from), eq(-1L), any(), any()))
            .thenReturn(hierarchicalInstanceList);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(eq(hierarchicalInstanceList), eq(userDto)))
            .thenReturn(startTaskFlowDtoList);
        when(startContrastCommonBaseService.startContrastSendAndUpdateState(anyList(), eq(startType)))
            .thenReturn(false);
        
        // 模拟实例和实例详情
        RunInstanceEntity instance = new RunInstanceEntity();
        instance.setId(101L);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        
        RunInstanceInfoEntity instanceInfo = new RunInstanceInfoEntity();
        instanceInfo.setId(201L);
        when(runInstanceInfoMapper.selectRunInstanceInfoById(anyLong())).thenReturn(instanceInfo);
        
        List<RunRuleEntity> rules = new ArrayList<>();
        RunRuleEntity rule = new RunRuleEntity();
        rule.setId(301L);
        rules.add(rule);
        when(runRuleMapper.selectRulesByInstanceInfoId(anyLong())).thenReturn(rules);
        
        List<RunFlowEntity> flows = new ArrayList<>();
        RunFlowEntity flow = new RunFlowEntity();
        flow.setId(401L);
        flows.add(flow);
        when(runFlowMapper.selectRunFlowList(any(RunFlowEntity.class))).thenReturn(flows);
        
        // 模拟计数器存在和清理
        when(taskCounterComponent.isInstanceCounterExists(anyLong())).thenReturn(true);
        when(taskCounterComponent.isInstanceInfoCounterExists(anyLong())).thenReturn(true);
        doNothing().when(taskCounterComponent).clearInstanceCounter(anyLong());
        doNothing().when(taskCounterComponent).clearInstanceInfoCounter(anyLong());
        
        // 执行测试
        StartResult result = startContrastCommonService.processStart(startPlanList, userId, userName, from, startType, userDto);
        
        // 验证结果
        assertTrue(result.isSuccess()); // 注意：虽然MQ发送失败，但方法仍然返回成功，失败处理在内部进行
        verify(startContrastBaseService).saveRunInstanceData(eq(startPlanList), eq(userId), eq(userName), eq(from), eq(-1L), any(), any());
        verify(startContrastCommonBaseService).buildTaskFlowDtoList(eq(hierarchicalInstanceList), eq(userDto));
        verify(startContrastCommonBaseService).startContrastSendAndUpdateState(anyList(), eq(startType));
        verify(taskCounterComponent).clearInstanceCounter(anyLong());
        verify(taskCounterComponent, atLeastOnce()).clearInstanceInfoCounter(anyLong());
        verify(runInstanceMapper).updateRunInstance(any(RunInstanceEntity.class));
        verify(runInstanceInfoMapper).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));
        verify(runRuleMapper).updateRunRule(any(RunRuleEntity.class));
        verify(runFlowMapper).updateRunFlow(any(RunFlowEntity.class));
    }
    
    @Test
    @DisplayName("通用启动处理方法 - 计数器初始化异常")
    void processStart_CounterInitException() throws EngineServiceException, ContrastBusinessException {
        // 准备测试数据
        List<StartTaskFlowDto> startTaskFlowDtoList = Arrays.asList(
            createStartTaskFlowDto(101L)
        );
        
        // Mock依赖
        when(systemInteract.getCenterMap(any(CenterQueryDto.class))).thenReturn(centerMap);
        when(systemInteract.getComputerInfoMap(any(SystemComputerListQueryIo.class))).thenReturn(computerInfoDtoMap);
        when(startContrastBaseService.saveRunInstanceData(eq(startPlanList), eq(userId), eq(userName), eq(from), eq(-1L), any(), any()))
            .thenReturn(hierarchicalInstanceList);
        when(startContrastCommonBaseService.buildTaskFlowDtoList(eq(hierarchicalInstanceList), eq(userDto)))
            .thenReturn(startTaskFlowDtoList);
        
        // 模拟计数器初始化异常
        doThrow(new ContrastBusinessException("计数器初始化失败")).when(taskCounterComponent).initInstanceCounter(anyLong(), any());
        
        // 模拟实例和实例详情
        RunInstanceEntity instance = new RunInstanceEntity();
        instance.setId(101L);
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        
        // 执行测试并验证异常
        EngineServiceException exception = assertThrows(
            EngineServiceException.class,
            () -> startContrastCommonService.processStart(startPlanList, userId, userName, from, startType, userDto)
        );
        
        // 验证异常信息
        assertTrue(exception.getMessage().contains("初始化计数器失败"));
        verify(startContrastBaseService).saveRunInstanceData(eq(startPlanList), eq(userId), eq(userName), eq(from), eq(-1L), any(), any());
        verify(startContrastCommonBaseService).buildTaskFlowDtoList(eq(hierarchicalInstanceList), eq(userDto));
        verify(taskCounterComponent).initInstanceCounter(anyLong(), any());
        verify(runInstanceMapper).updateRunInstance(any(RunInstanceEntity.class));
    }
    
    // 辅助方法 - 创建方案对象
    private StartPlanBean createStartPlanBean(Long id, String name) {
        StartPlanBean bean = new StartPlanBean();
        bean.setId(id);
        bean.setName(name);
        
        // 创建系统列表
        List<StartSystemBean> systems = new ArrayList<>();
        StartSystemBean system = new StartSystemBean();
        system.setBusinessSystemId(10L);
        system.setBusinessSystemName("测试系统");
        
        // 创建节点列表
        List<StartComputerNodeBean> nodes = new ArrayList<>();
        StartComputerNodeBean node = new StartComputerNodeBean();
        node.setId(1000L);
        node.setSourceCenterId(1L);
        node.setTargetCenterId(2L);
        node.setSourceComputerId(101L);
        node.setTargetComputerId(102L);
        node.setSourceComputerIp("*************");
        node.setTargetComputerIp("*************");
        
        // 创建规则列表
        List<StartRuleBean> rules = new ArrayList<>();
        StartRuleBean rule = new StartRuleBean();
        rule.setId(10000L);
        rule.setModel(1);
        rule.setType(1L);
        rule.setPath("/test/path");
        rule.setSourcePath("/test/source/path");
        rule.setEncode("UTF-8");
        rule.setWay(1);
        rule.setRuleType(1);
        rule.setEnabled(1);
        rule.setChildLevel(0);
        
        rules.add(rule);
        node.setRules(rules);
        nodes.add(node);
        system.setComputerNodeBeans(nodes);
        systems.add(system);
        bean.setSystems(systems);
        
        return bean;
    }
    
    // 辅助方法 - 创建层次化实例对象
    private HierarchicalRunInstanceBean createHierarchicalRunInstanceBean(Long id) {
        HierarchicalRunInstanceBean bean = new HierarchicalRunInstanceBean();
        bean.setId(id);
        bean.setEnvcPlanId(100L);
        bean.setEnvcTaskId(200L);
        bean.setResult(-1);
        bean.setState(0);
        bean.setFrom(2);
        bean.setStarterName("测试用户");
        bean.setStarterId(1L);
        bean.setStartTime(new Date());
        
        // 创建实例详情列表
        List<HierarchicalRunInstanceInfoBean> instanceInfoList = new ArrayList<>();
        HierarchicalRunInstanceInfoBean instanceInfo = new HierarchicalRunInstanceInfoBean();
        instanceInfo.setId(id + 100);
        instanceInfo.setEnvcRunInstanceId(id);
        instanceInfo.setBusinessSystemId(10L);
        instanceInfo.setSourceCenterId(1L);
        instanceInfo.setTargetCenterId(2L);
        instanceInfo.setSourceComputerId(101L);
        instanceInfo.setTargetComputerId(102L);
        instanceInfo.setState(0);
        instanceInfo.setResult(-1);
        
        // 创建规则列表
        List<HierarchicalRunRuleBean> ruleList = new ArrayList<>();
        HierarchicalRunRuleBean rule = new HierarchicalRunRuleBean();
        rule.setId(id + 200);
        rule.setEnvcRunInstanceInfoId(instanceInfo.getId());
        rule.setModel(1);
        rule.setType(1L);
        rule.setState(0);
        rule.setResult(-1);
        
        // 创建流程
        HierarchicalRunFlowBean flow = new HierarchicalRunFlowBean();
        flow.setId(id + 300);
        flow.setRunBizId(rule.getId());
        flow.setModel(1);
        flow.setState(0);
        
        rule.setRuleFlow(flow);
        ruleList.add(rule);
        instanceInfo.setRuleList(ruleList);
        instanceInfoList.add(instanceInfo);
        bean.setInstanceInfoList(instanceInfoList);
        
        return bean;
    }
    
    // 辅助方法 - 创建计算机信息DTO
    private ComputerInfoDto createComputerInfoDto(Long id, String ip, String osName) {
        ComputerInfoDto dto = new ComputerInfoDto();
        // ComputerInfoDto类中没有setComputerId和setComputerIp方法，这些属性可能需要通过其他方式设置
        // 或者在测试中可以忽略这些属性，只设置存在的属性
        dto.setAgentPort(22);
        dto.setOsName(osName);
        return dto;
    }
    
    // 辅助方法 - 创建任务流程DTO
    private StartTaskFlowDto createStartTaskFlowDto(Long id) {
        StartTaskFlowDto dto = new StartTaskFlowDto();
        dto.setUniqueTaskId(id);
        // StartTaskFlowDto类中没有setTaskName方法
        return dto;
    }
}
