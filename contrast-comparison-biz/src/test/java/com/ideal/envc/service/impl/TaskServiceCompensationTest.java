package com.ideal.envc.service.impl;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.mapper.TaskMapper;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.TaskDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.TaskEntity;
import com.ideal.envc.model.enums.TaskOperateEnums;
import com.ideal.envc.service.IJobOperateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 任务服务补偿机制单元测试
 * 专门测试createTask方法的补偿机制
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("任务服务补偿机制测试")
public class TaskServiceCompensationTest {

    @Mock
    private TaskMapper taskMapper;

    @Mock
    private IJobOperateService jobOperateService;

    @Spy
    @InjectMocks
    private TaskServiceImpl taskService;

    private TaskDto taskDto;
    private UserDto userDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        taskDto = new TaskDto();
        taskDto.setEnvcPlanId(100L);
        taskDto.setCron("0 0 12 * * ?");
        taskDto.setScheduledId(null);

        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");
    }

    @Test
    @DisplayName("测试补偿机制 - 正常流程成功")
    void testCompensationMechanism_NormalFlowSuccess() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode())).thenReturn(1);

        // 执行测试方法
        Long result = taskService.createTask(taskDto, userDto);

        // 验证结果
        assertEquals(1L, result);
        verify(taskMapper).insertTask(any(TaskEntity.class));
        verify(jobOperateService).createAndStartJob(any(ContrastScheduleJobTaskDto.class));
        verify(taskMapper).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试补偿机制 - 更新失败触发补偿成功")
    void testCompensationMechanism_UpdateFailedCompensationSuccess() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenReturn(true);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));
        
        // 验证补偿操作被调用
        verify(jobOperateService).removeJob(123);
        // 验证重试了3次
        verify(taskMapper, times(3)).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());
    }

    @Test
    @DisplayName("测试补偿机制 - 重试成功避免补偿")
    void testCompensationMechanism_RetrySuccessAvoidCompensation() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        
        // 前两次失败，第三次成功
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("第一次失败"))
                .thenThrow(new RuntimeException("第二次失败"))
                .thenReturn(1); // 第三次成功

        // 执行测试方法
        Long result = taskService.createTask(taskDto, userDto);

        // 验证结果
        assertEquals(1L, result);
        
        // 验证重试了3次
        verify(taskMapper, times(3)).updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode());
        
        // 验证没有调用补偿操作（因为重试成功了）
        verify(jobOperateService, never()).removeJob(anyInt());
    }

    @Test
    @DisplayName("测试补偿机制 - 补偿操作失败")
    void testCompensationMechanism_CompensationFailed() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenReturn(false);

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));
        
        // 验证补偿操作被调用多次（重试机制）
        verify(jobOperateService, times(2)).removeJob(123);
    }

    @Test
    @DisplayName("测试补偿机制 - 补偿操作异常")
    void testCompensationMechanism_CompensationException() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenThrow(new RuntimeException("补偿操作异常"));

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));
        
        // 验证补偿操作被调用
        verify(jobOperateService, times(2)).removeJob(123);
    }

    @Test
    @DisplayName("测试补偿机制 - 外部任务创建失败不触发补偿")
    void testCompensationMechanism_ExternalJobFailedNoCompensation() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class)))
                .thenThrow(new ScheduleJobOperateException("外部定时任务创建失败"));

        // 执行测试方法并验证异常
        ScheduleJobOperateException exception = assertThrows(ScheduleJobOperateException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertEquals("外部定时任务创建失败", exception.getMessage());
        
        // 验证没有调用补偿操作（因为外部任务创建失败，不需要补偿）
        verify(jobOperateService, never()).removeJob(anyInt());
        verify(taskMapper, never()).updateTaskScheduledIdAndState(anyLong(), anyLong(), anyInt());
    }

    @Test
    @DisplayName("测试补偿机制 - 线程中断处理")
    void testCompensationMechanism_ThreadInterrupted() throws Exception {
        // Mock方法调用
        doNothing().when(taskService).checkTaskRule(taskDto);
        when(taskMapper.insertTask(any(TaskEntity.class))).thenAnswer(invocation -> {
            TaskEntity task = invocation.getArgument(0);
            task.setId(1L);
            return 1;
        });
        when(jobOperateService.createAndStartJob(any(ContrastScheduleJobTaskDto.class))).thenReturn(123);
        when(taskMapper.updateTaskScheduledIdAndState(1L, 123L, TaskOperateEnums.START.getCode()))
                .thenThrow(new RuntimeException("数据库更新失败"));
        when(jobOperateService.removeJob(123)).thenReturn(true);

        // 在测试执行前中断当前线程
        Thread.currentThread().interrupt();

        // 执行测试方法并验证异常
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, () -> {
            taskService.createTask(taskDto, userDto);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("更新任务状态失败"));
        
        // 验证线程中断状态被保持
        assertTrue(Thread.interrupted()); // 清除中断状态
    }
}
