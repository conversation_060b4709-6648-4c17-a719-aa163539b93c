package com.ideal.envc.service;

import com.ideal.envc.model.dto.FileInfoDto;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.service.impl.HtmlComparisonServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * HTML比对Excel导出功能测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HTML比对Excel导出功能测试")
public class HtmlComparisonExcelExportTest {

    @Mock
    private IFileComparisonService fileComparisonService;

    @Mock
    private com.ideal.envc.mapper.RunFlowResultMapper runFlowResultMapper;

    @Mock
    private com.ideal.envc.mapper.RunRuleMapper runRuleMapper;

    @InjectMocks
    private HtmlComparisonServiceImpl htmlComparisonService;

    /**
     * 测试HTML比对Excel导出功能
     */
    @Test
    @DisplayName("测试HTML比对Excel导出功能")
    public void testHtmlComparisonExcelExport() {
        try {
            // 创建测试数据
            HtmlComparisonRequestDto request = createTestRequest();
            HtmlComparisonResultDto result = createTestResult();

            // 创建Mock响应
            MockHttpServletResponse response = new MockHttpServletResponse();

            // Mock导出方法不抛异常
            doNothing().when(htmlComparisonService).exportHtmlComparisonResult(any(HtmlComparisonRequestDto.class), any(HtmlComparisonResultDto.class), any(MockHttpServletResponse.class));

            // 执行导出
            htmlComparisonService.exportHtmlComparisonResult(request, result, response);

            // 验证响应
            System.out.println("=== Excel导出测试结果 ===");
            System.out.println("Content-Type: " + response.getContentType());
            System.out.println("Content-Length: " + response.getContentLength());
            System.out.println("Headers: " + response.getHeaderNames());

            // 保存到本地文件进行验证
            byte[] content = response.getContentAsByteArray();
            if (content.length > 0) {
                try (FileOutputStream fos = new FileOutputStream("test_html_comparison_export.xlsx")) {
                    fos.write(content);
                    System.out.println("Excel文件已保存到: test_html_comparison_export.xlsx");
                    System.out.println("文件大小: " + content.length + " bytes");
                }
            }

        } catch (Exception e) {
            System.err.println("Excel导出测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建测试请求
     */
    private HtmlComparisonRequestDto createTestRequest() {
        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(12345L);
        request.setBaselineServer("生产环境服务器");
        request.setTargetServer("测试环境服务器");
        request.setBaseServerIp("*************");
        request.setTargetServerIp("*************");
        request.setDescription("生产环境与测试环境一致性比对");
        return request;
    }
    
    /**
     * 创建测试结果
     */
    private HtmlComparisonResultDto createTestResult() {
        HtmlComparisonResultDto result = new HtmlComparisonResultDto();
        
        // 基本统计信息
        result.setTotalHtmlRows(150);
        result.setTotalSourceFiles(100);
        result.setTotalTargetFiles(95);
        result.setConsistentCount(80);
        result.setInconsistentCount(10);
        result.setMissingCount(10);
        result.setExtraCount(5);
        result.setConsistentRate(new BigDecimal(80));
        result.setInconsistentRate(new BigDecimal(10));
        result.setMissingRate(new BigDecimal(10));
        result.setExtraRate(new BigDecimal(5));
        
        // 服务器信息
        result.setBaselineServer("生产环境服务器");
        result.setTargetServer("测试环境服务器");
        result.setDescription("生产环境与测试环境一致性比对");
        
        // 不一致文本行列表（模拟HTML解析结果）
        List<FileInfoDto> inconsistentFiles = new ArrayList<>();
        for (int i = 1; i <= 10; i++) {
            FileInfoDto file = new FileInfoDto();
            // 模拟HTML解析出的行号和内容格式
            file.setFilePath(i + ":server.port=" + (8080 + i));
            file.setStatus("不一致");
            file.setRemark("配置参数值不同");
            inconsistentFiles.add(file);
        }
        result.setInconsistentFiles(inconsistentFiles);
        
        // 缺失文本行列表
        List<FileInfoDto> missingFiles = new ArrayList<>();
        for (int i = 11; i <= 20; i++) {
            FileInfoDto file = new FileInfoDto();
            // 模拟HTML解析出的缺失行
            file.setFilePath(i + ":database.url=**************************" + (i % 10) + "/test");
            file.setStatus("缺失");
            file.setRemark("目标配置中缺失此行");
            missingFiles.add(file);
        }
        result.setMissingFiles(missingFiles);
        
        // 多出文本行列表
        List<FileInfoDto> extraFiles = new ArrayList<>();
        for (int i = 21; i <= 25; i++) {
            FileInfoDto file = new FileInfoDto();
            // 模拟HTML解析出的多出行
            file.setFilePath(i + ":logging.level.com.example=DEBUG");
            file.setStatus("多出");
            file.setRemark("目标配置中多出此行");
            extraFiles.add(file);
        }
        result.setExtraFiles(extraFiles);
        
        // 一致文本行列表
        List<FileInfoDto> consistentFiles = new ArrayList<>();
        for (int i = 26; i <= 105; i++) {
            FileInfoDto file = new FileInfoDto();
            // 模拟HTML解析出的一致行
            file.setFilePath(i + ":spring.application.name=test-app");
            file.setStatus("一致");
            file.setRemark("内容完全一致");
            consistentFiles.add(file);
        }
        result.setConsistentFiles(consistentFiles);
        
        return result;
    }
    
    /**
     * 测试大数据量Excel导出
     */
    @Test
    public void testLargeDataExcelExport() {
        try {
            System.out.println("=== 大数据量Excel导出测试 ===");
            
            // 创建大量测试数据
            HtmlComparisonRequestDto request = createTestRequest();
            HtmlComparisonResultDto result = createLargeTestResult();
            
            long startTime = System.currentTimeMillis();
            
            // 创建Mock响应
            MockHttpServletResponse response = new MockHttpServletResponse();
            
            // 执行导出
            htmlComparisonService.exportHtmlComparisonResult(request, result, response);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            // 验证结果
            byte[] content = response.getContentAsByteArray();
            System.out.println("导出耗时: " + duration + "ms");
            System.out.println("文件大小: " + content.length + " bytes");
            System.out.println("总文件数: " + result.getTotalSourceFiles());
            System.out.println("平均处理速度: " + (result.getTotalSourceFiles() * 1000 / duration) + " 文件/秒");
            
            // 保存大数据量测试文件
            if (content.length > 0) {
                try (FileOutputStream fos = new FileOutputStream("test_large_html_comparison_export.xlsx")) {
                    fos.write(content);
                    System.out.println("大数据量Excel文件已保存到: test_large_html_comparison_export.xlsx");
                }
            }
            
        } catch (Exception e) {
            System.err.println("大数据量Excel导出测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建大量测试数据
     */
    private HtmlComparisonResultDto createLargeTestResult() {
        HtmlComparisonResultDto result = new HtmlComparisonResultDto();
        
        // 基本统计信息
        result.setTotalHtmlRows(5000);
        result.setTotalSourceFiles(2000);
        result.setTotalTargetFiles(1950);
        result.setConsistentCount(1800);
        result.setInconsistentCount(100);
        result.setMissingCount(100);
        result.setExtraCount(50);
        result.setConsistentRate(new BigDecimal(90));
        result.setInconsistentRate(new BigDecimal(5));
        result.setMissingRate(new BigDecimal(5));
        result.setExtraRate(new BigDecimal("2.5"));
        
        // 服务器信息
        result.setBaselineServer("生产环境服务器");
        result.setTargetServer("测试环境服务器");
        result.setDescription("大数据量环境一致性比对测试");
        
        // 创建大量不一致文件
        List<FileInfoDto> inconsistentFiles = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            FileInfoDto file = new FileInfoDto();
            file.setFilePath("/usr/app/large/config/application" + i + ".properties");
            file.setFileSize((1024 + i * 100) + " B");
            file.setPermissions("-rw-r--r--");
            file.setMd5("large123def456" + String.format("%06d", i));
            file.setStatus("不一致");
            file.setRemark("大数据量测试-配置差异");
            inconsistentFiles.add(file);
        }
        result.setInconsistentFiles(inconsistentFiles);
        
        // 创建大量缺失文件
        List<FileInfoDto> missingFiles = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            FileInfoDto file = new FileInfoDto();
            file.setFilePath("/usr/app/large/lib/missing-lib" + i + ".jar");
            file.setFileSize((2048 + i * 200) + " B");
            file.setPermissions("-rw-r--r--");
            file.setMd5("largemissing123" + String.format("%06d", i));
            file.setStatus("缺失");
            file.setRemark("大数据量测试-目标环境缺失");
            missingFiles.add(file);
        }
        result.setMissingFiles(missingFiles);
        
        // 创建大量多出文件
        List<FileInfoDto> extraFiles = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            FileInfoDto file = new FileInfoDto();
            file.setFilePath("/usr/app/large/temp/extra-file" + i + ".tmp");
            file.setFileSize((512 + i * 50) + " B");
            file.setPermissions("-rw-r--r--");
            file.setMd5("largeextra456" + String.format("%06d", i));
            file.setStatus("多出");
            file.setRemark("大数据量测试-目标环境多出");
            extraFiles.add(file);
        }
        result.setExtraFiles(extraFiles);
        
        // 创建大量一致文件
        List<FileInfoDto> consistentFiles = new ArrayList<>();
        for (int i = 1; i <= 1800; i++) {
            FileInfoDto file = new FileInfoDto();
            file.setFilePath("/usr/app/large/bin/consistent-file" + i + ".class");
            file.setFileSize((4096 + i * 10) + " B");
            file.setPermissions("-rw-r--r--");
            file.setMd5("largeconsistent789" + String.format("%06d", i));
            file.setStatus("一致");
            file.setRemark("大数据量测试-完全一致");
            consistentFiles.add(file);
        }
        result.setConsistentFiles(consistentFiles);
        
        return result;
    }
    
    /**
     * 测试Excel格式验证
     */
    @Test
    public void testExcelFormatValidation() {
        System.out.println("=== Excel格式验证测试 ===");
        
        try {
            HtmlComparisonRequestDto request = createTestRequest();
            HtmlComparisonResultDto result = createTestResult();
            
            MockHttpServletResponse response = new MockHttpServletResponse();
            htmlComparisonService.exportHtmlComparisonResult(request, result, response);
            
            // 验证响应头
            System.out.println("Content-Type验证: " +
                ("application/json".equals(response.getContentType()) ? "✓" : "✗"));
            System.out.println("字符编码验证: " +
                ("utf-8".equals(response.getCharacterEncoding()) ? "✓" : "✗"));
            
            // 验证文件名
            String contentDisposition = response.getHeader("Content-disposition");
            System.out.println("文件名验证: " + 
                (contentDisposition != null && contentDisposition.contains("环境一致性比对结果") ? "✓" : "✗"));
            
            // 验证文件内容
            byte[] content = response.getContentAsByteArray();
            System.out.println("文件内容验证: " + (content.length > 0 ? "✓" : "✗"));
            System.out.println("Excel魔数验证: " + 
                (content.length > 4 && content[0] == 0x50 && content[1] == 0x4B ? "✓" : "✗"));
            
        } catch (Exception e) {
            System.err.println("Excel格式验证测试失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
