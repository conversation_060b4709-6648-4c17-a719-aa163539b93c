package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.model.bean.NodeRelationListBean;
import com.ideal.envc.model.dto.NodeRelationDto;
import com.ideal.envc.model.dto.NodeRelationListDto;
import com.ideal.envc.model.dto.NodeRelationQueryDto;
import com.ideal.envc.model.dto.NodeRelationSaveDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.model.entity.NodeRuleContentEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 节点关系规则Service业务层的单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("NodeRelationServiceImpl单元测试")
public class NodeRelationServiceImplTest {

    @Mock
    private NodeRelationMapper nodeRelationMapper;

    @Mock
    private NodeRuleContentMapper nodeRuleContentMapper;

    @Mock
    private BatchHandler batchHandler;

    @InjectMocks
    private NodeRelationServiceImpl nodeRelationService;

    private NodeRelationDto nodeRelationDto;
    private NodeRelationEntity nodeRelationEntity;
    private NodeRelationQueryDto nodeRelationQueryDto;
    private NodeRelationSaveDto nodeRelationSaveDto;
    private NodeRelationListBean nodeRelationListBean;
    private NodeRelationListDto nodeRelationListDto;
    private NodeRuleContentEntity nodeRuleContentEntity;
    private UserDto userDto;
    private List<NodeRelationEntity> nodeRelationEntityList;
    private List<NodeRelationListBean> nodeRelationListBeanList;
    private List<NodeRelationListDto> nodeRelationListDtoList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        nodeRelationDto = new NodeRelationDto();
        nodeRelationDto.setId(1L);
        nodeRelationDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationDto.setModel(1);
        nodeRelationDto.setType(1L);
        nodeRelationDto.setPath("/test/path");
        nodeRelationDto.setSourcePath("/source/path");
        nodeRelationDto.setEncode("UTF-8");
        nodeRelationDto.setWay(1);
        nodeRelationDto.setRuleType(1);
        nodeRelationDto.setEnabled(1);
        nodeRelationDto.setChildLevel(1);
        nodeRelationDto.setCreatorId(1L);
        nodeRelationDto.setCreatorName("测试用户");
        nodeRelationDto.setCreateTime(new Date());
        nodeRelationDto.setUpdatorId(1L);
        nodeRelationDto.setUpdatorName("测试用户");
        nodeRelationDto.setUpdateTime(new Date());

        nodeRelationEntity = new NodeRelationEntity();
        nodeRelationEntity.setId(1L);
        nodeRelationEntity.setEnvcSystemComputerNodeId(100L);
        nodeRelationEntity.setModel(1);
        nodeRelationEntity.setType(1L);
        nodeRelationEntity.setPath("/test/path");
        nodeRelationEntity.setSourcePath("/source/path");
        nodeRelationEntity.setEncode("UTF-8");
        nodeRelationEntity.setWay(1);
        nodeRelationEntity.setRuleType(1);
        nodeRelationEntity.setEnabled(1);
        nodeRelationEntity.setChildLevel(1);
        nodeRelationEntity.setCreatorId(1L);
        nodeRelationEntity.setCreatorName("测试用户");
        nodeRelationEntity.setCreateTime(new Date());
        nodeRelationEntity.setUpdatorId(1L);
        nodeRelationEntity.setUpdatorName("测试用户");
        nodeRelationEntity.setUpdateTime(new Date());

        nodeRelationQueryDto = new NodeRelationQueryDto();
        nodeRelationQueryDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationQueryDto.setModel(1);

        nodeRelationSaveDto = new NodeRelationSaveDto();
        nodeRelationSaveDto.setId(1L);
        nodeRelationSaveDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationSaveDto.setModel(1);
        nodeRelationSaveDto.setType(1L);
        nodeRelationSaveDto.setPath("/test/path");
        nodeRelationSaveDto.setSourcePath("/source/path");
        nodeRelationSaveDto.setEncode("UTF-8");
        nodeRelationSaveDto.setWay(1);
        nodeRelationSaveDto.setRuleType(1);
        nodeRelationSaveDto.setEnabled(1);
        nodeRelationSaveDto.setChildLevel(1);
        nodeRelationSaveDto.setContent("test content");

        nodeRelationListBean = new NodeRelationListBean();
        nodeRelationListBean.setId(1L);
        nodeRelationListBean.setEnvcSystemComputerNodeId(100L);
        nodeRelationListBean.setModel(1);
        nodeRelationListBean.setType(1L);
        nodeRelationListBean.setPath("/test/path");

        nodeRelationListDto = new NodeRelationListDto();
        nodeRelationListDto.setId(1L);
        nodeRelationListDto.setEnvcSystemComputerNodeId(100L);
        nodeRelationListDto.setModel(1);
        nodeRelationListDto.setType(1L);
        nodeRelationListDto.setPath("/test/path");

        nodeRuleContentEntity = new NodeRuleContentEntity();
        nodeRuleContentEntity.setId(1L);
        nodeRuleContentEntity.setEnvcNodeRelationId(1L);
        nodeRuleContentEntity.setRuleContent("test content");

        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setLoginName("testuser");
        userDto.setFullName("测试用户");
        userDto.setOrgCode("TEST_ORG");

        nodeRelationEntityList = new ArrayList<>();
        nodeRelationEntityList.add(nodeRelationEntity);

        nodeRelationListBeanList = new ArrayList<>();
        nodeRelationListBeanList.add(nodeRelationListBean);

        nodeRelationListDtoList = new ArrayList<>();
        nodeRelationListDtoList.add(nodeRelationListDto);
    }

    @Test
    @DisplayName("根据ID查询节点关系 - 成功场景")
    void selectNodeRelationById_Success() {
        // 准备测试数据
        Long relationId = 1L;
        NodeRelationEntity entity = createNodeRelationEntity(relationId);

        // Mock依赖
        when(nodeRelationMapper.selectNodeRelationById(relationId)).thenReturn(entity);

        // 执行测试
        NodeRelationDto result = nodeRelationService.selectNodeRelationById(relationId);

        // 验证结果
        assertNotNull(result);
        assertEquals(relationId, result.getId());
        verify(nodeRelationMapper).selectNodeRelationById(relationId);
    }

    @Test
    @DisplayName("查询节点关系列表 - 成功场景")
    void selectNodeRelationList_Success() {
        // 准备测试数据
        NodeRelationQueryDto queryDto = new NodeRelationQueryDto();
        List<NodeRelationListBean> beanList = Arrays.asList(
            createNodeRelationListBean(1L),
            createNodeRelationListBean(2L)
        );

        // Mock依赖
        when(nodeRelationMapper.selectNodeRelationList(any())).thenReturn(beanList);

        // 执行测试
        PageInfo<NodeRelationListDto> result = nodeRelationService.selectNodeRelationList(queryDto, 1, 10);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getList().size());
        verify(nodeRelationMapper).selectNodeRelationList(any());
    }

    @Test
    @DisplayName("新增节点关系 - 成功场景")
    void insertNodeRelation_Success() {
        // 准备测试数据
        NodeRelationSaveDto saveDto = createNodeRelationSaveDto(null);
        UserDto userDto = createUserDto(1L);

        // Mock依赖
        when(nodeRelationMapper.insertNodeRelation(any())).thenReturn(1);
        when(nodeRuleContentMapper.insertNodeRuleContent(any())).thenReturn(1);

        // 执行测试
        int result = nodeRelationService.insertNodeRelation(saveDto, userDto);

        // 验证结果
        assertEquals(1, result);
        verify(nodeRelationMapper).insertNodeRelation(any());
        verify(nodeRuleContentMapper).insertNodeRuleContent(any());
    }

    @Test
    @DisplayName("新增节点关系 - 参数为空")
    void insertNodeRelation_NullParameter() {
        // 准备测试数据
        UserDto userDto = createUserDto(1L);

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> nodeRelationService.insertNodeRelation(null, userDto)
        );

        // 验证结果
        assertEquals("参数不能为空", exception.getMessage());
        verify(nodeRelationMapper, never()).insertNodeRelation(any());
    }

    @Test
    @DisplayName("修改节点关系 - 成功场景")
    void updateNodeRelation_Success() throws ContrastBusinessException {
        // 准备测试数据
        NodeRelationSaveDto saveDto = createNodeRelationSaveDto(1L);
        UserDto userDto = createUserDto(1L);
        NodeRelationEntity existingEntity = createNodeRelationEntity(1L);

        // Mock依赖
        when(nodeRelationMapper.selectNodeRelationById(1L)).thenReturn(existingEntity);
        when(nodeRelationMapper.updateNodeRelation(any())).thenReturn(1);
        when(nodeRuleContentMapper.selectNodeRuleContentList(any())).thenReturn(Collections.emptyList());
        when(nodeRuleContentMapper.insertNodeRuleContent(any())).thenReturn(1);

        // 执行测试
        int result = nodeRelationService.updateNodeRelation(saveDto, userDto);

        // 验证结果
        assertEquals(1, result);
        verify(nodeRelationMapper).selectNodeRelationById(1L);
        verify(nodeRelationMapper).updateNodeRelation(any());
        verify(nodeRuleContentMapper).selectNodeRuleContentList(any());
        verify(nodeRuleContentMapper).insertNodeRuleContent(any());
    }

    @Test
    @DisplayName("修改节点关系 - 记录不存在")
    void updateNodeRelation_NotFound() {
        // 准备测试数据
        NodeRelationSaveDto saveDto = createNodeRelationSaveDto(1L);
        UserDto userDto = createUserDto(1L);

        // Mock依赖
        when(nodeRelationMapper.selectNodeRelationById(1L)).thenReturn(null);

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> nodeRelationService.updateNodeRelation(saveDto, userDto)
        );

        // 验证结果
        assertEquals("未找到要修改的记录", exception.getMessage());
        verify(nodeRelationMapper).selectNodeRelationById(1L);
        verify(nodeRelationMapper, never()).updateNodeRelation(any());
    }

    @Test
    @DisplayName("批量删除节点关系 - 成功场景")
    void deleteNodeRelationByIds_Success() {
        // 准备测试数据
        Long[] ids = {1L, 2L};

        // Mock依赖
        when(nodeRelationMapper.deleteNodeRelationByIds(ids)).thenReturn(2);
        when(nodeRuleContentMapper.deleteNodeRuleContentByNodeRelationIds(ids)).thenReturn(2);

        // 执行测试
        int result = nodeRelationService.deleteNodeRelationByIds(ids);

        // 验证结果
        assertEquals(2, result);
        verify(nodeRelationMapper).deleteNodeRelationByIds(ids);
        verify(nodeRuleContentMapper).deleteNodeRuleContentByNodeRelationIds(ids);
    }

    @Test
    @DisplayName("批量更新节点关系启用状态 - 成功场景")
    void updateNodeRelationEnabledByIds_Success() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        Integer enabled = 1; // 禁用状态
        UserDto userDto = createUserDto(1L);
        
        // 创建两个启用状态的节点关系
        NodeRelationEntity entity1 = createNodeRelationEntity(1L);
        entity1.setEnabled(0); // 设置为启用状态
        
        NodeRelationEntity entity2 = createNodeRelationEntity(2L);
        entity2.setEnabled(0); // 设置为启用状态
        
        List<NodeRelationEntity> nodeRelationList = Arrays.asList(entity1, entity2);

        // Mock依赖
        when(nodeRelationMapper.selectNodeRelationByIds(ids)).thenReturn(nodeRelationList);
        doNothing().when(batchHandler).batchData(anyList(), any(), anyInt());

        // 执行测试
        int result = nodeRelationService.updateNodeRelationEnabledByIds(ids, enabled, userDto);

        // 验证结果
        assertEquals(2, result);
        verify(nodeRelationMapper).selectNodeRelationByIds(ids);
        verify(batchHandler).batchData(anyList(), any(), anyInt());
    }

    @Test
    @DisplayName("批量更新节点关系启用状态 - 空ID数组")
    void updateNodeRelationEnabledByIds_EmptyIds() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {};
        Integer enabled = 1;
        UserDto userDto = createUserDto(1L);

        // 执行测试并验证异常
        ContrastBusinessException exception = assertThrows(
            ContrastBusinessException.class,
            () -> nodeRelationService.updateNodeRelationEnabledByIds(ids, enabled, userDto)
        );
        
        // 验证结果
        assertEquals("参数不能为空", exception.getMessage());
        verify(nodeRelationMapper, never()).selectNodeRelationByIds(any());
        verify(nodeRelationMapper, never()).updateNodeRelation(any());
    }

    private NodeRelationEntity createNodeRelationEntity(Long id) {
        NodeRelationEntity entity = new NodeRelationEntity();
        entity.setId(id);
        entity.setEnvcSystemComputerNodeId(100L);
        entity.setModel(1);
        entity.setType(1L);
        entity.setPath("/test/path");
        entity.setSourcePath("/source/path");
        entity.setEncode("UTF-8");
        entity.setWay(1);
        entity.setRuleType(1);
        entity.setEnabled(1);
        entity.setChildLevel(1);
        return entity;
    }

    private NodeRelationListBean createNodeRelationListBean(Long id) {
        NodeRelationListBean bean = new NodeRelationListBean();
        bean.setId(id);
        bean.setEnvcSystemComputerNodeId(100L);
        bean.setModel(1);
        bean.setType(1L);
        bean.setPath("/test/path");
        return bean;
    }

    private NodeRelationSaveDto createNodeRelationSaveDto(Long id) {
        NodeRelationSaveDto dto = new NodeRelationSaveDto();
        dto.setId(id);
        dto.setEnvcSystemComputerNodeId(100L);
        dto.setModel(1);
        dto.setType(1L);
        dto.setPath("/test/path");
        dto.setSourcePath("/source/path");
        dto.setEncode("UTF-8");
        dto.setWay(1);
        dto.setRuleType(1);
        dto.setEnabled(1);
        dto.setChildLevel(1);
        dto.setContent("test content");
        return dto;
    }

    private UserDto createUserDto(Long id) {
        UserDto dto = new UserDto();
        dto.setId(id);
        dto.setLoginName("testuser");
        dto.setFullName("测试用户");
        dto.setOrgCode("TEST_ORG");
        return dto;
    }
}