package com.ideal.envc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.PlanRelationMapper;
import com.ideal.envc.model.bean.PlanSystemListBean;
import com.ideal.envc.model.bean.PlanRelationListBean;
import com.ideal.envc.model.dto.*;
import com.ideal.envc.model.entity.PlanRelationEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.mockito.MockedStatic;

/**
 * PlanRelationServiceImpl单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("方案关系Service实现类测试")
class PlanRelationServiceImplTest {

    @Mock
    private PlanRelationMapper planRelationMapper;

    @Mock
    private BatchHandler batchHandler;

    @InjectMocks
    private PlanRelationServiceImpl planRelationService;

    private UserDto mockUserDto;
    private PlanRelationEntity mockPlanRelationEntity;
    private PlanRelationDto mockPlanRelationDto;
    private PlanRelationQueryDto planRelationQueryDto;
    private PlanRelationBatchDto planRelationBatchDto;
    private PlanSystemQueryDto planSystemQueryDto;

    @BeforeEach
    void setUp() {
        // 初始化用户数据
        mockUserDto = new UserDto();
        mockUserDto.setId(1L);
        mockUserDto.setFullName("测试用户");
        mockUserDto.setLoginName("testUser");
        mockUserDto.setOrgCode("TEST_ORG");

        // 初始化方案关系实体
        mockPlanRelationEntity = new PlanRelationEntity();
        mockPlanRelationEntity.setId(1L);
        mockPlanRelationEntity.setEnvcPlanId(100L);
        mockPlanRelationEntity.setBusinessSystemId(200L);
        mockPlanRelationEntity.setCreatorId(1L);
        mockPlanRelationEntity.setCreatorName("测试用户");

        // 初始化方案关系DTO
        mockPlanRelationDto = new PlanRelationDto();
        mockPlanRelationDto.setId(1L);
        mockPlanRelationDto.setEnvcPlanId(100L);
        mockPlanRelationDto.setBusinessSystemId(200L);
        mockPlanRelationDto.setCreatorId(1L);
        mockPlanRelationDto.setCreatorName("测试用户");

        // 初始化查询条件
        planRelationQueryDto = new PlanRelationQueryDto();
        planRelationQueryDto.setEnvcPlanId(100L);

        // 初始化批量保存DTO
        planRelationBatchDto = new PlanRelationBatchDto();
        planRelationBatchDto.setEnvcPlanId(100L);
        planRelationBatchDto.setBusinessSystemIdList(Arrays.asList(200L, 201L));

        // 初始化方案系统查询条件
        planSystemQueryDto = new PlanSystemQueryDto();
        planSystemQueryDto.setPlanId(100L);
    }

    @ParameterizedTest
    @MethodSource("provideSelectPlanRelationByIdScenarios")
    @DisplayName("根据ID查询方案关系")
    void testSelectPlanRelationById(Long id, PlanRelationEntity mockEntity, 
                                   Class<? extends Exception> expectedException, String expectedMessage) {
        // given
        if (mockEntity != null) {
            when(planRelationMapper.selectPlanRelationById(id)).thenReturn(mockEntity);
        } else if (id != null) {
            when(planRelationMapper.selectPlanRelationById(id)).thenReturn(null);
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class, 
                () -> planRelationService.selectPlanRelationById(id));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                PlanRelationDto result = planRelationService.selectPlanRelationById(id);
                assertNotNull(result);
                assertEquals(mockEntity.getId(), result.getId());
            });
        }

        // verify
        if (id != null) {
            verify(planRelationMapper).selectPlanRelationById(id);
        } else {
            verify(planRelationMapper, never()).selectPlanRelationById(anyLong());
        }
    }

    static Stream<Arguments> provideSelectPlanRelationByIdScenarios() {
        PlanRelationEntity validEntity = new PlanRelationEntity();
        validEntity.setId(1L);
        validEntity.setEnvcPlanId(100L);
        validEntity.setBusinessSystemId(200L);

        return Stream.of(
                // ID为null的情况
                Arguments.of(null, null, ContrastBusinessException.class, "方案ID不能为空"),
                // 查询成功的情况
                Arguments.of(1L, validEntity, null, null),
                // 数据不存在的情况
                Arguments.of(999L, null, ContrastBusinessException.class, "未找到对应的方案信息")
        );
    }

    @Test
    @DisplayName("查询方案关系列表-成功")
    void testSelectPlanRelationList_Success() throws Exception {
        // given
        List<PlanRelationListBean> mockBeans = new ArrayList<>();
        PlanRelationListBean bean = new PlanRelationListBean();
        bean.setId(1L);
        bean.setEnvcPlanId(100L);
        bean.setBusinessSystemId(200L);
        mockBeans.add(bean);

        // 使用try-with-resources来mock静态方法PageMethod.startPage
        try (MockedStatic<com.github.pagehelper.page.PageMethod> mockedPageMethod = mockStatic(com.github.pagehelper.page.PageMethod.class);
             MockedStatic<com.ideal.common.util.PageDataUtil> mockedPageDataUtil = mockStatic(com.ideal.common.util.PageDataUtil.class)) {
            
            // 创建Page对象而不是ArrayList
            com.github.pagehelper.Page<PlanRelationListBean> page = new com.github.pagehelper.Page<>(1, 10);
            page.addAll(mockBeans);
            page.setTotal(1);
            
            when(planRelationMapper.selectPlanRelationList(planRelationQueryDto)).thenReturn(page);
            
            // Mock PageDataUtil.toDtoPage方法
            PageInfo<PlanRelationDto> expectedPageInfo = new PageInfo<>();
            List<PlanRelationDto> dtoList = new ArrayList<>();
            PlanRelationDto dto = new PlanRelationDto();
            dto.setId(1L);
            dto.setEnvcPlanId(100L);
            dto.setBusinessSystemId(200L);
            dtoList.add(dto);
            expectedPageInfo.setList(dtoList);
            expectedPageInfo.setTotal(1);
            
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(any(List.class), eq(PlanRelationDto.class)))
                    .thenReturn(expectedPageInfo);

            // when
            PageInfo<PlanRelationDto> result = planRelationService.selectPlanRelationList(planRelationQueryDto, 1, 10);

            // then
            assertNotNull(result);
            assertNotNull(result.getList());
            assertFalse(result.getList().isEmpty());

            verify(planRelationMapper).selectPlanRelationList(planRelationQueryDto);
        }
    }

    @Test
    @DisplayName("查询方案关系列表-异常")
    void testSelectPlanRelationList_Exception() {
        // given
        when(planRelationMapper.selectPlanRelationList(planRelationQueryDto))
                .thenThrow(new RuntimeException("数据库异常"));

        // when & then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> planRelationService.selectPlanRelationList(planRelationQueryDto, 1, 10));
        assertTrue(exception.getMessage().contains("查询方案信息列表失败"));

        verify(planRelationMapper).selectPlanRelationList(planRelationQueryDto);
    }

    @ParameterizedTest
    @MethodSource("provideInsertPlanRelationScenarios")
    @DisplayName("新增方案关系")
    void testInsertPlanRelation(PlanRelationDto dto, UserDto user, List<PlanRelationEntity> existingRelations,
                               Integer mockResult, Class<? extends Exception> expectedException, String expectedMessage) {
        // given - 只在需要时设置mock
        if (dto != null && dto.getEnvcPlanId() != null && dto.getBusinessSystemId() != null && user != null) {
            when(planRelationMapper.selectPlanRelationByPlanAndSystem(dto.getEnvcPlanId(), dto.getBusinessSystemId()))
                    .thenReturn(existingRelations != null ? existingRelations : Collections.emptyList());
            
            // 只在没有异常且关系不存在时mock插入方法
            if (expectedException == null && (existingRelations == null || existingRelations.isEmpty()) && mockResult != null) {
                when(planRelationMapper.insertPlanRelation(any(PlanRelationEntity.class))).thenReturn(mockResult);
            }
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planRelationService.insertPlanRelation(dto, user));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                int result = planRelationService.insertPlanRelation(dto, user);
                assertEquals(mockResult.intValue(), result);
            });
        }

        // verify
        if (dto != null && dto.getEnvcPlanId() != null && dto.getBusinessSystemId() != null && user != null &&
            expectedException == null && (existingRelations == null || existingRelations.isEmpty()) && mockResult != null) {
            verify(planRelationMapper).insertPlanRelation(any(PlanRelationEntity.class));
        }
    }

    static Stream<Arguments> provideInsertPlanRelationScenarios() {
        PlanRelationDto validDto = new PlanRelationDto();
        validDto.setEnvcPlanId(100L);
        validDto.setBusinessSystemId(200L);

        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        List<PlanRelationEntity> existingRelations = Arrays.asList(new PlanRelationEntity());

        return Stream.of(
                // DTO为null的情况
                Arguments.of(null, validUser, null, null, ContrastBusinessException.class, "方案信息不能为空"),
                // 用户为null的情况
                Arguments.of(validDto, null, null, null, ContrastBusinessException.class, "用户信息不能为空"),
                // 环境方案ID为null的情况
                Arguments.of(new PlanRelationDto(), validUser, null, null, ContrastBusinessException.class, "环境方案ID不能为空"),
                // 业务系统ID为null的情况
                Arguments.of(createDtoWithPlanId(100L), validUser, null, null, ContrastBusinessException.class, "业务系统ID不能为空"),
                // 关联关系已存在的情况
                Arguments.of(validDto, validUser, existingRelations, null, ContrastBusinessException.class, "该方案与系统的关联关系已存在"),
                // 新增成功的情况
                Arguments.of(validDto, validUser, null, 1, null, null),
                // 新增失败的情况
                Arguments.of(validDto, validUser, null, 0, ContrastBusinessException.class, "新增方案信息失败")
        );
    }

    private static PlanRelationDto createDtoWithPlanId(Long planId) {
        PlanRelationDto dto = new PlanRelationDto();
        dto.setEnvcPlanId(planId);
        return dto;
    }

    @ParameterizedTest
    @MethodSource("provideUpdatePlanRelationScenarios")
    @DisplayName("修改方案关系")
    void testUpdatePlanRelation(PlanRelationDto dto, UserDto user, PlanRelationEntity existingEntity,
                               Integer mockResult, Class<? extends Exception> expectedException, String expectedMessage) {
        // given - 只在需要时设置mock
        if (dto != null && dto.getId() != null && user != null) {
            when(planRelationMapper.selectPlanRelationById(dto.getId())).thenReturn(existingEntity);
            
            // 只在没有异常且实体存在时mock更新方法
            if (expectedException == null && existingEntity != null && mockResult != null) {
                when(planRelationMapper.updatePlanRelation(any(PlanRelationEntity.class))).thenReturn(mockResult);
            }
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planRelationService.updatePlanRelation(dto, user));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                int result = planRelationService.updatePlanRelation(dto, user);
                assertEquals(mockResult.intValue(), result);
            });
        }

        // verify
        if (dto != null && dto.getId() != null && user != null && expectedException == null && existingEntity != null && mockResult != null) {
            verify(planRelationMapper).updatePlanRelation(any(PlanRelationEntity.class));
        }
    }

    static Stream<Arguments> provideUpdatePlanRelationScenarios() {
        PlanRelationDto validDto = new PlanRelationDto();
        validDto.setId(1L);
        validDto.setEnvcPlanId(100L);
        validDto.setBusinessSystemId(200L);

        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        PlanRelationEntity existingEntity = new PlanRelationEntity();
        existingEntity.setId(1L);

        return Stream.of(
                // DTO为null的情况
                Arguments.of(null, validUser, null, null, ContrastBusinessException.class, "方案信息不能为空"),
                // ID为null的情况
                Arguments.of(new PlanRelationDto(), validUser, null, null, ContrastBusinessException.class, "方案ID不能为空"),
                // 用户为null的情况
                Arguments.of(validDto, null, null, null, ContrastBusinessException.class, "用户信息不能为空"),
                // 记录不存在的情况
                Arguments.of(validDto, validUser, null, null, ContrastBusinessException.class, "未找到要修改的记录"),
                // 修改成功的情况
                Arguments.of(validDto, validUser, existingEntity, 1, null, null),
                // 修改失败的情况
                Arguments.of(validDto, validUser, existingEntity, 0, ContrastBusinessException.class, "修改方案信息失败")
        );
    }

    @ParameterizedTest
    @MethodSource("provideDeletePlanRelationByIdsScenarios")
    @DisplayName("批量删除方案关系")
    void testDeletePlanRelationByIds(Long[] ids, UserDto user, List<PlanRelationEntity> existingEntities,
                                    Integer mockResult, Class<? extends Exception> expectedException, String expectedMessage) {
        // given - 只在需要时设置mock
        if (ids != null && ids.length > 0 && user != null) {
            // 对于记录不存在的情况，只mock第一个ID的查询，因为方法会在第一个不存在的记录处抛出异常
            if (expectedException != null && expectedMessage.contains("的方案不存在")) {
                // 只mock第一个ID，因为会在这里抛出异常
                when(planRelationMapper.selectPlanRelationById(ids[0])).thenReturn(null);
            } else {
                // 正常情况下mock所有ID的查询
                for (int i = 0; i < ids.length; i++) {
                    PlanRelationEntity entity = (existingEntities != null && i < existingEntities.size()) 
                        ? existingEntities.get(i) : null;
                    when(planRelationMapper.selectPlanRelationById(ids[i])).thenReturn(entity);
                }
                
                // 只在没有异常且实体存在时mock删除方法
                if (expectedException == null && existingEntities != null && 
                    existingEntities.size() >= ids.length && mockResult != null) {
                    when(planRelationMapper.deletePlanRelationByIds(ids)).thenReturn(mockResult);
                }
            }
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planRelationService.deletePlanRelationByIds(ids, user));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                int result = planRelationService.deletePlanRelationByIds(ids, user);
                assertEquals(mockResult.intValue(), result);
            });
        }

        // verify
        if (ids != null && ids.length > 0 && user != null && expectedException == null && existingEntities != null && 
            existingEntities.size() >= ids.length && mockResult != null) {
            verify(planRelationMapper).deletePlanRelationByIds(ids);
        }
    }

    static Stream<Arguments> provideDeletePlanRelationByIdsScenarios() {
        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        Long[] validIds = {1L, 2L};
        List<PlanRelationEntity> existingEntities = Arrays.asList(
            new PlanRelationEntity(), new PlanRelationEntity()
        );

        return Stream.of(
                // IDs为null的情况
                Arguments.of(null, validUser, null, null, ContrastBusinessException.class, "删除ID不能为空"),
                // IDs为空数组的情况
                Arguments.of(new Long[]{}, validUser, null, null, ContrastBusinessException.class, "删除ID不能为空"),
                // 用户为null的情况
                Arguments.of(validIds, null, null, null, ContrastBusinessException.class, "用户信息不能为空"),
                // 记录不存在的情况
                Arguments.of(new Long[]{999L}, validUser, Arrays.asList((PlanRelationEntity) null), null, 
                    ContrastBusinessException.class, "的方案不存在"),
                // 删除成功的情况
                Arguments.of(validIds, validUser, existingEntities, 2, null, null),
                // 删除失败的情况
                Arguments.of(validIds, validUser, existingEntities, 0, ContrastBusinessException.class, "删除方案信息失败")
        );
    }

    @ParameterizedTest
    @MethodSource("provideSelectPlanSystemListScenarios")
    @DisplayName("查询方案绑定系统列表")
    void testSelectPlanSystemList(PlanSystemQueryDto queryDto, List<PlanSystemListBean> mockBeans,
                                 Class<? extends Exception> expectedException, String expectedMessage) {
        // given
        if (mockBeans != null && queryDto != null && queryDto.getPlanId() != null) {
            when(planRelationMapper.selectPlanSystemList(eq(queryDto.getPlanId()), 
                    eq(queryDto.getBusinessSystemName()))).thenReturn(mockBeans);
        }

        // when & then
        if (expectedException != null) {
            ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                    () -> planRelationService.selectPlanSystemList(queryDto, 1, 10));
            assertTrue(exception.getMessage().contains(expectedMessage));
        } else {
            assertDoesNotThrow(() -> {
                PageInfo<PlanSystemListDto> result = planRelationService.selectPlanSystemList(queryDto, 1, 10);
                assertNotNull(result);
            });
        }

        // verify
        if (queryDto != null && queryDto.getPlanId() != null) {
            verify(planRelationMapper).selectPlanSystemList(eq(queryDto.getPlanId()), 
                    eq(queryDto.getBusinessSystemName()));
        }
    }

    static Stream<Arguments> provideSelectPlanSystemListScenarios() {
        PlanSystemQueryDto validQuery = new PlanSystemQueryDto();
        validQuery.setPlanId(100L);

        List<PlanSystemListBean> mockBeans = Arrays.asList(new PlanSystemListBean());

        return Stream.of(
                // 查询条件为null的情况
                Arguments.of(null, null, ContrastBusinessException.class, "方案ID不能为空"),
                // 方案ID为null的情况
                Arguments.of(new PlanSystemQueryDto(), null, ContrastBusinessException.class, "方案ID不能为空"),
                // 查询成功的情况
                Arguments.of(validQuery, mockBeans, null, null)
        );
    }

    @Test
    @DisplayName("批量新增方案关系-成功")
    void testBatchInsertPlanRelation_Success() throws Exception {
        // given
        doNothing().when(batchHandler).batchData(anyList(), any(), eq(500));

        // when
        int result = planRelationService.batchInsertPlanRelation(planRelationBatchDto, mockUserDto);

        // then
        assertEquals(2, result);
        verify(batchHandler).batchData(anyList(), any(), eq(500));
    }

    @ParameterizedTest
    @MethodSource("provideBatchInsertScenarios")
    @DisplayName("批量新增方案关系-参数验证")
    void testBatchInsertPlanRelation_Validation(PlanRelationBatchDto batchDto, UserDto user,
                                               String expectedMessage) {
        // when & then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> planRelationService.batchInsertPlanRelation(batchDto, user));
        assertTrue(exception.getMessage().contains(expectedMessage));
    }

    static Stream<Arguments> provideBatchInsertScenarios() {
        UserDto validUser = new UserDto();
        validUser.setId(1L);
        validUser.setFullName("测试用户");

        PlanRelationBatchDto emptyListDto = new PlanRelationBatchDto();
        emptyListDto.setEnvcPlanId(100L);
        emptyListDto.setBusinessSystemIdList(Collections.emptyList());

        return Stream.of(
                // DTO为null的情况
                Arguments.of(null, validUser, "参数不能为空"),
                // 系统ID列表为null的情况
                Arguments.of(new PlanRelationBatchDto(), validUser, "参数不能为空"),
                // 系统ID列表为空的情况
                Arguments.of(emptyListDto, validUser, "参数不能为空")
        );
    }

    @Test
    @DisplayName("查询方案可绑定系统列表-成功")
    void testSelectAvailablePlanSystemList_Success() throws Exception {
        // given
        List<Long> boundSystemIds = Arrays.asList(200L);
        List<PlanSystemListBean> mockBeans = Arrays.asList(new PlanSystemListBean());
        
        when(planRelationMapper.selectBoundSystemIdsByPlanId(100L)).thenReturn(boundSystemIds);
        when(planRelationMapper.selectAvailablePlanSystemList(eq(100L), eq(boundSystemIds), 
                eq(planSystemQueryDto.getBusinessSystemName()))).thenReturn(mockBeans);

        // when
        PageInfo<PlanSystemListDto> result = planRelationService.selectAvailablePlanSystemList(planSystemQueryDto, 1, 10);

        // then
        assertNotNull(result);
        verify(planRelationMapper).selectBoundSystemIdsByPlanId(100L);
        verify(planRelationMapper).selectAvailablePlanSystemList(eq(100L), eq(boundSystemIds), 
                eq(planSystemQueryDto.getBusinessSystemName()));
    }

    @Test
    @DisplayName("查询方案可绑定系统列表-方案ID为空")
    void testSelectAvailablePlanSystemList_NullPlanId() {
        // given
        PlanSystemQueryDto invalidQuery = new PlanSystemQueryDto();

        // when & then
        ContrastBusinessException exception = assertThrows(ContrastBusinessException.class,
                () -> planRelationService.selectAvailablePlanSystemList(invalidQuery, 1, 10));
        assertTrue(exception.getMessage().contains("方案ID不能为空"));
    }
} 