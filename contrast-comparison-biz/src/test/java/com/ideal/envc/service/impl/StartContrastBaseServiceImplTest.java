package com.ideal.envc.service.impl;

import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.*;
import com.ideal.envc.model.bean.*;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.entity.*;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.dao.DeadlockLoserDataAccessException;

import java.util.*;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StartContrastBaseServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StartContrastBaseServiceImpl单元测试")
class StartContrastBaseServiceImplTest {

    @Mock
    private StartContrastBaseMapper startContrastBaseMapper;

    @Mock
    private StartContrastMapper startContrastMapper;

    @Mock
    private BatchHandler batchHandler;

    @Mock
    private RunInstanceMapper runInstanceMapper;

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @Mock
    private RunRuleMapper runRuleMapper;

    @Mock
    private RunFlowMapper runFlowMapper;

    @Mock
    private RunRuleContentMapper runRuleContentMapper;

    private StartContrastBaseServiceImpl startContrastBaseService;

    private HierarchicalRunInstanceBean hierarchicalRunInstanceBean;
    private List<HierarchicalRunInstanceBean> hierarchicalRunInstanceBeanList;
    private List<StartPlanBean> startPlanBeanList;
    private Map<Long, String> centerMap;
    private Map<Long, ComputerInfoDto> computerInfoDtoMap;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 初始化测试数据
        hierarchicalRunInstanceBean = createHierarchicalRunInstanceBean(1L);
        hierarchicalRunInstanceBeanList = Arrays.asList(
            createHierarchicalRunInstanceBean(1L),
            createHierarchicalRunInstanceBean(2L)
        );
        
        // 初始化中心映射
        centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        centerMap.put(2L, "中心2");
        
        // 初始化计算机信息映射
        computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));
        computerInfoDtoMap.put(102L, createComputerInfoDto(102L, "*************", "Windows"));
        
        // 初始化方案列表
        startPlanBeanList = Arrays.asList(
            createStartPlanBean(1L, "方案1"),
            createStartPlanBean(2L, "方案2")
        );
        
        // 手动创建服务实例
        startContrastBaseService = new StartContrastBaseServiceImpl(
            startContrastBaseMapper,
            startContrastMapper,
            batchHandler,
            runInstanceMapper,
            runInstanceInfoMapper,
            runRuleMapper,
            runFlowMapper,
            runRuleContentMapper
        );
    }

    @Test
    @DisplayName("根据实例ID查询层次化实例信息 - 成功场景")
    void getHierarchicalRunInstanceById_Success() {
        // 准备测试数据
        Long instanceId = 1L;
        
        // Mock依赖
        when(startContrastBaseMapper.selectHierarchicalRunInstanceById(instanceId)).thenReturn(hierarchicalRunInstanceBean);
        
        // 执行测试
        HierarchicalRunInstanceBean result = startContrastBaseService.getHierarchicalRunInstanceById(instanceId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(instanceId, result.getId());
        verify(startContrastBaseMapper).selectHierarchicalRunInstanceById(instanceId);
    }
    
    @Test
    @DisplayName("根据实例ID查询层次化实例信息 - ID为空")
    void getHierarchicalRunInstanceById_NullId() {
        // 执行测试
        HierarchicalRunInstanceBean result = startContrastBaseService.getHierarchicalRunInstanceById(null);
        
        // 验证结果
        assertNull(result);
        verify(startContrastBaseMapper, never()).selectHierarchicalRunInstanceById(any());
    }
    
    @Test
    @DisplayName("根据实例ID查询层次化实例信息 - 实例不存在")
    void getHierarchicalRunInstanceById_NotFound() {
        // 准备测试数据
        Long instanceId = 999L;
        
        // Mock依赖
        when(startContrastBaseMapper.selectHierarchicalRunInstanceById(instanceId)).thenReturn(null);
        
        // 执行测试
        HierarchicalRunInstanceBean result = startContrastBaseService.getHierarchicalRunInstanceById(instanceId);
        
        // 验证结果
        assertNull(result);
        verify(startContrastBaseMapper).selectHierarchicalRunInstanceById(instanceId);
    }
    
    @Test
    @DisplayName("根据方案ID查询层次化实例信息列表 - 成功场景")
    void getHierarchicalRunInstancesByPlanId_Success() {
        // 准备测试数据
        Long planId = 1L;
        
        // 确保 hierarchicalRunInstanceBeanList 包含两个元素
        hierarchicalRunInstanceBeanList = Arrays.asList(
            createHierarchicalRunInstanceBean(1L),
            createHierarchicalRunInstanceBean(2L)
        );
        
        // Mock依赖
        when(startContrastBaseMapper.selectHierarchicalRunInstancesByPlanId(planId)).thenReturn(hierarchicalRunInstanceBeanList);
        
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.getHierarchicalRunInstancesByPlanId(planId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(startContrastBaseMapper).selectHierarchicalRunInstancesByPlanId(planId);
    }
    
    @Test
    @DisplayName("根据方案ID查询层次化实例信息列表 - ID为空")
    void getHierarchicalRunInstancesByPlanId_NullId() {
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.getHierarchicalRunInstancesByPlanId(null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(startContrastBaseMapper, never()).selectHierarchicalRunInstancesByPlanId(any());
    }
    
    @Test
    @DisplayName("根据方案ID查询层次化实例信息列表 - 列表为空")
    void getHierarchicalRunInstancesByPlanId_EmptyList() {
        // 准备测试数据
        Long planId = 999L;
        
        // Mock依赖
        when(startContrastBaseMapper.selectHierarchicalRunInstancesByPlanId(planId)).thenReturn(Collections.emptyList());
        
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.getHierarchicalRunInstancesByPlanId(planId);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(startContrastBaseMapper).selectHierarchicalRunInstancesByPlanId(planId);
    }
    
    @Test
    @DisplayName("根据任务ID查询层次化实例信息列表 - 成功场景")
    void getHierarchicalRunInstancesByTaskId_Success() {
        // 准备测试数据
        Long taskId = 1L;
        
        // 确保 hierarchicalRunInstanceBeanList 包含两个元素
        hierarchicalRunInstanceBeanList = Arrays.asList(
            createHierarchicalRunInstanceBean(1L),
            createHierarchicalRunInstanceBean(2L)
        );
        
        // Mock依赖
        when(startContrastBaseMapper.selectHierarchicalRunInstancesByTaskId(taskId)).thenReturn(hierarchicalRunInstanceBeanList);
        
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.getHierarchicalRunInstancesByTaskId(taskId);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(startContrastBaseMapper).selectHierarchicalRunInstancesByTaskId(taskId);
    }
    
    @Test
    @DisplayName("根据任务ID查询层次化实例信息列表 - ID为空")
    void getHierarchicalRunInstancesByTaskId_NullId() {
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.getHierarchicalRunInstancesByTaskId(null);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(startContrastBaseMapper, never()).selectHierarchicalRunInstancesByTaskId(any());
    }
    
    @Test
    @DisplayName("根据任务ID查询层次化实例信息列表 - 列表为空")
    void getHierarchicalRunInstancesByTaskId_EmptyList() {
        // 准备测试数据
        Long taskId = 999L;
        
        // Mock依赖
        when(startContrastBaseMapper.selectHierarchicalRunInstancesByTaskId(taskId)).thenReturn(Collections.emptyList());
        
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.getHierarchicalRunInstancesByTaskId(taskId);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(startContrastBaseMapper).selectHierarchicalRunInstancesByTaskId(taskId);
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 成功场景")
    void saveRunInstanceData_Success() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = 100L;
        
        // Mock依赖 - 批量处理
        doNothing().when(batchHandler).batchData(anyList(), any(Consumer.class), anyInt());
        
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
            startPlanBeanList, userId, userName, from, taskId, centerMap, computerInfoDtoMap
        );
        
        // 验证结果
        assertNotNull(result);
        verify(batchHandler, atLeastOnce()).batchData(anyList(), any(Consumer.class), anyInt());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 方案列表为空")
    void saveRunInstanceData_EmptyPlanList() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = 100L;
        
        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
            Collections.emptyList(), userId, userName, from, taskId, centerMap, computerInfoDtoMap
        );
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(batchHandler, never()).batchData(anyList(), any(Consumer.class), anyInt());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 用户ID为空")
    void saveRunInstanceData_NullUserId() {
        // 准备测试数据
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = 100L;
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, null, userName, from, taskId, centerMap, computerInfoDtoMap
            )
        );
        
        // 验证异常信息
        assertEquals("用户ID不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 用户名称为空")
    void saveRunInstanceData_EmptyUserName() {
        // 准备测试数据
        Long userId = 1L;
        Integer from = 2; // 手动触发
        Long taskId = 100L;
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, "", from, taskId, centerMap, computerInfoDtoMap
            )
        );
        
        // 验证异常信息
        assertEquals("用户名称不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 触发来源为空")
    void saveRunInstanceData_NullFrom() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Long taskId = 100L;
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, null, taskId, centerMap, computerInfoDtoMap
            )
        );
        
        // 验证异常信息
        assertEquals("触发来源不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 中心映射为空")
    void saveRunInstanceData_NullCenterMap() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = 100L;
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, null, computerInfoDtoMap
            )
        );
        
        // 验证异常信息
        assertEquals("中心映射不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 计算机信息映射为空")
    void saveRunInstanceData_NullComputerInfoMap() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = 100L;
        
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, centerMap, null
            )
        );
        
        // 验证异常信息
        assertEquals("计算机信息映射不能为空", exception.getMessage());
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 空的系统列表场景")
    void saveRunInstanceData_EmptySystems() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = null;
        Map<Long, String> centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));

        // 创建一个方案，但系统列表为空
        StartPlanBean planWithEmptySystems = createStartPlanBean(1L, "方案1");
        planWithEmptySystems.setSystems(Collections.emptyList());
        List<StartPlanBean> startPlanBeanList = Collections.singletonList(planWithEmptySystems);

        // Mock依赖 - 使用doAnswer而不是when
        doAnswer(invocation -> {
            RunInstanceEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        // Mock批量处理方法
        doAnswer(invocation -> {
            List<RunInstanceEntity> entities = invocation.getArgument(0);
            for (RunInstanceEntity entity : entities) {
                runInstanceMapper.insertRunInstance(entity);
            }
            return null;
        }).when(batchHandler).batchData(anyList(), any(), anyInt());

        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, centerMap, computerInfoDtoMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size()); // 应该仍然创建了实例，只是没有系统数据
        
        // 验证runInstance被创建
        verify(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        // 验证没有创建系统相关数据
        verify(runInstanceInfoMapper, never()).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        verify(runRuleMapper, never()).insertRunRule(any(RunRuleEntity.class));
        verify(runFlowMapper, never()).insertRunFlow(any(RunFlowEntity.class));
        verify(runRuleContentMapper, never()).insertRunRuleContent(any(RunRuleContentEntity.class));
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 空的节点列表场景")
    void saveRunInstanceData_EmptyNodes() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = null;
        Map<Long, String> centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));

        // 创建一个方案，其中系统的节点列表为空
        StartPlanBean planWithEmptyNodes = createStartPlanBean(1L, "方案1");
        StartSystemBean systemWithEmptyNodes = new StartSystemBean();
        systemWithEmptyNodes.setId(101L);
        systemWithEmptyNodes.setBusinessSystemName("系统1");
        systemWithEmptyNodes.setBusinessSystemId(1001L);
        systemWithEmptyNodes.setComputerNodeBeans(Collections.emptyList());
        planWithEmptyNodes.setSystems(Collections.singletonList(systemWithEmptyNodes));
        List<StartPlanBean> startPlanBeanList = Collections.singletonList(planWithEmptyNodes);

        // Mock依赖 - 使用doAnswer而不是when
        doAnswer(invocation -> {
            RunInstanceEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        // Mock批量处理方法
        doAnswer(invocation -> {
            List<RunInstanceEntity> entities = invocation.getArgument(0);
            for (RunInstanceEntity entity : entities) {
                runInstanceMapper.insertRunInstance(entity);
            }
            return null;
        }).when(batchHandler).batchData(anyList(), any(), anyInt());

        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, centerMap, computerInfoDtoMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size()); // 应该创建了实例，但没有节点数据
        
        // 验证runInstance被创建
        verify(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        // 验证没有创建节点相关数据
        verify(runInstanceInfoMapper, never()).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        verify(runRuleMapper, never()).insertRunRule(any(RunRuleEntity.class));
        verify(runFlowMapper, never()).insertRunFlow(any(RunFlowEntity.class));
        verify(runRuleContentMapper, never()).insertRunRuleContent(any(RunRuleContentEntity.class));
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 空的规则列表场景")
    void saveRunInstanceData_EmptyRules() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = null;
        Map<Long, String> centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));
        computerInfoDtoMap.put(102L, createComputerInfoDto(102L, "*************", "Windows"));

        // 创建一个方案，其中节点的规则列表为空
        StartPlanBean planWithEmptyRules = createStartPlanBean(1L, "方案1");
        StartSystemBean system = new StartSystemBean();
        system.setId(101L);
        system.setBusinessSystemName("系统1");
        system.setBusinessSystemId(1001L);
        
        StartComputerNodeBean nodeWithEmptyRules = new StartComputerNodeBean();
        nodeWithEmptyRules.setId(201L);
        nodeWithEmptyRules.setSourceCenterId(1L);
        nodeWithEmptyRules.setTargetCenterId(1L);
        nodeWithEmptyRules.setSourceComputerId(101L);
        nodeWithEmptyRules.setTargetComputerId(102L);
        nodeWithEmptyRules.setRules(Collections.emptyList());
        
        system.setComputerNodeBeans(Collections.singletonList(nodeWithEmptyRules));
        planWithEmptyRules.setSystems(Collections.singletonList(system));
        List<StartPlanBean> startPlanBeanList = Collections.singletonList(planWithEmptyRules);

        // Mock依赖 - 使用doAnswer而不是when
        doAnswer(invocation -> {
            RunInstanceEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        doAnswer(invocation -> {
            RunInstanceInfoEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        
        // Mock批量处理方法
        doAnswer(invocation -> {
            List<?> entities = invocation.getArgument(0);
            Consumer<?> consumer = invocation.getArgument(1);
            
            if (!entities.isEmpty()) {
                Object firstEntity = entities.get(0);
                if (firstEntity instanceof RunInstanceEntity) {
                    for (Object entity : entities) {
                        runInstanceMapper.insertRunInstance((RunInstanceEntity) entity);
                    }
                } else if (firstEntity instanceof RunInstanceInfoEntity) {
                    for (Object entity : entities) {
                        runInstanceInfoMapper.insertRunInstanceInfo((RunInstanceInfoEntity) entity);
                    }
                }
            }
            return null;
        }).when(batchHandler).batchData(anyList(), any(), anyInt());

        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, centerMap, computerInfoDtoMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size()); // 应该创建了实例和节点，但没有规则数据
        
        // 验证runInstance和runInstanceInfo被创建
        verify(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        verify(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        
        // 验证没有创建规则相关数据
        verify(runRuleMapper, never()).insertRunRule(any(RunRuleEntity.class));
        verify(runFlowMapper, never()).insertRunFlow(any(RunFlowEntity.class));
        verify(runRuleContentMapper, never()).insertRunRuleContent(any(RunRuleContentEntity.class));
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 节点必要字段为空")
    void saveRunInstanceData_NodeRequiredFieldsEmpty() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = null;
        Map<Long, String> centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));
        computerInfoDtoMap.put(102L, createComputerInfoDto(102L, "*************", "Windows"));

        // 创建一个方案，其中节点的必要字段为空
        StartPlanBean plan = createStartPlanBean(1L, "方案1");
        StartSystemBean system = new StartSystemBean();
        system.setId(101L);
        system.setBusinessSystemName("系统1");
        system.setBusinessSystemId(1001L);
        
        // 创建一个缺少必要字段的节点
        StartComputerNodeBean nodeWithMissingFields = new StartComputerNodeBean();
        nodeWithMissingFields.setId(201L);
        // 不设置sourceCenterId
        nodeWithMissingFields.setTargetCenterId(1L);
        nodeWithMissingFields.setSourceComputerId(101L);
        nodeWithMissingFields.setTargetComputerId(102L);
        
        StartRuleBean rule = new StartRuleBean();
        rule.setId(301L);
        rule.setRuleType(1);
        StartRuleContentBean contentBean = new StartRuleContentBean();
        contentBean.setContent("测试内容");
        rule.setContentBean(contentBean);
        
        nodeWithMissingFields.setRules(Collections.singletonList(rule));
        system.setComputerNodeBeans(Collections.singletonList(nodeWithMissingFields));
        plan.setSystems(Collections.singletonList(system));
        List<StartPlanBean> startPlanBeanList = Collections.singletonList(plan);

        // Mock依赖 - 使用doAnswer而不是when
        doAnswer(invocation -> {
            RunInstanceEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        // Mock批量处理方法
        doAnswer(invocation -> {
            List<RunInstanceEntity> entities = invocation.getArgument(0);
            for (RunInstanceEntity entity : entities) {
                runInstanceMapper.insertRunInstance(entity);
            }
            return null;
        }).when(batchHandler).batchData(anyList(), any(), anyInt());

        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, centerMap, computerInfoDtoMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size()); // 应该创建了实例，但没有节点数据（因为节点必要字段为空）
        
        // 验证runInstance被创建
        verify(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        // 验证没有创建节点相关数据（因为节点验证失败）
        verify(runInstanceInfoMapper, never()).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        verify(runRuleMapper, never()).insertRunRule(any(RunRuleEntity.class));
        verify(runFlowMapper, never()).insertRunFlow(any(RunFlowEntity.class));
        verify(runRuleContentMapper, never()).insertRunRuleContent(any(RunRuleContentEntity.class));
    }
    
    @Test
    @DisplayName("存储运行实例相关表数据 - 规则必要字段为空")
    void saveRunInstanceData_RuleRequiredFieldsEmpty() {
        // 准备测试数据
        Long userId = 1L;
        String userName = "测试用户";
        Integer from = 2; // 手动触发
        Long taskId = null;
        Map<Long, String> centerMap = new HashMap<>();
        centerMap.put(1L, "中心1");
        Map<Long, ComputerInfoDto> computerInfoDtoMap = new HashMap<>();
        computerInfoDtoMap.put(101L, createComputerInfoDto(101L, "*************", "Linux"));
        computerInfoDtoMap.put(102L, createComputerInfoDto(102L, "*************", "Windows"));

        // 创建一个方案，其中规则的必要字段为空
        StartPlanBean plan = createStartPlanBean(1L, "方案1");
        StartSystemBean system = new StartSystemBean();
        system.setId(101L);
        system.setBusinessSystemName("系统1");
        system.setBusinessSystemId(1001L);
        
        StartComputerNodeBean node = new StartComputerNodeBean();
        node.setId(201L);
        node.setSourceCenterId(1L);
        node.setTargetCenterId(1L);
        node.setSourceComputerId(101L);
        node.setTargetComputerId(102L);
        
        // 创建一个缺少必要字段的规则
        StartRuleBean ruleWithMissingFields = new StartRuleBean();
        ruleWithMissingFields.setId(301L);
        ruleWithMissingFields.setRuleType(1);
        // 不设置model、type、path和sourcePath
        StartRuleContentBean contentBean = new StartRuleContentBean();
        contentBean.setContent("测试内容");
        ruleWithMissingFields.setContentBean(contentBean);
        
        node.setRules(Collections.singletonList(ruleWithMissingFields));
        system.setComputerNodeBeans(Collections.singletonList(node));
        plan.setSystems(Collections.singletonList(system));
        List<StartPlanBean> startPlanBeanList = Collections.singletonList(plan);

        // Mock依赖 - 使用doAnswer而不是when
        doAnswer(invocation -> {
            RunInstanceEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        
        doAnswer(invocation -> {
            RunInstanceInfoEntity entity = invocation.getArgument(0);
            entity.setId(1L); // 设置ID，模拟数据库自增ID
            return 1;
        }).when(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        
        // Mock批量处理方法
        doAnswer(invocation -> {
            List<?> entities = invocation.getArgument(0);
            Consumer<?> consumer = invocation.getArgument(1);
            
            if (!entities.isEmpty()) {
                Object firstEntity = entities.get(0);
                if (firstEntity instanceof RunInstanceEntity) {
                    for (Object entity : entities) {
                        runInstanceMapper.insertRunInstance((RunInstanceEntity) entity);
                    }
                } else if (firstEntity instanceof RunInstanceInfoEntity) {
                    for (Object entity : entities) {
                        runInstanceInfoMapper.insertRunInstanceInfo((RunInstanceInfoEntity) entity);
                    }
                }
            }
            return null;
        }).when(batchHandler).batchData(anyList(), any(), anyInt());

        // 执行测试
        List<HierarchicalRunInstanceBean> result = startContrastBaseService.saveRunInstanceData(
                startPlanBeanList, userId, userName, from, taskId, centerMap, computerInfoDtoMap);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size()); // 应该创建了实例和节点，但没有规则数据（因为规则必要字段为空）
        
        // 验证runInstance和runInstanceInfo被创建
        verify(runInstanceMapper).insertRunInstance(any(RunInstanceEntity.class));
        verify(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
        
        // 验证没有创建规则相关数据（因为规则验证失败）
        verify(runRuleMapper, never()).insertRunRule(any(RunRuleEntity.class));
        verify(runFlowMapper, never()).insertRunFlow(any(RunFlowEntity.class));
        verify(runRuleContentMapper, never()).insertRunRuleContent(any(RunRuleContentEntity.class));
    }
    
    @Test
    @DisplayName("更新实例状态为初始状态 - 成功场景")
    void updateInstanceStatusToInitial_Success() {
        // 准备测试数据
        Long instanceId = 1L;
        
        // Mock依赖 - 确保所有更新方法返回正整数表示更新成功
        when(startContrastBaseMapper.updateRunInstanceStatusToInitial(instanceId)).thenReturn(1);
        when(startContrastBaseMapper.updateRunInstanceInfoStatusToInitial(instanceId)).thenReturn(2);
        when(startContrastBaseMapper.updateRunRuleStatusToInitial(instanceId)).thenReturn(3);
        when(startContrastBaseMapper.updateRunFlowStatusToInitial(instanceId)).thenReturn(3);
        
        // 执行测试
        boolean result = startContrastBaseService.updateInstanceStatusToInitial(instanceId);
        
        // 验证结果
        assertTrue(result);
        verify(startContrastBaseMapper).updateRunInstanceStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunInstanceInfoStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunRuleStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunFlowStatusToInitial(instanceId);
    }
    
    @Test
    @DisplayName("更新实例状态为初始状态 - 实例ID为空")
    void updateInstanceStatusToInitial_NullId() {
        // 执行测试
        boolean result = startContrastBaseService.updateInstanceStatusToInitial(null);
        
        // 验证结果
        assertFalse(result);
        verify(startContrastBaseMapper, never()).updateRunInstanceStatusToInitial(any());
    }
    
    @Test
    @DisplayName("更新实例状态为初始状态 - 实例不存在")
    void updateInstanceStatusToInitial_NotFound() {
        // 准备测试数据
        Long instanceId = 999L;
        
        // Mock依赖 - 返回0表示没有记录被更新
        when(startContrastBaseMapper.updateRunInstanceStatusToInitial(instanceId)).thenReturn(0);
        
        // 执行测试
        boolean result = startContrastBaseService.updateInstanceStatusToInitial(instanceId);
        
        // 验证结果
        assertFalse(result);
        verify(startContrastBaseMapper).updateRunInstanceStatusToInitial(instanceId);
        verify(startContrastBaseMapper, never()).updateRunInstanceInfoStatusToInitial(any());
    }
    
    @Test
    @DisplayName("更新实例状态为初始状态 - 死锁异常重试成功")
    void updateInstanceStatusToInitial_DeadlockRetrySuccess() {
        // 准备测试数据
        Long instanceId = 1L;
        
        // Mock依赖 - 第一次抛出死锁异常，第二次成功
        when(startContrastBaseMapper.updateRunInstanceStatusToInitial(instanceId))
            .thenThrow(new DeadlockLoserDataAccessException("测试死锁", null))
            .thenReturn(1);
        when(startContrastBaseMapper.updateRunInstanceInfoStatusToInitial(instanceId)).thenReturn(2);
        when(startContrastBaseMapper.updateRunRuleStatusToInitial(instanceId)).thenReturn(3);
        when(startContrastBaseMapper.updateRunFlowStatusToInitial(instanceId)).thenReturn(3);
        
        // 执行测试
        boolean result = startContrastBaseService.updateInstanceStatusToInitial(instanceId);
        
        // 验证结果
        assertTrue(result);
        verify(startContrastBaseMapper, times(2)).updateRunInstanceStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunInstanceInfoStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunRuleStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunFlowStatusToInitial(instanceId);
        
        // 注意：我们不需要验证指数退避策略(50ms, 100ms, 200ms)的具体实现
        // 只需确保测试能够成功通过，表明重试机制正常工作即可
    }
    
    @Test
    @DisplayName("更新实例状态为初始状态 - 达到最大重试次数仍然失败")
    void updateInstanceStatusToInitial_MaxRetriesExceeded() {
        // 准备测试数据
        Long instanceId = 1L;
        
        // Mock依赖 - 始终抛出死锁异常
        when(startContrastBaseMapper.updateRunInstanceStatusToInitial(instanceId))
            .thenThrow(new DeadlockLoserDataAccessException("测试死锁", null));
        
        // 执行测试并验证异常
        DeadlockLoserDataAccessException exception = assertThrows(
            DeadlockLoserDataAccessException.class,
            () -> startContrastBaseService.updateInstanceStatusToInitial(instanceId)
        );
        
        // 验证结果
        verify(startContrastBaseMapper, times(3)).updateRunInstanceStatusToInitial(instanceId);
        verify(startContrastBaseMapper, never()).updateRunInstanceInfoStatusToInitial(any());
    }
    
    @Test
    @DisplayName("更新实例状态为初始状态 - 更新过程中发生异常")
    void updateInstanceStatusToInitial_ExceptionDuringUpdate() {
        // 准备测试数据
        Long instanceId = 1L;
        
        // Mock依赖 - 第一个更新成功，后续更新抛出异常
        when(startContrastBaseMapper.updateRunInstanceStatusToInitial(instanceId)).thenReturn(1);
        when(startContrastBaseMapper.updateRunInstanceInfoStatusToInitial(instanceId))
            .thenThrow(new RuntimeException("测试异常"));
        
        // 执行测试并验证异常
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> startContrastBaseService.updateInstanceStatusToInitial(instanceId)
        );
        
        // 验证结果
        verify(startContrastBaseMapper).updateRunInstanceStatusToInitial(instanceId);
        verify(startContrastBaseMapper).updateRunInstanceInfoStatusToInitial(instanceId);
        verify(startContrastBaseMapper, never()).updateRunRuleStatusToInitial(any());
    }
    
    // 辅助方法 - 创建层次化实例对象
    private HierarchicalRunInstanceBean createHierarchicalRunInstanceBean(Long id) {
        HierarchicalRunInstanceBean bean = new HierarchicalRunInstanceBean();
        bean.setId(id);
        bean.setEnvcPlanId(100L);
        bean.setEnvcTaskId(200L);
        bean.setResult(-1);
        bean.setState(0);
        bean.setFrom(2);
        bean.setStarterName("测试用户");
        bean.setStarterId(1L);
        bean.setStartTime(new Date());
        bean.setInstanceInfoList(new ArrayList<>());
        return bean;
    }
    
    // 辅助方法 - 创建计算机信息DTO
    private ComputerInfoDto createComputerInfoDto(Long id, String ip, String osName) {
        ComputerInfoDto dto = new ComputerInfoDto();
        // ComputerInfoDto类中没有setComputerId和setComputerIp方法，这些属性可能需要通过其他方式设置
        // 或者在测试中可以忽略这些属性，只设置存在的属性
        dto.setAgentPort(22);
        dto.setOsName(osName);
        return dto;
    }
    
    // 辅助方法 - 创建方案对象
    private StartPlanBean createStartPlanBean(Long id, String name) {
        StartPlanBean bean = new StartPlanBean();
        bean.setId(id);
        bean.setName(name);
        
        // 创建系统列表
        List<StartSystemBean> systems = new ArrayList<>();
        StartSystemBean system = new StartSystemBean();
        system.setId(101L);
        system.setBusinessSystemId(10L);
        system.setBusinessSystemName("测试系统");
        
        // 创建节点列表
        List<StartComputerNodeBean> nodes = new ArrayList<>();
        StartComputerNodeBean node = new StartComputerNodeBean();
        node.setId(1000L);
        node.setSourceCenterId(1L);
        node.setTargetCenterId(2L);
        node.setSourceComputerId(101L);
        node.setTargetComputerId(102L);
        node.setSourceComputerIp("*************");
        node.setTargetComputerIp("*************");
        node.setSourceCenterName("中心1");
        node.setTargetCenterName("中心2");
        
        // 创建规则列表
        List<StartRuleBean> rules = new ArrayList<>();
        StartRuleBean rule = new StartRuleBean();
        rule.setId(10000L);
        rule.setModel(1);
        rule.setType(1L);
        rule.setPath("/test/path");
        rule.setSourcePath("/test/source/path");
        rule.setEncode("UTF-8");
        rule.setWay(1);
        rule.setRuleType(1);
        rule.setEnabled(1);
        rule.setChildLevel(0);
        
        // 创建规则内容
        StartRuleContentBean content = new StartRuleContentBean();
        content.setContent("测试规则内容");
        rule.setContentBean(content);
        
        rules.add(rule);
        node.setRules(rules);
        nodes.add(node);
        system.setComputerNodeBeans(nodes); // 使用正确的属性名
        systems.add(system);
        bean.setSystems(systems);
        
        return bean;
    }
} 