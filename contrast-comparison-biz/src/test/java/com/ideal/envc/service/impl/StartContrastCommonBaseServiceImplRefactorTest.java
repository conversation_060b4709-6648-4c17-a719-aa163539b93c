package com.ideal.envc.service.impl;

import com.ideal.envc.model.bean.*;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.enums.RuleITypeEnums;
import com.ideal.envc.model.enums.RuleWayEnums;
import com.ideal.envc.producer.SendDataToEngineMqProducer;
import com.ideal.envc.service.IStartContrastBaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * StartContrastCommonBaseServiceImpl重构验证测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StartContrastCommonBaseServiceImpl重构验证测试")
class StartContrastCommonBaseServiceImplRefactorTest {

    @Mock
    private SendDataToEngineMqProducer sendDataToEngineMqProducer;

    @Mock
    private IStartContrastBaseService startContrastBaseService;

    @InjectMocks
    private StartContrastCommonBaseServiceImpl startContrastCommonBaseService;

    private UserDto userDto;
    private List<HierarchicalRunInstanceBean> hierarchicalInstanceList;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        // 创建测试数据
        HierarchicalRunFlowBean ruleFlowBean = new HierarchicalRunFlowBean();
        ruleFlowBean.setId(100L);

        HierarchicalRunRuleContentBean ruleContentBean = new HierarchicalRunRuleContentBean();
        ruleContentBean.setContent("test content");

        HierarchicalRunRuleBean ruleBean = new HierarchicalRunRuleBean();
        ruleBean.setId(1L);
        ruleBean.setType(RuleITypeEnums.DIRECTORY.getCode());
        ruleBean.setWay(RuleWayEnums.ALL.getCode());
        ruleBean.setSourcePath("/source/path");
        ruleBean.setPath("/target/path");
        ruleBean.setEncode("UTF-8");
        ruleBean.setRuleContent(ruleContentBean);
        ruleBean.setRuleFlow(ruleFlowBean);
        // 添加必要的属性
        ruleBean.setModel(1);
        ruleBean.setRuleType(1);

        HierarchicalRunInstanceInfoBean infoBean = new HierarchicalRunInstanceInfoBean();
        infoBean.setId(1L);
        infoBean.setSourceComputerIp("***********");
        infoBean.setSourceComputerPort(22);
        infoBean.setSourceComputerOs("Linux");
        infoBean.setTargetComputerIp("***********");
        infoBean.setTargetComputerPort(22);
        // 添加必要的属性
        infoBean.setSourceComputerId(1L);
        infoBean.setTargetComputerId(2L);
        infoBean.setTargetComputerOs("Windows");
        infoBean.setRuleList(Arrays.asList(ruleBean));

        HierarchicalRunInstanceBean instanceBean = new HierarchicalRunInstanceBean();
        instanceBean.setId(1L);
        instanceBean.setInstanceInfoList(Arrays.asList(infoBean));

        hierarchicalInstanceList = Arrays.asList(instanceBean);
    }

    @Test
    @DisplayName("重构后的buildTaskFlowDtoList方法应正常工作")
    void should_work_correctly_after_refactoring() {
        // When
        List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getUniqueTaskId());
        assertNotNull(result.get(0).getTasks());
        assertEquals(1, result.get(0).getTasks().size());
    }

    @Test
    @DisplayName("空列表输入应返回空结果")
    void should_return_empty_for_empty_input() {
        // When
        List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(Collections.emptyList(), userDto);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("null输入应返回空结果")
    void should_return_empty_for_null_input() {
        // When
        List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(null, userDto);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
} 