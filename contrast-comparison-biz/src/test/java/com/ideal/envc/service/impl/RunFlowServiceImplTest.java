package com.ideal.envc.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.model.dto.RunFlowDto;
import com.ideal.envc.model.dto.RunFlowQueryDto;
import com.ideal.envc.model.entity.RunFlowEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RunFlowServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RunFlowServiceImpl单元测试")
class RunFlowServiceImplTest {

    @Mock
    private RunFlowMapper runFlowMapper;

    @InjectMocks
    private RunFlowServiceImpl runFlowService;

    private RunFlowEntity entity;
    private RunFlowDto dto;
    private RunFlowQueryDto queryDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        entity = new RunFlowEntity();
        entity.setId(1L);
        entity.setRunBizId(100L);
        entity.setFlowid(1L);
        entity.setState(1);

        dto = new RunFlowDto();
        dto.setId(1L);
        dto.setRunBizId(100L);
        dto.setFlowid(1L);
        dto.setState(1);

        queryDto = new RunFlowQueryDto();
        queryDto.setRunBizId(100L);
    }

    @Test
    @DisplayName("根据ID查询流程 - 正常情况")
    void selectRunFlowById_Success() throws ContrastBusinessException {
        // 准备测试数据
        Long id = 1L;
        RunFlowEntity entity = new RunFlowEntity();
        entity.setId(id);
        entity.setFlowid(1L);

        // Mock依赖
        when(runFlowMapper.selectRunFlowById(id)).thenReturn(entity);

        // 执行测试
        RunFlowDto result = runFlowService.selectRunFlowById(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(entity.getFlowid(), result.getFlowid());
        verify(runFlowMapper).selectRunFlowById(id);
    }

    @Test
    @DisplayName("根据ID查询流程 - ID为空")
    void selectRunFlowById_NullId() throws ContrastBusinessException {
        // 执行测试
        RunFlowDto result = runFlowService.selectRunFlowById(null);

        // 验证结果
        assertNull(result);
        verify(runFlowMapper, never()).selectRunFlowById(any());
    }

    @Test
    @DisplayName("根据ID查询流程 - 流程不存在")
    void selectRunFlowById_NotFound() throws ContrastBusinessException {
        // 准备测试数据
        Long id = 1L;

        // Mock依赖
        when(runFlowMapper.selectRunFlowById(id)).thenReturn(null);

        // 执行测试
        RunFlowDto result = runFlowService.selectRunFlowById(id);

        // 验证结果
        assertNull(result);
        verify(runFlowMapper).selectRunFlowById(id);
    }

    @Test
    @DisplayName("测试查询节点规则流程列表 - 正常场景")
    void testSelectRunFlowList_Normal() throws ContrastBusinessException {
        // 准备测试数据
        List<RunFlowEntity> entityList = Arrays.asList(entity);
        
        // 创建Page对象包装实体列表
        Page<RunFlowEntity> page = new Page<>(1, 10);
        page.addAll(entityList);
        page.setTotal(entityList.size());
        
        // 模拟查询结果
        doReturn(page).when(runFlowMapper).selectRunFlowList(any());
        
        // 执行测试
        PageInfo<RunFlowDto> result = runFlowService.selectRunFlowList(queryDto, 1, 10);
        
        // 验证结果
        assertNotNull(result);
        verify(runFlowMapper).selectRunFlowList(any());
    }

    @Test
    @DisplayName("测试新增节点规则流程 - 正常场景")
    void testInsertRunFlow_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(1).when(runFlowMapper).insertRunFlow(any());

        // 执行测试
        int result = runFlowService.insertRunFlow(dto);

        // 验证结果
        assertEquals(1, result);
        verify(runFlowMapper).insertRunFlow(any());
    }

    @Test
    @DisplayName("测试新增节点规则流程 - 异常场景")
    void testInsertRunFlow_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowMapper).insertRunFlow(any());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowService.insertRunFlow(dto));
        verify(runFlowMapper).insertRunFlow(any());
    }

    @Test
    @DisplayName("测试修改节点规则流程 - 正常场景")
    void testUpdateRunFlow_Normal() throws ContrastBusinessException {
        // 准备测试数据
        doReturn(1).when(runFlowMapper).updateRunFlow(any());

        // 执行测试
        int result = runFlowService.updateRunFlow(dto);

        // 验证结果
        assertEquals(1, result);
        verify(runFlowMapper).updateRunFlow(any());
    }

    @Test
    @DisplayName("测试修改节点规则流程 - 异常场景")
    void testUpdateRunFlow_Exception() {
        // 准备测试数据
        doThrow(new RuntimeException("测试异常")).when(runFlowMapper).updateRunFlow(any());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowService.updateRunFlow(dto));
        verify(runFlowMapper).updateRunFlow(any());
    }

    @Test
    @DisplayName("测试批量删除节点规则流程 - 正常场景")
    void testDeleteRunFlowByIds_Normal() throws ContrastBusinessException {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        doReturn(2).when(runFlowMapper).deleteRunFlowByIds(any());

        // 执行测试
        int result = runFlowService.deleteRunFlowByIds(ids);

        // 验证结果
        assertEquals(2, result);
        verify(runFlowMapper).deleteRunFlowByIds(ids);
    }

    @Test
    @DisplayName("测试批量删除节点规则流程 - 异常场景")
    void testDeleteRunFlowByIds_Exception() {
        // 准备测试数据
        Long[] ids = {1L, 2L};
        doThrow(new RuntimeException("测试异常")).when(runFlowMapper).deleteRunFlowByIds(any());

        // 执行测试并验证异常
        assertThrows(ContrastBusinessException.class, () -> 
            runFlowService.deleteRunFlowByIds(ids));
        verify(runFlowMapper).deleteRunFlowByIds(ids);
    }
} 