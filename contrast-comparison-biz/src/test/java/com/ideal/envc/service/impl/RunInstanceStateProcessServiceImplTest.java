package com.ideal.envc.service.impl;

import com.ideal.envc.component.TaskCounterComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;

import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RunInstanceStateProcessServiceImpl的单元测试类
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RunInstanceStateProcessServiceImpl单元测试")
class RunInstanceStateProcessServiceImplTest {

    @Mock
    private RunInstanceMapper runInstanceMapper;

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @Mock
    private TaskCounterComponent taskCounterComponent;

    @Spy
    @InjectMocks
    private RunInstanceStateProcessServiceImpl runInstanceStateProcessService;

    private RunInstanceEntity instance;
    private Long instanceId;
    private Long instanceInfoId;
    private Long messageTimestamp;
    private Long currentTimestamp;

    @BeforeEach
    void setUp() {
        instanceId = 1L;
        instanceInfoId = 100L;
        currentTimestamp = System.currentTimeMillis();
        messageTimestamp = currentTimestamp - 1000; // 1秒前的消息

        // 初始化实例
        instance = new RunInstanceEntity();
        instance.setId(instanceId);
        instance.setState(1);
        instance.setResult(1);
        instance.setUpdateTime(new Timestamp(currentTimestamp));
    }

    @Test
    @DisplayName("处理实例状态更新 - 参数为空")
    void processInstanceStateUpdate_NullParams() {
        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(null, null, null);

        // 验证结果
        assertFalse(result);
        verify(runInstanceMapper, never()).selectRunInstanceById(any());
    }

    @Test
    @DisplayName("处理实例状态更新 - 实例不存在")
    void processInstanceStateUpdate_InstanceNotFound() throws ContrastBusinessException {
        // 准备测试数据
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(null);

        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(instanceId, instanceInfoId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        verify(runInstanceMapper).selectRunInstanceById(instanceId);
        verify(taskCounterComponent, never()).decrementInstanceCounter(any());
    }

    @Test
    @DisplayName("处理实例状态更新 - 消息过期")
    void processInstanceStateUpdate_MessageExpired() throws ContrastBusinessException {
        // 准备测试数据
        Long expiredMessageTimestamp = currentTimestamp - 40000; // 40秒前的消息
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        
        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(instanceId, instanceInfoId, expiredMessageTimestamp);

        // 验证结果
        assertTrue(result);
        verify(runInstanceMapper).selectRunInstanceById(instanceId);
        verify(taskCounterComponent, never()).decrementInstanceCounter(any());
        verify(runInstanceStateProcessService).isMessageExpired(eq(expiredMessageTimestamp), anyLong(), eq(instanceId));
    }

    @Test
    @DisplayName("处理实例状态更新 - 计数器未归零")
    void processInstanceStateUpdate_CounterNotZero() throws ContrastBusinessException {
        // 准备测试数据
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        when(taskCounterComponent.decrementInstanceCounter(anyLong())).thenReturn(1L);

        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(instanceId, instanceInfoId, messageTimestamp);

        // 验证结果
        assertTrue(result);
        verify(runInstanceMapper).selectRunInstanceById(instanceId);
        verify(taskCounterComponent).decrementInstanceCounter(instanceId);
        verify(taskCounterComponent, never()).getBusinessLock(any(), anyLong());
        verify(runInstanceStateProcessService).handleCounterValue(eq(instanceId), eq(instanceInfoId), anyLong(), eq(1));
    }

    @Test
    @DisplayName("处理实例状态更新 - 计数器归零且状态需要更新")
    void processInstanceStateUpdate_CounterZeroAndStateChanged() throws ContrastBusinessException, InterruptedException {
        // 准备测试数据
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        when(taskCounterComponent.decrementInstanceCounter(anyLong())).thenReturn(0L);
        // 修改为使用getBusinessLock而不是tryBusinessLock
        RLock mockLock = mock(RLock.class);
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(taskCounterComponent.getInstanceCounterValue(anyLong())).thenReturn(0L);
        when(runInstanceInfoMapper.selectInstanceInfosByInstanceId(anyLong()))
            .thenReturn(Collections.singletonList(createInstanceInfoEntity(0, 0)));
        when(runInstanceMapper.updateStateAndResultByIdAndTimestamp(eq(instanceId), anyInt(), anyInt()))
            .thenReturn(1);
        
        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(instanceId, instanceInfoId, messageTimestamp);

        // 验证结果 - 根据源码，更新状态成功应返回true
        assertTrue(result);
        verify(runInstanceMapper, times(2)).selectRunInstanceById(instanceId);
        verify(taskCounterComponent).decrementInstanceCounter(instanceId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(mockLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        verify(taskCounterComponent).getInstanceCounterValue(instanceId);
        verify(runInstanceInfoMapper).selectInstanceInfosByInstanceId(instanceId);
        verify(runInstanceMapper).updateStateAndResultByIdAndTimestamp(eq(instanceId), anyInt(), anyInt());
        verify(taskCounterComponent).clearInstanceCounter(instanceId);
        verify(mockLock).unlock();
        verify(runInstanceStateProcessService).processZeroCounter(eq(instanceId), anyLong());
        verify(runInstanceStateProcessService).processWithLock(eq(instanceId), anyLong());
    }

    @Test
    @DisplayName("处理实例状态更新 - 计数器归零但状态未变化")
    void processInstanceStateUpdate_CounterZeroButStateUnchanged() throws ContrastBusinessException, InterruptedException {
        // 准备测试数据
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        when(taskCounterComponent.decrementInstanceCounter(anyLong())).thenReturn(0L);
        // 修改为使用getBusinessLock而不是tryBusinessLock
        RLock mockLock = mock(RLock.class);
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(mockLock);
        when(mockLock.tryLock(anyLong(), anyLong(), any(TimeUnit.class))).thenReturn(true);
        when(taskCounterComponent.getInstanceCounterValue(anyLong())).thenReturn(0L);
        when(runInstanceInfoMapper.selectInstanceInfosByInstanceId(anyLong()))
            .thenReturn(Collections.singletonList(createInstanceInfoEntity(1, 1)));

        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(instanceId, instanceInfoId, messageTimestamp);

        // 验证结果 - 根据源码，状态未变化但处理完成应返回true
        assertTrue(result);
        verify(runInstanceMapper, times(2)).selectRunInstanceById(instanceId);
        verify(taskCounterComponent).decrementInstanceCounter(instanceId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(mockLock).tryLock(anyLong(), anyLong(), any(TimeUnit.class));
        verify(taskCounterComponent).getInstanceCounterValue(instanceId);
        verify(runInstanceInfoMapper).selectInstanceInfosByInstanceId(instanceId);
        verify(runInstanceMapper, never()).updateStateAndResultByIdAndTimestamp(any(), anyInt(), anyInt());
        verify(taskCounterComponent).clearInstanceCounter(instanceId);
        verify(mockLock).unlock();
        verify(runInstanceStateProcessService).isStateUnchanged(any(), anyInt(), anyInt());
        verify(runInstanceStateProcessService).handleUnchangedState(eq(instanceId));
    }

    @Test
    @DisplayName("处理实例状态更新 - 无法获取业务锁")
    void processInstanceStateUpdate_CannotGetLock() throws ContrastBusinessException {
        // 准备测试数据
        when(runInstanceMapper.selectRunInstanceById(anyLong())).thenReturn(instance);
        when(taskCounterComponent.decrementInstanceCounter(anyLong())).thenReturn(0L);
        // 修改为使用getBusinessLock而不是tryBusinessLock，并返回null表示无法获取锁
        when(taskCounterComponent.getBusinessLock(anyString(), anyLong())).thenReturn(null);

        // 执行测试
        boolean result = runInstanceStateProcessService.processInstanceStateUpdate(instanceId, instanceInfoId, messageTimestamp);

        // 验证结果 - 根据源码，无法获取锁应返回false
        assertFalse(result);
        verify(runInstanceMapper).selectRunInstanceById(instanceId);
        verify(taskCounterComponent).decrementInstanceCounter(instanceId);
        verify(taskCounterComponent).getBusinessLock(anyString(), anyLong());
        verify(taskCounterComponent, never()).getInstanceCounterValue(instanceId);
    }

    @Test
    @DisplayName("测试获取当前时间戳")
    void testGetCurrentTimestamp() {
        // 准备测试数据
        RunInstanceEntity instanceWithTime = new RunInstanceEntity();
        instanceWithTime.setUpdateTime(new Timestamp(currentTimestamp));
        
        RunInstanceEntity instanceWithoutTime = new RunInstanceEntity();
        
        // 执行测试 - 有更新时间
        Long result1 = runInstanceStateProcessService.getCurrentTimestamp(instanceWithTime);
        
        // 执行测试 - 无更新时间
        Long result2 = runInstanceStateProcessService.getCurrentTimestamp(instanceWithoutTime);
        
        // 验证结果
        assertEquals(currentTimestamp, result1);
        assertTrue(result2 > 0); // 应该返回当前系统时间
    }

    @Test
    @DisplayName("测试消息是否过期")
    void testIsMessageExpired() {
        // 准备测试数据
        Long validMessageTimestamp = currentTimestamp - 1000; // 1秒前
        Long expiredMessageTimestamp = currentTimestamp - 40000; // 40秒前
        
        // 执行测试
        boolean result1 = runInstanceStateProcessService.isMessageExpired(validMessageTimestamp, currentTimestamp, instanceId);
        boolean result2 = runInstanceStateProcessService.isMessageExpired(expiredMessageTimestamp, currentTimestamp, instanceId);
        boolean result3 = runInstanceStateProcessService.isMessageExpired(null, currentTimestamp, instanceId);
        
        // 验证结果
        assertFalse(result1); // 有效消息
        assertTrue(result2);  // 过期消息
        assertFalse(result3); // 空时间戳
    }

    @Test
    @DisplayName("测试处理计数器值")
    void testHandleCounterValue() {
        // 准备测试数据 - 使用doReturn...when...方式模拟Spy对象
        doReturn(true).when(runInstanceStateProcessService).processZeroCounter(anyLong(), anyLong());
        
        // 执行测试 - 计数器为0
        boolean result1 = runInstanceStateProcessService.handleCounterValue(instanceId, instanceInfoId, currentTimestamp, 0);
        
        // 执行测试 - 计数器大于0
        boolean result2 = runInstanceStateProcessService.handleCounterValue(instanceId, instanceInfoId, currentTimestamp, 1);
        
        // 执行测试 - 计数器小于0
        boolean result3 = runInstanceStateProcessService.handleCounterValue(instanceId, instanceInfoId, currentTimestamp, -1);
        
        // 验证结果
        assertTrue(result1);
        assertTrue(result2);
        assertFalse(result3);
        verify(runInstanceStateProcessService).processZeroCounter(instanceId, currentTimestamp);
    }

    @Test
    @DisplayName("计算实例状态和结果 - 成功场景")
    void calculateInstanceStateAndResult_Success() {
        // 准备测试数据
        List<RunInstanceInfoEntity> infoList = Arrays.asList(
            createInstanceInfoEntity(1, 1),
            createInstanceInfoEntity(2, 2)
        );
        when(runInstanceInfoMapper.selectInstanceInfosByInstanceId(instanceId)).thenReturn(infoList);

        // 执行测试
        int[] result = runInstanceStateProcessService.calculateInstanceStateAndResult(instanceId);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.length);
        assertEquals(2, result[0]); // 取终止状态(2)，这是最高优先级
        assertEquals(1, result[1]); // 取失败结果(1)，这是最高优先级
        verify(runInstanceInfoMapper).selectInstanceInfosByInstanceId(instanceId);
    }

    @Test
    @DisplayName("更新实例状态和结果 - 成功场景")
    void updateInstanceStateAndResult_Success() {
        // 准备测试数据
        int state = 2;
        int result = 2;
        when(runInstanceMapper.updateStateAndResultByIdAndTimestamp(eq(instanceId), eq(state), eq(result)))
            .thenReturn(1);

        // 执行测试
        boolean success = runInstanceStateProcessService.updateInstanceStateAndResult(instanceId, state, result, currentTimestamp);

        // 验证结果
        assertTrue(success);
        verify(runInstanceMapper).updateStateAndResultByIdAndTimestamp(instanceId, state, result);
    }

    @Test
    @DisplayName("清理实例计数器 - 成功场景")
    void clearInstanceCounter_Success() throws ContrastBusinessException {
        // 执行测试
        boolean result = runInstanceStateProcessService.clearInstanceCounter(instanceId);

        // 验证结果
        assertTrue(result);
        verify(taskCounterComponent).clearInstanceCounter(instanceId);
    }

    @Test
    @DisplayName("测试服务实例化")
    void testServiceInitialization() {
        // 验证服务实例已正确初始化
        assertNotNull(runInstanceStateProcessService);
    }

    /**
     * 创建运行实例详情实体用于测试
     */
    private RunInstanceInfoEntity createInstanceInfoEntity(int state, int result) {
        RunInstanceInfoEntity entity = new RunInstanceInfoEntity();
        entity.setId(100L);
        entity.setEnvcRunInstanceId(instanceId);
        entity.setState(state);
        entity.setResult(result);
        return entity;
    }
} 