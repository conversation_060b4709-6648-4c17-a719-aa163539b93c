package com.ideal.envc.service.impl;

import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.sysm.SystemInteract;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 中心Service单元测试
 */
@ExtendWith(MockitoExtension.class)
public class CenterServiceImplTest {

    @Mock
    private SystemInteract systemInteract;

    @InjectMocks
    private CenterServiceImpl centerService;

    private List<CenterDto> mockCenterList;
    private CenterQueryDto mockQueryDto;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockCenterList = new ArrayList<>();
        
        CenterDto centerDto1 = new CenterDto();
        centerDto1.setId(1L);
        centerDto1.setName("测试中心1");
        
        CenterDto centerDto2 = new CenterDto();
        centerDto2.setId(2L);
        centerDto2.setName("测试中心2");
        
        mockCenterList.add(centerDto1);
        mockCenterList.add(centerDto2);
        
        // 初始化查询条件
        mockQueryDto = new CenterQueryDto();
        mockQueryDto.setName("测试中心");
    }

    @Test
    @DisplayName("测试获取中心列表 - 成功场景")
    void testGetCenterList_Success() {
        // 模拟SystemInteract方法返回
        when(systemInteract.getCenterList(any(CenterQueryDto.class))).thenReturn(mockCenterList);

        // 执行测试方法
        List<CenterDto> result = centerService.getCenterList(mockQueryDto);

        // 断言结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals("测试中心1", result.get(0).getName());
        assertEquals(2L, result.get(1).getId());
        assertEquals("测试中心2", result.get(1).getName());

        // 验证方法调用
        verify(systemInteract, times(1)).getCenterList(mockQueryDto);
    }

    @Test
    @DisplayName("测试获取中心列表 - 无查询条件")
    void testGetCenterList_NullQuery() {
        // 模拟SystemInteract方法返回
        when(systemInteract.getCenterList(null)).thenReturn(mockCenterList);

        // 执行测试方法
        List<CenterDto> result = centerService.getCenterList(null);

        // 断言结果
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证方法调用
        verify(systemInteract, times(1)).getCenterList(null);
    }

    @Test
    @DisplayName("测试获取中心列表 - 空列表场景")
    void testGetCenterList_EmptyList() {
        // 模拟SystemInteract方法返回空列表
        when(systemInteract.getCenterList(any(CenterQueryDto.class))).thenReturn(new ArrayList<>());

        // 执行测试方法
        List<CenterDto> result = centerService.getCenterList(mockQueryDto);

        // 断言结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(systemInteract, times(1)).getCenterList(mockQueryDto);
    }

    @Test
    @DisplayName("测试获取中心列表 - 系统交互异常")
    void testGetCenterList_SystemInteractException() {
        // 模拟SystemInteract方法抛出异常
        when(systemInteract.getCenterList(any(CenterQueryDto.class))).thenThrow(new RuntimeException("系统交互异常"));

        // 执行测试方法并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> centerService.getCenterList(mockQueryDto));
        assertEquals("系统交互异常", exception.getMessage());

        // 验证方法调用
        verify(systemInteract, times(1)).getCenterList(mockQueryDto);
    }

    @Test
    @DisplayName("测试获取中心列表 - 返回null场景")
    void testGetCenterList_ReturnNull() {
        // 模拟SystemInteract方法返回null
        when(systemInteract.getCenterList(any(CenterQueryDto.class))).thenReturn(null);

        // 执行测试方法
        List<CenterDto> result = centerService.getCenterList(mockQueryDto);

        // 断言结果
        assertNull(result);

        // 验证方法调用
        verify(systemInteract, times(1)).getCenterList(mockQueryDto);
    }
} 