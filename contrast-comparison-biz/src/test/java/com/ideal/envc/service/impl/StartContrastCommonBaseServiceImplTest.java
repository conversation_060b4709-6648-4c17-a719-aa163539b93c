package com.ideal.envc.service.impl;

import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.bean.*;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartFlowDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.enums.RuleITypeEnums;
import com.ideal.envc.model.enums.RuleMatchTypeEnums;
import com.ideal.envc.model.enums.RuleWayEnums;
import com.ideal.envc.producer.SendDataToEngineMqProducer;
import com.ideal.envc.service.IStartContrastBaseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * StartContrastCommonBaseServiceImpl单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StartContrastCommonBaseServiceImpl单元测试")
class StartContrastCommonBaseServiceImplTest {

    @Mock
    private SendDataToEngineMqProducer sendDataToEngineMqProducer;

    @Mock
    private IStartContrastBaseService startContrastBaseService;

    @InjectMocks
    private StartContrastCommonBaseServiceImpl startContrastCommonBaseService;

    private UserDto userDto;
    private List<HierarchicalRunInstanceBean> hierarchicalInstanceList;
    private HierarchicalRunInstanceBean instanceBean;
    private HierarchicalRunInstanceInfoBean infoBean;
    private HierarchicalRunRuleBean ruleBean;
    private HierarchicalRunRuleContentBean ruleContentBean;
    private HierarchicalRunFlowBean ruleFlowBean;
    private List<StartTaskFlowDto> startTaskFlowDtoList;

    @BeforeEach
    void setUp() {
        // 初始化用户信息
        userDto = new UserDto();
        userDto.setId(1L);
        userDto.setFullName("测试用户");

        // 初始化规则流程信息
        ruleFlowBean = new HierarchicalRunFlowBean();
        ruleFlowBean.setId(100L);

        // 初始化规则内容信息
        ruleContentBean = new HierarchicalRunRuleContentBean();
        ruleContentBean.setId(200L);
        ruleContentBean.setContent("test content");

        // 初始化规则信息
        ruleBean = new HierarchicalRunRuleBean();
        ruleBean.setId(1L);
        ruleBean.setType(RuleITypeEnums.DIRECTORY.getCode());
        ruleBean.setWay(RuleWayEnums.ALL.getCode());
        ruleBean.setRuleType(RuleMatchTypeEnums.MATCH.getCode());
        ruleBean.setSourcePath("/source/path");
        ruleBean.setPath("/target/path");
        ruleBean.setEncode("UTF-8");
        ruleBean.setRuleContent(ruleContentBean);
        ruleBean.setRuleFlow(ruleFlowBean);
        // 添加必要的属性
        ruleBean.setModel(1);

        // 初始化实例详情信息
        infoBean = new HierarchicalRunInstanceInfoBean();
        infoBean.setId(1L);
        infoBean.setSourceComputerIp("***********");
        infoBean.setSourceComputerPort(22);
        infoBean.setSourceComputerOs("Linux");
        infoBean.setTargetComputerIp("***********");
        infoBean.setTargetComputerPort(22);
        infoBean.setTargetComputerOs("Windows");
        // 添加必要的属性
        infoBean.setSourceComputerId(1L);
        infoBean.setTargetComputerId(2L);
        infoBean.setRuleList(Arrays.asList(ruleBean));

        // 初始化实例信息
        instanceBean = new HierarchicalRunInstanceBean();
        instanceBean.setId(1L);
        instanceBean.setInstanceInfoList(Arrays.asList(infoBean));

        // 初始化层次化实例列表
        hierarchicalInstanceList = Arrays.asList(instanceBean);

        // 初始化任务流DTO列表
        startTaskFlowDtoList = new ArrayList<>();
        StartTaskFlowDto taskFlowDto = new StartTaskFlowDto();
        taskFlowDto.setUniqueTaskId(1L);
        taskFlowDto.setTasks(new ArrayList<>());
        startTaskFlowDtoList.add(taskFlowDto);
    }

    // 提供参数化测试数据的静态方法
    static Stream<Arguments> provideRuleTypeAndWayParams() {
        return Stream.of(
            Arguments.of(RuleITypeEnums.DIRECTORY.getCode(), RuleWayEnums.ALL.getCode(), RuleMatchTypeEnums.MATCH.getCode()),
            Arguments.of(RuleITypeEnums.DIRECTORY.getCode(), RuleWayEnums.PARTIAL.getCode(), RuleMatchTypeEnums.MATCH.getCode()),
            Arguments.of(RuleITypeEnums.DIRECTORY.getCode(), RuleWayEnums.PARTIAL.getCode(), RuleMatchTypeEnums.EXCLUDE.getCode()),
            Arguments.of(RuleITypeEnums.FILE.getCode(), RuleWayEnums.ALL.getCode(), RuleMatchTypeEnums.MATCH.getCode()),
            Arguments.of(RuleITypeEnums.FILE.getCode(), RuleWayEnums.PARTIAL.getCode(), RuleMatchTypeEnums.MATCH.getCode()),
            Arguments.of(RuleITypeEnums.FILE.getCode(), RuleWayEnums.PARTIAL.getCode(), RuleMatchTypeEnums.EXCLUDE.getCode()),
            Arguments.of(RuleITypeEnums.SCRIPT.getCode(), RuleWayEnums.ALL.getCode(), RuleMatchTypeEnums.MATCH.getCode()),
            Arguments.of(RuleITypeEnums.SCRIPT.getCode(), RuleWayEnums.PARTIAL.getCode(), RuleMatchTypeEnums.MATCH.getCode()),
            Arguments.of(RuleITypeEnums.SCRIPT.getCode(), RuleWayEnums.PARTIAL.getCode(), RuleMatchTypeEnums.EXCLUDE.getCode())
        );
    }

    // 提供操作系统名称参数化测试数据的静态方法
    static Stream<Arguments> provideOsNames() {
        return Stream.of(
            Arguments.of("Windows 10", ".bat"),
            Arguments.of("windows server 2019", ".bat"),
            Arguments.of("WIN7", ".bat"),
            Arguments.of("winxp", ".bat"),
            Arguments.of("Linux Ubuntu 20.04", ".sh"),
            Arguments.of("CentOS 7", ".sh"),
            Arguments.of("Red Hat Enterprise Linux", ".sh"),
            Arguments.of("macOS", ".sh"),
            Arguments.of("FreeBSD", ".sh"),
            Arguments.of("AIX", ".sh"),
            Arguments.of("Solaris", ".sh")
        );
    }

    @Nested
    @DisplayName("buildTaskFlowDtoList方法测试")
    class BuildTaskFlowDtoListTest {

        @Test
        @DisplayName("当层次化实例列表为空时应返回空列表")
        void should_return_empty_list_when_hierarchical_instance_list_is_empty() {
            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(Collections.emptyList(), userDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("当层次化实例列表为null时应返回空列表")
        void should_return_empty_list_when_hierarchical_instance_list_is_null() {
            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(null, userDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("当实例没有实例详情时应跳过该实例")
        void should_skip_instance_when_no_instance_info() {
            // Given
            instanceBean.setInstanceInfoList(null);

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }

        @Test
        @DisplayName("当实例详情没有规则时应跳过该实例详情")
        void should_skip_instance_info_when_no_rules() {
            // Given
            infoBean.setRuleList(null);

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }

        @ParameterizedTest
        @MethodSource("com.ideal.envc.service.impl.StartContrastCommonBaseServiceImplTest#provideRuleTypeAndWayParams")
        @DisplayName("应正确构建不同规则类型和方式的任务流DTO")
        void should_build_task_flow_dto_correctly_for_different_rule_types_and_ways(
                Long ruleType, Integer way, Integer ruleMatchType) {
            // Given
            ruleBean.setType(ruleType);
            ruleBean.setWay(way);
            ruleBean.setRuleType(ruleMatchType);

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            assertNotNull(result);
            assertEquals(1, result.size());

            StartTaskFlowDto taskFlowDto = result.get(0);
            assertEquals(1L, taskFlowDto.getUniqueTaskId());
            assertEquals(1, taskFlowDto.getTasks().size());

            StartFlowDto task = taskFlowDto.getTasks().get(0);
            assertEquals(ContrastConstants.CONTRAST_PRJ_NAME, task.getPrjName());
            
            // 验证工作流名称
            assertEquals(ContrastConstants.CONTRAST_FILE_COMPARE_FLOW_NAME, task.getFlowName());

            assertEquals("Instance_1_1", task.getIinsName());
            assertTrue(task.getComment().contains("一致性比对任务：1-类型："));
            assertEquals(ContrastConstants.CONTRAST_PRJ_TYPE, task.getType());
            assertEquals(ContrastConstants.CONTRAST_PRJ_TYPE, task.getPrjType());
            assertFalse(task.getIsAdvance());
            assertEquals(userDto, task.getUserInfo());
            assertNull(task.getLogConfig());
            assertFalse(task.getForceEfficiencyPrior());
            assertNull(task.getStarterSucElem());
            assertEquals(100L, task.getTaskFlowId());

            // 验证环境变量
            Map<String, Serializable> envVars = task.getEnvVars();
            assertNotNull(envVars);
            assertTrue(envVars.containsKey(ContrastConstants.CONTRAST_FLOW_ENV_BUTTERFLY_VERSION));

            // 验证参数列表
            List<Serializable> args = task.getArgs();
            assertNotNull(args);
            assertFalse(args.isEmpty());
        }

        @Test
        @DisplayName("目录规则全部方式应正确构建参数列表")
        void should_build_correct_args_for_directory_all_way() {
            // Given
            ruleBean.setType(RuleITypeEnums.DIRECTORY.getCode());
            ruleBean.setWay(RuleWayEnums.ALL.getCode());

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals(9, args.size());
            assertEquals("socket", args.get(0));
            assertEquals("all", args.get(1));
            assertEquals("/source/path", args.get(2));
            assertEquals("***********:22", args.get(3));
            assertEquals("/target/path", args.get(4));
            assertEquals("***********:22", args.get(5));
            assertEquals("", args.get(6)); // content为空
            assertEquals(".sh", args.get(7)); // Linux系统默认.sh
            assertEquals("0", args.get(8));
        }

        @Test
        @DisplayName("目录规则部分匹配方式应正确构建参数列表")
        void should_build_correct_args_for_directory_partial_match_way() {
            // Given
            ruleBean.setType(RuleITypeEnums.DIRECTORY.getCode());
            ruleBean.setWay(RuleWayEnums.PARTIAL.getCode());
            ruleBean.setRuleType(RuleMatchTypeEnums.MATCH.getCode());

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals(9, args.size());
            assertEquals("socket", args.get(0));
            assertEquals("part", args.get(1));
            assertEquals("/source/path", args.get(2));
            assertEquals("***********:22", args.get(3));
            assertEquals("/target/path", args.get(4));
            assertEquals("***********:22", args.get(5));
            assertEquals("--include test content", args.get(6));
            assertEquals(".sh", args.get(7));
            assertEquals("0", args.get(8));
        }

        @Test
        @DisplayName("目录规则部分排除方式应正确构建参数列表")
        void should_build_correct_args_for_directory_partial_exclude_way() {
            // Given
            ruleBean.setType(RuleITypeEnums.DIRECTORY.getCode());
            ruleBean.setWay(RuleWayEnums.PARTIAL.getCode());
            ruleBean.setRuleType(RuleMatchTypeEnums.EXCLUDE.getCode());

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals(9, args.size());
            assertEquals("--exclude test content", args.get(6));
        }

        @Test
        @DisplayName("文件规则全部方式应正确构建参数列表")
        void should_build_correct_args_for_file_all_way() {
            // Given
            ruleBean.setType(RuleITypeEnums.FILE.getCode());
            ruleBean.setWay(RuleWayEnums.ALL.getCode());

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals(15, args.size());
            assertEquals("***********:22", args.get(0));
            assertEquals("/source/path", args.get(1));
            assertEquals("UTF-8", args.get(2));
            assertEquals("文件获取", args.get(3));
            assertEquals("", args.get(4)); // 基线内容
            assertEquals("", args.get(5)); // checkContent
            assertEquals("", args.get(6)); // excludeCheckContent
            assertEquals("***********:22", args.get(7));
            assertEquals("/target/path", args.get(8));
            assertEquals("UTF-8", args.get(9));
            assertEquals("文件获取", args.get(10));
            assertEquals("", args.get(11)); // 基线内容
            assertEquals("", args.get(12)); // checkContent
            assertEquals("", args.get(13)); // excludeCheckContent
            assertEquals(false, args.get(14));
        }

        @Test
        @DisplayName("脚本规则在Linux系统应添加sh前缀")
        void should_add_sh_prefix_for_script_rule_on_linux() {
            // Given
            ruleBean.setType(RuleITypeEnums.SCRIPT.getCode());
            ruleBean.setWay(RuleWayEnums.ALL.getCode());
            infoBean.setSourceComputerOs("Linux");

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals("sh /source/path", args.get(1));
            assertEquals("sh /target/path", args.get(8));
        }

        @Test
        @DisplayName("脚本规则在AIX系统应添加sh前缀")
        void should_add_sh_prefix_for_script_rule_on_aix() {
            // Given
            ruleBean.setType(RuleITypeEnums.SCRIPT.getCode());
            ruleBean.setWay(RuleWayEnums.ALL.getCode());
            infoBean.setSourceComputerOs("AIX");

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals("sh /source/path", args.get(1));
            assertEquals("sh /target/path", args.get(8));
        }

        @Test
        @DisplayName("脚本规则在Windows系统不应添加sh前缀")
        void should_not_add_sh_prefix_for_script_rule_on_windows() {
            // Given
            ruleBean.setType(RuleITypeEnums.SCRIPT.getCode());
            ruleBean.setWay(RuleWayEnums.ALL.getCode());
            infoBean.setSourceComputerOs("Windows");

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals("/source/path", args.get(1));
            assertEquals("/target/path", args.get(8));
        }

        @Test
        @DisplayName("文件规则部分匹配方式应正确设置checkContent")
        void should_set_check_content_for_file_partial_match_way() {
            // Given
            ruleBean.setType(RuleITypeEnums.FILE.getCode());
            ruleBean.setWay(RuleWayEnums.PARTIAL.getCode());
            ruleBean.setRuleType(RuleMatchTypeEnums.MATCH.getCode());

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals("test content", args.get(5)); // checkContent
            assertEquals("", args.get(6)); // excludeCheckContent
            assertEquals("test content", args.get(12)); // checkContent
            assertEquals("", args.get(13)); // excludeCheckContent
        }

        @Test
        @DisplayName("文件规则部分排除方式应正确设置excludeCheckContent")
        void should_set_exclude_check_content_for_file_partial_exclude_way() {
            // Given
            ruleBean.setType(RuleITypeEnums.FILE.getCode());
            ruleBean.setWay(RuleWayEnums.PARTIAL.getCode());
            ruleBean.setRuleType(RuleMatchTypeEnums.EXCLUDE.getCode());

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals("", args.get(5)); // checkContent
            assertEquals("test content", args.get(6)); // excludeCheckContent
            assertEquals("", args.get(12)); // checkContent
            assertEquals("test content", args.get(13)); // excludeCheckContent
        }

        @Test
        @DisplayName("当规则内容为null时应正确处理")
        void should_handle_null_rule_content() {
            // Given
            ruleBean.setType(RuleITypeEnums.DIRECTORY.getCode());
            ruleBean.setWay(RuleWayEnums.PARTIAL.getCode());
            ruleBean.setRuleType(RuleMatchTypeEnums.MATCH.getCode());
            ruleBean.setRuleContent(null);

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

            // Then
            StartFlowDto task = result.get(0).getTasks().get(0);
            List<Serializable> args = task.getArgs();
            
            assertEquals("--include ", args.get(6));
        }

        @Test
        @DisplayName("应正确处理多个实例和多个规则")
        void should_handle_multiple_instances_and_rules() {
            // Given
            HierarchicalRunRuleBean ruleBean2 = new HierarchicalRunRuleBean();
            ruleBean2.setId(2L);
            ruleBean2.setType(RuleITypeEnums.FILE.getCode());
            ruleBean2.setWay(RuleWayEnums.ALL.getCode());
            ruleBean2.setRuleType(RuleMatchTypeEnums.MATCH.getCode());
            ruleBean2.setSourcePath("/source/path2");
            ruleBean2.setPath("/target/path2");
            ruleBean2.setEncode("UTF-8");
            ruleBean2.setRuleContent(ruleContentBean);
            ruleBean2.setRuleFlow(ruleFlowBean);
            // 添加必要的属性
            ruleBean2.setModel(1);

            infoBean.setRuleList(Arrays.asList(ruleBean, ruleBean2));

            HierarchicalRunInstanceBean instanceBean2 = new HierarchicalRunInstanceBean();
            instanceBean2.setId(2L);
            instanceBean2.setInstanceInfoList(Arrays.asList(infoBean));

            List<HierarchicalRunInstanceBean> multipleInstances = Arrays.asList(instanceBean, instanceBean2);

            // When
            List<StartTaskFlowDto> result = startContrastCommonBaseService.buildTaskFlowDtoList(multipleInstances, userDto);

            // Then
            assertNotNull(result);
            assertEquals(2, result.size());
            
            // 验证第一个实例
            StartTaskFlowDto taskFlowDto1 = result.get(0);
            assertEquals(1L, taskFlowDto1.getUniqueTaskId());
            assertEquals(2, taskFlowDto1.getTasks().size());
            
            // 验证第二个实例
            StartTaskFlowDto taskFlowDto2 = result.get(1);
            assertEquals(2L, taskFlowDto2.getUniqueTaskId());
            assertEquals(2, taskFlowDto2.getTasks().size());
        }

    }

    @Nested
    @DisplayName("startContrastSendAndUpdateState方法测试")
    class StartContrastSendAndUpdateStateTest {

        private List<StartTaskFlowDto> startTaskFlowDtoList;

        @BeforeEach
        void setUp() {
            StartTaskFlowDto taskFlowDto = new StartTaskFlowDto();
            taskFlowDto.setUniqueTaskId(1L);
            taskFlowDto.setTasks(new ArrayList<>());
            startTaskFlowDtoList = Arrays.asList(taskFlowDto);
        }

        @Test
        @DisplayName("当任务流DTO列表为空时应返回false")
        void should_return_false_when_task_flow_dto_list_is_empty() throws EngineServiceException {
            // When
            boolean result = startContrastCommonBaseService.startContrastSendAndUpdateState(Collections.emptyList(), "测试描述");

            // Then
            assertFalse(result);
            verify(startContrastBaseService, never()).updateInstanceStatusToInitial(any());
            verify(sendDataToEngineMqProducer, never()).sendStartTaskFlowData(any());
        }

        @Test
        @DisplayName("当发送MQ消息成功时应返回true")
        void should_return_true_when_send_mq_message_success() throws EngineServiceException {
            // Given
            when(sendDataToEngineMqProducer.sendStartTaskFlowData(any())).thenReturn(true);

            // When
            boolean result = startContrastCommonBaseService.startContrastSendAndUpdateState(startTaskFlowDtoList, "测试描述");

            // Then
            assertTrue(result);
            verify(startContrastBaseService).updateInstanceStatusToInitial(1L);
            verify(sendDataToEngineMqProducer).sendStartTaskFlowData(any(StartTaskFlowDto.class));
        }

        @Test
        @DisplayName("当发送MQ消息失败时应抛出EngineServiceException")
        void should_throw_engine_service_exception_when_send_mq_message_fail() throws EngineServiceException {
            // Given
            when(sendDataToEngineMqProducer.sendStartTaskFlowData(any())).thenReturn(false);

            // When & Then
            EngineServiceException exception = assertThrows(EngineServiceException.class, () -> {
                startContrastCommonBaseService.startContrastSendAndUpdateState(startTaskFlowDtoList, "测试描述");
            });

            assertEquals("发送MQ消息到引擎消费MQ主题失败", exception.getMessage());
            verify(startContrastBaseService).updateInstanceStatusToInitial(1L);
            verify(sendDataToEngineMqProducer).sendStartTaskFlowData(any(StartTaskFlowDto.class));
        }

        @Test
        @DisplayName("应正确处理多个任务流DTO")
        void should_handle_multiple_task_flow_dtos() throws EngineServiceException {
            // Given
            StartTaskFlowDto taskFlowDto2 = new StartTaskFlowDto();
            taskFlowDto2.setUniqueTaskId(2L);
            taskFlowDto2.setTasks(new ArrayList<>());
            
            List<StartTaskFlowDto> multipleTaskFlowDtos = Arrays.asList(startTaskFlowDtoList.get(0), taskFlowDto2);
            when(sendDataToEngineMqProducer.sendStartTaskFlowData(any())).thenReturn(true);

            // When
            boolean result = startContrastCommonBaseService.startContrastSendAndUpdateState(multipleTaskFlowDtos, "测试描述");

            // Then
            assertTrue(result);
            verify(startContrastBaseService).updateInstanceStatusToInitial(1L);
            verify(startContrastBaseService).updateInstanceStatusToInitial(2L);
            verify(sendDataToEngineMqProducer, times(2)).sendStartTaskFlowData(any(StartTaskFlowDto.class));
        }

        @Test
        @DisplayName("当第二个任务发送失败时应抛出异常")
        void should_throw_exception_when_second_task_send_fail() throws EngineServiceException {
            // Given
            StartTaskFlowDto taskFlowDto2 = new StartTaskFlowDto();
            taskFlowDto2.setUniqueTaskId(2L);
            taskFlowDto2.setTasks(new ArrayList<>());
            
            List<StartTaskFlowDto> multipleTaskFlowDtos = Arrays.asList(startTaskFlowDtoList.get(0), taskFlowDto2);
            when(sendDataToEngineMqProducer.sendStartTaskFlowData(any()))
                .thenReturn(true)  // 第一个成功
                .thenReturn(false); // 第二个失败

            // When & Then
            EngineServiceException exception = assertThrows(EngineServiceException.class, () -> {
                startContrastCommonBaseService.startContrastSendAndUpdateState(multipleTaskFlowDtos, "测试描述");
            });

            assertEquals("发送MQ消息到引擎消费MQ主题失败", exception.getMessage());
            verify(startContrastBaseService).updateInstanceStatusToInitial(1L);
            verify(startContrastBaseService).updateInstanceStatusToInitial(2L);
            verify(sendDataToEngineMqProducer, times(2)).sendStartTaskFlowData(any(StartTaskFlowDto.class));
        }
    }

    @Nested
    @DisplayName("getScriptExtend方法测试")
    class GetScriptExtendTest {

        @Test
        @DisplayName("当操作系统包含win时应返回.bat")
        void should_return_bat_when_os_contains_win() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend("Windows 10");

            // Then
            assertEquals(".bat", result);
        }

        @Test
        @DisplayName("当操作系统包含WIN时应返回.bat")
        void should_return_bat_when_os_contains_win_uppercase() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend("WIN SERVER 2019");

            // Then
            assertEquals(".bat", result);
        }

        @Test
        @DisplayName("当操作系统为Linux时应返回.sh")
        void should_return_sh_when_os_is_linux() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend("Linux");

            // Then
            assertEquals(".sh", result);
        }

        @Test
        @DisplayName("当操作系统为Unix时应返回.sh")
        void should_return_sh_when_os_is_unix() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend("Unix");

            // Then
            assertEquals(".sh", result);
        }

        @Test
        @DisplayName("当操作系统为null时应返回.sh")
        void should_return_sh_when_os_is_null() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend(null);

            // Then
            assertEquals(".sh", result);
        }

        @Test
        @DisplayName("当操作系统为空字符串时应返回.sh")
        void should_return_sh_when_os_is_empty() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend("");

            // Then
            assertEquals(".sh", result);
        }

        @Test
        @DisplayName("当操作系统为空白字符串时应返回.sh")
        void should_return_sh_when_os_is_blank() {
            // When
            String result = startContrastCommonBaseService.getScriptExtend("   ");

            // Then
            assertEquals(".sh", result);
        }

        @ParameterizedTest
        @MethodSource("com.ideal.envc.service.impl.StartContrastCommonBaseServiceImplTest#provideOsNames")
        @DisplayName("应正确识别不同操作系统名称")
        void should_correctly_identify_different_os_names(String osName, String expectedExtension) {
            // When
            String result = startContrastCommonBaseService.getScriptExtend(osName);

            // Then
            assertEquals(expectedExtension, result);
        }
    }
} 