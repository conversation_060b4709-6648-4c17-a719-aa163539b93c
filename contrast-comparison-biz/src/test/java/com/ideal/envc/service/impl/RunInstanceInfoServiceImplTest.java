package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.model.dto.RunInstanceInfoDto;
import com.ideal.envc.model.dto.RunInstanceInfoQueryDto;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * RunInstanceInfoServiceImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
public class RunInstanceInfoServiceImplTest {

    @Mock
    private RunInstanceInfoMapper runInstanceInfoMapper;

    @InjectMocks
    private RunInstanceInfoServiceImpl runInstanceInfoService;

    private RunInstanceInfoEntity mockEntity;
    private RunInstanceInfoDto mockDto;
    private List<RunInstanceInfoEntity> mockEntityList;
    private PageInfo<RunInstanceInfoDto> pageInfo;

    @BeforeEach
    void setUp() {
        // 初始化测试对象
        mockEntity = new RunInstanceInfoEntity();
        mockEntity.setId(1L);
        mockEntity.setEnvcRunInstanceId(101L);
        mockEntity.setEnvcPlanId(201L);
        mockEntity.setBusinessSystemId(301L);
        mockEntity.setSourceCenterId(401L);
        mockEntity.setSourceCenterName("源中心");
        mockEntity.setTargetCenterId(501L);
        mockEntity.setTargetCenterName("目标中心");
        mockEntity.setSourceComputerId(601L);
        mockEntity.setSourceComputerIp("***********");
        mockEntity.setSourceComputerPort(22);
        mockEntity.setSourceComputerOs("Linux");
        mockEntity.setTargetComputerId(701L);
        mockEntity.setTargetComputerIp("***********");
        mockEntity.setTargetComputerPort(22);
        mockEntity.setTargetComputerOs("Windows");
        mockEntity.setStoreTime(new Date());
        mockEntity.setResult(0);
        mockEntity.setState(1);

        // 初始化DTO对象
        mockDto = new RunInstanceInfoDto();
        mockDto.setId(1L);
        mockDto.setEnvcRunInstanceId(101L);
        mockDto.setEnvcPlanId(201L);
        mockDto.setBusinessSystemId(301L);
        mockDto.setSourceCenterId(401L);
        mockDto.setSourceCenterName("源中心");
        mockDto.setTargetCenterId(501L);
        mockDto.setTargetCenterName("目标中心");
        mockDto.setSourceComputerId(601L);
        mockDto.setSourceComputerIp("***********");
        mockDto.setSourceComputerPort(22);
        mockDto.setSourceComputerOs("Linux");
        mockDto.setTargetComputerId(701L);
        mockDto.setTargetComputerIp("***********");
        mockDto.setTargetComputerPort(22);
        mockDto.setTargetComputerOs("Windows");
        mockDto.setStoreTime(new Date());
        mockDto.setResult(0);
        mockDto.setState(1);

        // 初始化列表数据
        mockEntityList = new ArrayList<>();
        mockEntityList.add(mockEntity);

        // 初始化分页对象
        pageInfo = new PageInfo<>();
        List<RunInstanceInfoDto> dtoList = new ArrayList<>();
        dtoList.add(mockDto);
        pageInfo.setList(dtoList);
        pageInfo.setTotal(1);
        pageInfo.setPageNum(1);
        pageInfo.setPageSize(10);
        pageInfo.setPages(1);
    }

    @Test
    @DisplayName("测试根据ID查询实例详情")
    void testSelectRunInstanceInfoById() {
        // 模拟Mapper方法调用
        doReturn(mockEntity).when(runInstanceInfoMapper).selectRunInstanceInfoById(anyLong());

        // 执行测试方法
        RunInstanceInfoDto result = runInstanceInfoService.selectRunInstanceInfoById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(101L, result.getEnvcRunInstanceId());
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoById(1L);
    }

    @Test
    @DisplayName("测试查询实例详情列表")
    void testSelectRunInstanceInfoList() {
        // 准备测试参数
        RunInstanceInfoQueryDto queryDto = new RunInstanceInfoQueryDto();
        queryDto.setEnvcRunInstanceId(101L);
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法调用
            doReturn(mockEntityList).when(runInstanceInfoMapper).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunInstanceInfoDto.class))).thenReturn(pageInfo);

            // 执行测试方法
            PageInfo<RunInstanceInfoDto> result = runInstanceInfoService.selectRunInstanceInfoList(queryDto, pageNum, pageSize);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getList());
            assertEquals(1, result.getList().size());
            assertEquals(1L, result.getList().get(0).getId());
            
            // 验证Mapper方法被调用
            verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
            
            // 验证PageDataUtil.toDtoPage方法被调用
            mockedPageDataUtil.verify(() -> PageDataUtil.toDtoPage(mockEntityList, RunInstanceInfoDto.class));
        }
    }

    @Test
    @DisplayName("测试插入实例详情")
    void testInsertRunInstanceInfo() {
        // 模拟Mapper方法调用
        doReturn(1).when(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法
        int result = runInstanceInfoService.insertRunInstanceInfo(mockDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试更新实例详情")
    void testUpdateRunInstanceInfo() {
        // 模拟Mapper方法调用
        doReturn(1).when(runInstanceInfoMapper).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法
        int result = runInstanceInfoService.updateRunInstanceInfo(mockDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试根据ID批量删除实例详情")
    void testDeleteRunInstanceInfoByIds() {
        // 准备测试参数
        Long[] ids = {1L, 2L, 3L};
        
        // 模拟Mapper方法调用
        doReturn(3).when(runInstanceInfoMapper).deleteRunInstanceInfoByIds(ids);

        // 执行测试方法
        int result = runInstanceInfoService.deleteRunInstanceInfoByIds(ids);

        // 验证结果
        assertEquals(3, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).deleteRunInstanceInfoByIds(ids);
    }

    @Test
    @DisplayName("测试根据ID查询实例详情 - 返回null")
    void testSelectRunInstanceInfoById_ReturnNull() {
        // 模拟Mapper方法返回null
        doReturn(null).when(runInstanceInfoMapper).selectRunInstanceInfoById(anyLong());

        // 执行测试方法
        RunInstanceInfoDto result = runInstanceInfoService.selectRunInstanceInfoById(999L);

        // 验证结果 - BeanUtils.copy(null, class)会返回一个空对象而不是null
        assertNotNull(result);
        assertEquals(null, result.getId());
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoById(999L);
    }

    @Test
    @DisplayName("测试查询实例详情列表 - 空查询条件")
    void testSelectRunInstanceInfoList_NullQuery() {
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法调用
            doReturn(mockEntityList).when(runInstanceInfoMapper).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunInstanceInfoDto.class))).thenReturn(pageInfo);

            // 执行测试方法 - 传入null查询条件
            PageInfo<RunInstanceInfoDto> result = runInstanceInfoService.selectRunInstanceInfoList(null, 1, 10);

            // 验证结果
            assertNotNull(result);
            
            // 验证Mapper方法被调用
            verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
        }
    }

    @Test
    @DisplayName("测试查询实例详情列表 - 空结果")
    void testSelectRunInstanceInfoList_EmptyResult() {
        // 准备测试参数
        RunInstanceInfoQueryDto queryDto = new RunInstanceInfoQueryDto();
        queryDto.setEnvcRunInstanceId(999L);
        
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法返回空列表
            doReturn(new ArrayList<>()).when(runInstanceInfoMapper).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法返回空分页
            PageInfo<RunInstanceInfoDto> emptyPageInfo = new PageInfo<>(new ArrayList<>());
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunInstanceInfoDto.class))).thenReturn(emptyPageInfo);

            // 执行测试方法
            PageInfo<RunInstanceInfoDto> result = runInstanceInfoService.selectRunInstanceInfoList(queryDto, 1, 10);

            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.getList().size());
            
            // 验证Mapper方法被调用
            verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
        }
    }

    @Test
    @DisplayName("测试插入实例详情 - 插入失败")
    void testInsertRunInstanceInfo_Fail() {
        // 模拟Mapper方法返回0表示失败
        doReturn(0).when(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法
        int result = runInstanceInfoService.insertRunInstanceInfo(mockDto);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试插入实例详情 - 空对象")
    void testInsertRunInstanceInfo_NullDto() {
        // 模拟Mapper方法
        doReturn(1).when(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法 - 传入null
        int result = runInstanceInfoService.insertRunInstanceInfo(null);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试更新实例详情 - 更新失败")
    void testUpdateRunInstanceInfo_Fail() {
        // 模拟Mapper方法返回0表示失败
        doReturn(0).when(runInstanceInfoMapper).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法
        int result = runInstanceInfoService.updateRunInstanceInfo(mockDto);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试更新实例详情 - 空对象")
    void testUpdateRunInstanceInfo_NullDto() {
        // 模拟Mapper方法
        doReturn(1).when(runInstanceInfoMapper).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法 - 传入null
        int result = runInstanceInfoService.updateRunInstanceInfo(null);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试批量删除实例详情 - 空ID数组")
    void testDeleteRunInstanceInfoByIds_EmptyArray() {
        // 准备测试参数 - 空数组
        Long[] ids = new Long[]{};
        
        // 模拟Mapper方法调用
        doReturn(0).when(runInstanceInfoMapper).deleteRunInstanceInfoByIds(ids);

        // 执行测试方法
        int result = runInstanceInfoService.deleteRunInstanceInfoByIds(ids);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).deleteRunInstanceInfoByIds(ids);
    }

    @Test
    @DisplayName("测试批量删除实例详情 - null数组")
    void testDeleteRunInstanceInfoByIds_NullArray() {
        // 模拟Mapper方法调用
        doReturn(0).when(runInstanceInfoMapper).deleteRunInstanceInfoByIds(any());

        // 执行测试方法 - 传入null
        int result = runInstanceInfoService.deleteRunInstanceInfoByIds(null);

        // 验证结果
        assertEquals(0, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).deleteRunInstanceInfoByIds(null);
    }

    @Test
    @DisplayName("测试查询实例详情列表 - 大分页参数")
    void testSelectRunInstanceInfoList_LargePageParams() {
        // 准备测试参数
        RunInstanceInfoQueryDto queryDto = new RunInstanceInfoQueryDto();
        queryDto.setEnvcRunInstanceId(101L);
        
        try (MockedStatic<PageDataUtil> mockedPageDataUtil = Mockito.mockStatic(PageDataUtil.class)) {
            // 模拟Mapper方法调用
            doReturn(mockEntityList).when(runInstanceInfoMapper).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
            
            // 模拟PageDataUtil.toDtoPage方法
            mockedPageDataUtil.when(() -> PageDataUtil.toDtoPage(anyList(), eq(RunInstanceInfoDto.class))).thenReturn(pageInfo);

            // 执行测试方法 - 使用大分页参数
            PageInfo<RunInstanceInfoDto> result = runInstanceInfoService.selectRunInstanceInfoList(queryDto, 999, 1000);

            // 验证结果
            assertNotNull(result);
            
            // 验证Mapper方法被调用
            verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoList(any(RunInstanceInfoEntity.class));
        }
    }

    @Test
    @DisplayName("测试根据ID查询实例详情 - 无效ID")
    void testSelectRunInstanceInfoById_InvalidId() {
        // 模拟Mapper方法返回null
        doReturn(null).when(runInstanceInfoMapper).selectRunInstanceInfoById(anyLong());

        // 执行测试方法 - 传入负数ID
        RunInstanceInfoDto result = runInstanceInfoService.selectRunInstanceInfoById(-1L);

        // 验证结果 - BeanUtils.copy(null, class)会返回一个空对象而不是null
        assertNotNull(result);
        assertEquals(null, result.getId());
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).selectRunInstanceInfoById(-1L);
    }

    @Test
    @DisplayName("测试插入实例详情 - 部分字段为空")
    void testInsertRunInstanceInfo_PartialFields() {
        // 创建部分字段为空的DTO
        RunInstanceInfoDto partialDto = new RunInstanceInfoDto();
        partialDto.setId(1L);
        partialDto.setEnvcRunInstanceId(101L);
        // 其他字段为null

        // 模拟Mapper方法
        doReturn(1).when(runInstanceInfoMapper).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法
        int result = runInstanceInfoService.insertRunInstanceInfo(partialDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).insertRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试更新实例详情 - 部分字段为空")
    void testUpdateRunInstanceInfo_PartialFields() {
        // 创建部分字段为空的DTO
        RunInstanceInfoDto partialDto = new RunInstanceInfoDto();
        partialDto.setId(1L);
        partialDto.setResult(1); // 只设置部分字段

        // 模拟Mapper方法
        doReturn(1).when(runInstanceInfoMapper).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));

        // 执行测试方法
        int result = runInstanceInfoService.updateRunInstanceInfo(partialDto);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).updateRunInstanceInfo(any(RunInstanceInfoEntity.class));
    }

    @Test
    @DisplayName("测试批量删除实例详情 - 单个ID")
    void testDeleteRunInstanceInfoByIds_SingleId() {
        // 准备测试参数 - 单个ID
        Long[] ids = new Long[]{1L};
        
        // 模拟Mapper方法调用
        doReturn(1).when(runInstanceInfoMapper).deleteRunInstanceInfoByIds(ids);

        // 执行测试方法
        int result = runInstanceInfoService.deleteRunInstanceInfoByIds(ids);

        // 验证结果
        assertEquals(1, result);
        
        // 验证Mapper方法被调用
        verify(runInstanceInfoMapper, times(1)).deleteRunInstanceInfoByIds(ids);
    }
} 