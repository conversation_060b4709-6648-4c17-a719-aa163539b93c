<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.PlanMapper">

    <resultMap type="com.ideal.envc.model.entity.PlanEntity" id="PlanResult">
            <result property="id" column="iid"/>
            <result property="name" column="iname"/>
            <result property="planDesc" column="iplan_desc"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectPlan">
        select iid, iname, iplan_desc, icreator_name, icreator_id, icreate_time, iupdator_id, iupdator_name, iupdate_time
        from ieai_envc_plan
    </sql>

    <select id="selectPlanList" parameterType="com.ideal.envc.model.entity.PlanEntity" resultMap="PlanResult">
        <include refid="selectPlan"/>
        <where>
                        <if test="name != null  and name != ''">
                            and iname like concat('%', #{name}, '%')
                        </if>
                        <if test="planDesc != null  and planDesc != ''">
                            and iplan_desc = #{planDesc}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
                        <if test="updateTime != null ">
                            and iupdate_time = #{updateTime}
                        </if>
        </where>
    </select>

    <select id="selectPlanById" parameterType="Long"
            resultMap="PlanResult">
            <include refid="selectPlan"/>
            where iid = #{id}
    </select>

    <insert id="insertPlan" parameterType="com.ideal.envc.model.entity.PlanEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="name != null">iname,
                    </if>
                    <if test="planDesc != null">iplan_desc,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    icreate_time,
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="name != null">#{name},
                    </if>
                    <if test="planDesc != null">#{planDesc},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updatePlan" parameterType="com.ideal.envc.model.entity.PlanEntity">
        update ieai_envc_plan
        <trim prefix="SET" suffixOverrides=",">
                    <if test="name != null">iname =
                        #{name},
                    </if>
                    <if test="planDesc != null">iplan_desc =
                        #{planDesc},
                    </if>

                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deletePlanById" parameterType="Long">
        delete
        from ieai_envc_plan where iid = #{id}
    </delete>

    <delete id="deletePlanByIds" parameterType="String">
        delete from ieai_envc_plan where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkPlanNameExists" parameterType="String" resultType="int">
        select count(1) from ieai_envc_plan where iname = #{name}
    </select>

    <select id="checkPlanNameExistsExcludeId" resultType="int">
        select count(1) from ieai_envc_plan where iname = #{name} and iid != #{id}
    </select>
</mapper>