<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.DictionaryMapper">

    <resultMap type="com.ideal.envc.model.entity.DictionaryEntity" id="DictionaryResult">
            <result property="id" column="iid"/>
            <result property="code" column="icode"/>
            <result property="description" column="idescription"/>
            <result property="nit" column="init"/>
            <result property="deleted" column="ideleted"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectDictionary">
        select iid, icode, idescription, init, ideleted, icreator_name, icreator_id, icreate_time, iupdator_id, iupdator_name, iupdate_time
        from ieai_envc_dictionary
    </sql>

    <select id="selectDictionaryList" parameterType="com.ideal.envc.model.entity.DictionaryEntity" resultMap="DictionaryResult">
        <include refid="selectDictionary"/>
        <where>
                        <if test="code != null  and code != ''">
                            and icode = #{code}
                        </if>
                        <if test="description != null  and description != ''">
                            and idescription = #{description}
                        </if>
                        <if test="nit != null ">
                            and init = #{nit}
                        </if>
                        <if test="deleted != null ">
                            and ideleted = #{deleted}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
                        <if test="updateTime != null ">
                            and iupdate_time = #{updateTime}
                        </if>
        </where>
    </select>

    <select id="selectDictionaryById" parameterType="Long"
            resultMap="DictionaryResult">
            <include refid="selectDictionary"/>
            where iid = #{id}
    </select>

    <insert id="insertDictionary" parameterType="com.ideal.envc.model.entity.DictionaryEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_dictionary
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="code != null">icode,
                    </if>
                    <if test="description != null">idescription,
                    </if>
                    <if test="nit != null">init,
                    </if>
                    <if test="deleted != null">ideleted,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    icreate_time,
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                   iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="code != null">#{code},
                    </if>
                    <if test="description != null">#{description},
                    </if>
                    <if test="nit != null">#{nit},
                    </if>
                    <if test="deleted != null">#{deleted},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateDictionary" parameterType="com.ideal.envc.model.entity.DictionaryEntity">
        update ieai_envc_dictionary
        <trim prefix="SET" suffixOverrides=",">
                    <if test="code != null">icode =
                        #{code},
                    </if>
                    <if test="description != null">idescription =
                        #{description},
                    </if>
                    <if test="nit != null">init =
                        #{nit},
                    </if>
                    <if test="deleted != null">ideleted =
                        #{deleted},
                    </if>

                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time =  ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteDictionaryById" parameterType="Long">
        delete
        from ieai_envc_dictionary where iid = #{id}
    </delete>

    <delete id="deleteDictionaryByIds" parameterType="String">
        delete from ieai_envc_dictionary where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>