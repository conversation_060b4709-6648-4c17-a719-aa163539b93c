<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.StartContrastMapper">

    <!-- 根据查询条件查询方案信息（包含关联的系统、节点、规则） -->
    <select id="selectStartPlansByQuery" parameterType="com.ideal.envc.model.bean.StartContrastQueryBean" resultMap="StartPlanResultMap">
        SELECT
            p.iid AS plan_id,
            p.iname AS plan_name,
            pr.iid AS relation_id,
            pr.ibusiness_system_id AS system_id,
            proj.ibusiness_system_name AS system_name,
            scn.iid AS node_id,
            scn.isource_center_id AS source_center_id,
            scn.itarget_center_id AS target_center_id,
            scn.isource_computer_id AS source_computer_id,
            scn.isource_computer_ip AS source_computer_ip,
            scn.itarget_computer_id AS target_computer_id,
            scn.itarget_computer_ip AS target_computer_ip,
            nr.iid AS rule_id,
            nr.imodel AS rule_model,
            nr.itype AS rule_type,
            nr.ipath AS rule_path,
            nr.isource_path AS rule_source_path,
            nr.iencode AS rule_encode,
            nr.iway AS rule_way,
            nr.irule_type AS rule_rule_type,
            nr.ienabled AS rule_enabled,
            nr.ichild_level AS rule_child_level,
            nrc.iid AS rule_content_id,
            nrc.irule_content AS rule_content
        FROM
            ieai_envc_plan p
        INNER JOIN
            ieai_envc_plan_relation pr ON p.iid = pr.ienvc_plan_id
        INNER JOIN
            ieai_envc_project proj ON pr.ibusiness_system_id = proj.ibusiness_system_id
        INNER JOIN
            ieai_envc_system_computer_node scn ON proj.ibusiness_system_id = scn.ibusiness_system_id
        INNER JOIN
            ieai_envc_system_computer sc ON scn.isource_computer_id = sc.icomputer_id and proj.ibusiness_system_id = sc.ibusiness_system_id
        INNER JOIN
            ieai_envc_system_computer tc ON scn.itarget_computer_id = tc.icomputer_id and proj.ibusiness_system_id = tc.ibusiness_system_id
        INNER JOIN
            ieai_envc_node_relation nr ON scn.iid = nr.ienvc_system_computer_node_id
        LEFT JOIN
            ieai_envc_node_rule_content nrc ON nr.iid = nrc.ienvc_node_relation_id
        <where>
            <if test="planNameLike != null and planNameLike != ''">
                AND p.iname LIKE CONCAT('%', #{planNameLike}, '%')
            </if>
            <if test="planIds != null and planIds.size() > 0">
                AND p.iid IN
                <foreach collection="planIds" item="planId" open="(" separator="," close=")">
                    #{planId}
                </foreach>
            </if>
            <if test="systemIds != null and systemIds.size() > 0">
                AND proj.ibusiness_system_id IN
                <foreach collection="systemIds" item="systemId" open="(" separator="," close=")">
                    #{systemId}
                </foreach>
            </if>
            <if test="nodeIds != null and nodeIds.size() > 0">
                AND scn.iid IN
                <foreach collection="nodeIds" item="nodeId" open="(" separator="," close=")">
                    #{nodeId}
                </foreach>
            </if>
            <if test="ruleIds != null and ruleIds.size() > 0">
                AND nr.iid IN
                <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
                    #{ruleId}
                </foreach>
            </if>
            <if test="sourceCenterId != null">
                AND scn.isource_center_id = #{sourceCenterId}
            </if>
            <if test="targetCenterId != null">
                AND scn.itarget_center_id = #{targetCenterId}
            </if>
            <!-- 默认只查询有效的规则 -->
            AND (nr.ienabled = 0 OR nr.ienabled IS NULL)
        </where>
        ORDER BY p.iid, proj.ibusiness_system_id, scn.iid, nr.iid
    </select>

    <!-- 根据查询条件查询任务级方案信息（包含关联的系统、节点、规则和任务信息） -->
    <select id="selectStartPlansByTaskQuery" parameterType="com.ideal.envc.model.bean.StartContrastQueryBean" resultMap="StartPlanResultMap">
        SELECT
            p.iid AS plan_id,
            p.iname AS plan_name,
            pr.iid AS relation_id,
            pr.ibusiness_system_id AS system_id,
            p2.ibusiness_system_name AS system_name,
            scn.iid AS node_id,
            scn.isource_center_id AS source_center_id,
            scn.itarget_center_id AS target_center_id,
            scn.isource_computer_id AS source_computer_id,
            scn.isource_computer_ip AS source_computer_ip,
            scn.itarget_computer_id AS target_computer_id,
            scn.itarget_computer_ip AS target_computer_ip,
            nr.iid AS rule_id,
            nr.imodel AS rule_model,
            nr.itype AS rule_type,
            nr.ipath AS rule_path,
            nr.isource_path AS rule_source_path,
            nr.iencode AS rule_encode,
            nr.iway AS rule_way,
            nr.irule_type AS rule_rule_type,
            nr.ienabled AS rule_enabled,
            nr.ichild_level AS rule_child_level,
            nrc.iid AS rule_content_id,
            nrc.irule_content AS rule_content,
            t.iid AS task_id,
            t.icron AS task_cron,
            t.ienabled AS task_enabled,
            t.istate AS task_state
        FROM
            ieai_envc_task t
        LEFT JOIN
            ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        LEFT JOIN
            ieai_envc_plan_relation pr ON p.iid = pr.ienvc_plan_id
        LEFT JOIN
            ieai_envc_project p2 ON pr.ibusiness_system_id = p2.ibusiness_system_id
        LEFT JOIN
            ieai_envc_system_computer_node scn ON p2.ibusiness_system_id = scn.ibusiness_system_id
                      and (scn.isource_center_id = t.isource_center_id or t.isource_center_id is null)
                      and (scn.itarget_center_id = t.itarget_center_id or t.itarget_center_id is null)
        LEFT JOIN
            ieai_envc_system_computer scp ON scn.isource_computer_id = scp.icomputer_id and p2.ibusiness_system_id = scp.ibusiness_system_id
        LEFT JOIN
            ieai_envc_system_computer tcp ON scn.itarget_computer_id = tcp.icomputer_id and p2.ibusiness_system_id = tcp.ibusiness_system_id
        LEFT JOIN
            ieai_envc_node_relation nr ON scn.iid = nr.ienvc_system_computer_node_id
        LEFT JOIN
            ieai_envc_node_rule_content nrc ON nr.iid = nrc.ienvc_node_relation_id
        <where>
            <if test="taskIds != null and taskIds.size() > 0">
                AND t.iid IN
                <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
            <if test="planNameLike != null and planNameLike != ''">
                AND p.iname LIKE CONCAT('%', #{planNameLike}, '%')
            </if>
            <if test="planIds != null and planIds.size() > 0">
                AND p.iid IN
                <foreach collection="planIds" item="planId" open="(" separator="," close=")">
                    #{planId}
                </foreach>
            </if>
            <if test="systemIds != null and systemIds.size() > 0">
                AND p2.ibusiness_system_id IN
                <foreach collection="systemIds" item="systemId" open="(" separator="," close=")">
                    #{systemId}
                </foreach>
            </if>
            <if test="nodeIds != null and nodeIds.size() > 0">
                AND scn.iid IN
                <foreach collection="nodeIds" item="nodeId" open="(" separator="," close=")">
                    #{nodeId}
                </foreach>
            </if>
            <if test="ruleIds != null and ruleIds.size() > 0">
                AND nr.iid IN
                <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
                    #{ruleId}
                </foreach>
            </if>
            <if test="sourceCenterId != null">
                AND (scn.isource_center_id = #{sourceCenterId} OR t.isource_center_id = #{sourceCenterId})
            </if>
            <if test="targetCenterId != null">
                AND (scn.itarget_center_id = #{targetCenterId} OR t.itarget_center_id = #{targetCenterId})
            </if>
            <if test="userId != null">
                AND (p.icreator_id = #{userId} OR t.icreator_id = #{userId})
            </if>
            <if test="userName != null and userName != ''">
                AND (p.icreator_name = #{userName} OR t.icreator_name = #{userName})
            </if>
            <if test="startType != null and startType == 5">
                <!-- 任务级 -->
                AND t.iid IS NOT NULL
            </if>
            <if test="triggerFrom != null">
                <!-- 触发来源可以在后续处理中使用 -->
            </if>
            <!-- 默认只查询有效的规则 -->
            AND (nr.ienabled = 0 OR nr.ienabled IS NULL)
            <!-- 默认只查询启用的任务 -->
            AND t.ienabled = 1
        </where>
        ORDER BY
            t.iid, p.iid, p2.iid, scn.iid, nr.iid
    </select>

    <!-- 根据查询条件查询任务信息 -->
    <select id="selectStartTasksByQuery" parameterType="com.ideal.envc.model.bean.StartContrastQueryBean" resultType="com.ideal.envc.model.bean.StartTaskBean">
        SELECT
            t.iid AS id,
            t.ienvc_plan_id AS envcPlanId,
            p.iname AS planName,
            t.icron AS cron,
            t.ienabled AS enabled,
            t.istate AS state,
            t.isource_center_id AS sourceCenterId,
            sc.iname AS sourceCenterName,
            t.itarget_center_id AS targetCenterId,
            tc.iname AS targetCenterName,
            t.ischeduled_id AS scheduledId,
            t.icreator_id AS creatorId,
            t.icreator_name AS creatorName,
            t.icreate_time AS createTime,
            t.iupdator_id AS updatorId,
            t.iupdator_name AS updatorName,
            t.iupdate_time AS updateTime
        FROM
            ieai_envc_task t
        LEFT JOIN
            ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        LEFT JOIN
            ieai_envc_center sc ON t.isource_center_id = sc.iid
        LEFT JOIN
            ieai_envc_center tc ON t.itarget_center_id = tc.iid
        WHERE
            1=1
            <if test="taskIds != null and taskIds.size() > 0">
                AND t.iid IN
                <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
                    #{taskId}
                </foreach>
            </if>
            <if test="planIds != null and planIds.size() > 0">
                AND t.ienvc_plan_id IN
                <foreach collection="planIds" item="planId" open="(" separator="," close=")">
                    #{planId}
                </foreach>
            </if>
            <if test="sourceCenterId != null">
                AND t.isource_center_id = #{sourceCenterId}
            </if>
            <if test="targetCenterId != null">
                AND t.itarget_center_id = #{targetCenterId}
            </if>
        ORDER BY
            t.iid
    </select>

    <!-- 方案信息结果映射 -->
    <resultMap id="StartPlanResultMap" type="com.ideal.envc.model.bean.StartPlanBean">
        <id property="id" column="plan_id" />
        <result property="name" column="plan_name" />
        <result property="taskId" column="task_id" />
        <result property="taskCron" column="task_cron" />
        <result property="taskEnabled" column="task_enabled" />
        <result property="taskState" column="task_state" />
        <collection property="systems" ofType="com.ideal.envc.model.bean.StartSystemBean">
            <id property="id" column="relation_id" />
            <result property="businessSystemId" column="system_id" />
            <result property="businessSystemName" column="system_name" />
            <collection property="computerNodeBeans" ofType="com.ideal.envc.model.bean.StartComputerNodeBean">
                <id property="id" column="node_id" />
                <result property="businessSystemId" column="system_id" />
                <result property="sourceCenterId" column="source_center_id" />
                <result property="targetCenterId" column="target_center_id" />
                <result property="sourceComputerId" column="source_computer_id" />
                <result property="sourceComputerIp" column="source_computer_ip" />
                <result property="sourceComputerPort" column="source_computer_port" />
                <result property="targetComputerId" column="target_computer_id" />
                <result property="targetComputerIp" column="target_computer_ip" />
                <result property="targetComputerPort" column="target_computer_port" />
                <collection property="rules" ofType="com.ideal.envc.model.bean.StartRuleBean">
                    <id property="id" column="rule_id" />
                    <result property="envcNodeRelationId" column="node_id" />
                    <result property="model" column="rule_model" />
                    <result property="type" column="rule_type" />
                    <result property="path" column="rule_path" />
                    <result property="sourcePath" column="rule_source_path" />
                    <result property="encode" column="rule_encode" />
                    <result property="way" column="rule_way" />
                    <result property="ruleType" column="rule_rule_type" />
                    <result property="enabled" column="rule_enabled" />
                    <result property="childLevel" column="rule_child_level" />
                    <association property="contentBean" javaType="com.ideal.envc.model.bean.StartRuleContentBean">
                        <id property="id" column="rule_content_id" />
                        <result property="envcRunRuleId" column="rule_id" />
                        <result property="content" column="rule_content" />
                    </association>
                </collection>
            </collection>
        </collection>
    </resultMap>

    <!-- 根据任务ID列表查询任务信息 -->
    <select id="selectTaskInfoByIds" resultType="com.ideal.envc.model.bean.TaskInfoBean">
        SELECT
            t.iid AS id,
            t.ienvc_plan_id AS envcPlanId,
            p.iname AS envcPlanName,
            t.icron AS cron,
            t.ienabled AS enabled,
            t.istate AS state,
            t.isource_center_id AS sourceCenterId,
            sc.iname AS sourceCenterName,
            t.itarget_center_id AS targetCenterId,
            tc.iname AS targetCenterName,
            t.ischeduled_id AS scheduledId,
            t.icreator_id AS creatorId,
            t.icreator_name AS creatorName,
            t.icreate_time AS createTime,
            t.iupdator_id AS updatorId,
            t.iupdator_name AS updatorName,
            t.iupdate_time AS updateTime
        FROM
            ieai_envc_task t
        LEFT JOIN
            ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        LEFT JOIN
            ieai_envc_center sc ON t.isource_center_id = sc.iid
        LEFT JOIN
            ieai_envc_center tc ON t.itarget_center_id = tc.iid
        WHERE
            t.iid IN
            <foreach collection="list" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
    </select>

    <!-- 批量插入运行实例 -->
    <insert id="batchInsertRunInstance" parameterType="java.util.List">
        INSERT INTO ieai_envc_run_instance (
            iid, ienvc_plan_id, ienvc_task_id, iresult, istate, ifrom,
            istarter_name, istarter_id, istart_time, iend_time, ielapsed_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.envcPlanId}, #{item.envcTaskId}, #{item.result}, #{item.state}, #{item.from},
                #{item.starterName}, #{item.starterId}, ${@com.ideal.common.util.DbUtils@getCurrentTime()}, #{item.endTime}, #{item.elapsedTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入实例详情 -->
    <insert id="batchInsertRunInstanceInfo" parameterType="java.util.List">
        INSERT INTO ieai_envc_run_instance_info (
            iid, ienvc_run_instance_id, ienvc_plan_id, ibusiness_system_id,
            isource_center_id, isource_center_name, itarget_center_id, itarget_center_name, isource_computer_id,
            isource_computer_ip, isource_computer_port, isource_computer_os,
            itarget_computer_id, itarget_computer_ip, itarget_computer_port,
            itarget_computer_os, istore_time, iresult, istate
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.envcRunInstanceId}, #{item.envcPlanId}, #{item.businessSystemId},
                #{item.sourceCenterId}, #{item.sourceCenterName}, #{item.targetCenterId}, #{item.targetCenterName}, #{item.sourceComputerId},
                #{item.sourceComputerIp}, #{item.sourceComputerPort}, #{item.sourceComputerOs},
                #{item.targetComputerId}, #{item.targetComputerIp}, #{item.targetComputerPort},
                #{item.targetComputerOs}, ${@com.ideal.common.util.DbUtils@getCurrentTime()}, #{item.result}, #{item.state}
            )
        </foreach>
    </insert>

    <!-- 批量插入节点规则运行 -->
    <insert id="batchInsertRunRule" parameterType="java.util.List">
        INSERT INTO ieai_envc_run_rule (
            iid, ienvc_run_instance_info_id, imodel, itype, ipath, isource_path, iencode,
            iway, irule_type, ienabled, ichild_level, icreator_id, icreator_name,
            icreate_time, iend_time, iresult, istate, ielapsed_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.envcRunInstanceInfoId}, #{item.model}, #{item.type}, #{item.path}, #{item.sourcePath}, #{item.encode},
                #{item.way}, #{item.ruleType}, #{item.enabled}, #{item.childLevel}, #{item.creatorId}, #{item.creatorName},
                ${@com.ideal.common.util.DbUtils@getCurrentTime()}, #{item.endTime}, #{item.result}, #{item.state}, #{item.elapsedTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入节点规则同步 -->
    <insert id="batchInsertRunRuleSync" parameterType="java.util.List">
        INSERT INTO ieai_envc_run_rule_sync (
            iid, ienvc_run_rule_id, icreator_id, icreator_name,
            icreate_time, iend_time, iresult, istate, ielapsed_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.envcRunRuleId}, #{item.creatorId}, #{item.creatorName},
                ${@com.ideal.common.util.DbUtils@getCurrentTime()}, #{item.endTime}, #{item.result}, #{item.state}, #{item.elapsedTime}
            )
        </foreach>
    </insert>

    <!-- 批量插入节点规则内容 -->
    <insert id="batchInsertRunRuleContent" parameterType="java.util.List">
        INSERT INTO ieai_envc_run_rule_content (
            iid, ienvc_run_rule_id, icontent
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.envcRunRuleId}, #{item.content}
            )
        </foreach>
    </insert>

    <!-- 批量插入节点规则流程 -->
    <insert id="batchInsertRunFlow" parameterType="java.util.List">
        INSERT INTO ieai_envc_run_flow (
            iid, iflowid, irun_biz_id, imodel, icreator_id, icreator_name,
            icreate_time, iend_time, istate, ielapsed_time, iret, iupdate_order_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.flowid}, #{item.runBizId}, #{item.model}, #{item.creatorId}, #{item.creatorName},
                #{item.createTime}, #{item.endTime}, #{item.state}, #{item.elapsedTime}, #{item.ret}, #{item.updateOrderTime}
            )
        </foreach>
    </insert>

    <!-- 批量更新节点规则流程状态 -->
    <update id="batchUpdateRunFlowState" parameterType="java.util.Map">
        UPDATE ieai_envc_run_flow
        SET istate = #{state},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        WHERE iid IN
        <foreach collection="flowIds" item="flowId" open="(" separator="," close=")">
            #{flowId}
        </foreach>
    </update>

    <!-- 更新节点规则流程 -->
    <update id="updateRunFlow" parameterType="com.ideal.envc.model.entity.RunFlowEntity">
        UPDATE ieai_envc_run_flow
        SET istate = #{state},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
            iupdate_order_time = #{updateOrderTime}
        WHERE iid = #{id}
    </update>
</mapper>
