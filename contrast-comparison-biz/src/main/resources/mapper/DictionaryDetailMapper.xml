<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.DictionaryDetailMapper">

    <resultMap type="com.ideal.envc.model.entity.DictionaryDetailEntity" id="DictionaryDetailResult">
            <result property="id" column="iid"/>
            <result property="envcDictionaryId" column="ienvc_dictionary_id"/>
            <result property="code" column="icode"/>
            <result property="lable" column="ilable"/>
            <result property="value" column="ivalue"/>
            <result property="sort" column="isort"/>
            <result property="deleted" column="ideleted"/>
            <result property="arrayFlag" column="iarray_flag"/>
            <result property="valueType" column="ivalue_type"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectDictionaryDetail">
        select iid, ienvc_dictionary_id, icode, ilable, ivalue, isort, ideleted, iarray_flag, ivalue_type, icreator_name, icreator_id, icreate_time, iupdator_id, iupdator_name, iupdate_time
        from ieai_envc_dictionary_detail
    </sql>

    <select id="selectDictionaryDetailList" parameterType="com.ideal.envc.model.entity.DictionaryDetailEntity" resultMap="DictionaryDetailResult">
        <include refid="selectDictionaryDetail"/>
        <where>
                        <if test="envcDictionaryId != null ">
                            and ienvc_dictionary_id = #{envcDictionaryId}
                        </if>
                        <if test="code != null  and code != ''">
                            and icode = #{code}
                        </if>
                        <if test="lable != null  and lable != ''">
                            and ilable = #{lable}
                        </if>
                        <if test="value != null  and value != ''">
                            and ivalue = #{value}
                        </if>
                        <if test="sort != null ">
                            and isort = #{sort}
                        </if>
                        <if test="deleted != null ">
                            and ideleted = #{deleted}
                        </if>
                        <if test="arrayFlag != null ">
                            and iarray_flag = #{arrayFlag}
                        </if>
                        <if test="valueType != null  and valueType != ''">
                            and ivalue_type = #{valueType}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
                        <if test="updateTime != null ">
                            and iupdate_time = #{updateTime}
                        </if>
        </where>
    </select>

    <select id="selectDictionaryDetailById" parameterType="Long"
            resultMap="DictionaryDetailResult">
            <include refid="selectDictionaryDetail"/>
            where iid = #{id}
    </select>

    <insert id="insertDictionaryDetail" parameterType="com.ideal.envc.model.entity.DictionaryDetailEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_dictionary_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="envcDictionaryId != null">ienvc_dictionary_id,
                    </if>
                    <if test="code != null">icode,
                    </if>
                    <if test="lable != null">ilable,
                    </if>
                    <if test="value != null">ivalue,
                    </if>
                    <if test="sort != null">isort,
                    </if>
                    <if test="deleted != null">ideleted,
                    </if>
                    <if test="arrayFlag != null">iarray_flag,
                    </if>
                    <if test="valueType != null">ivalue_type,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    icreate_time,
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="envcDictionaryId != null">#{envcDictionaryId},
                    </if>
                    <if test="code != null">#{code},
                    </if>
                    <if test="lable != null">#{lable},
                    </if>
                    <if test="value != null">#{value},
                    </if>
                    <if test="sort != null">#{sort},
                    </if>
                    <if test="deleted != null">#{deleted},
                    </if>
                    <if test="arrayFlag != null">#{arrayFlag},
                    </if>
                    <if test="valueType != null">#{valueType},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateDictionaryDetail" parameterType="com.ideal.envc.model.entity.DictionaryDetailEntity">
        update ieai_envc_dictionary_detail
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcDictionaryId != null">ienvc_dictionary_id =
                        #{envcDictionaryId},
                    </if>
                    <if test="code != null">icode =
                        #{code},
                    </if>
                    <if test="lable != null">ilable =
                        #{lable},
                    </if>
                    <if test="value != null">ivalue =
                        #{value},
                    </if>
                    <if test="sort != null">isort =
                        #{sort},
                    </if>
                    <if test="deleted != null">ideleted =
                        #{deleted},
                    </if>
                    <if test="arrayFlag != null">iarray_flag =
                        #{arrayFlag},
                    </if>
                    <if test="valueType != null">ivalue_type =
                        #{valueType},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time =  ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteDictionaryDetailById" parameterType="Long">
        delete
        from ieai_envc_dictionary_detail where iid = #{id}
    </delete>

    <delete id="deleteDictionaryDetailByIds" parameterType="String">
        delete from ieai_envc_dictionary_detail where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectDictionaryDetailListByCode" parameterType="String" resultMap="DictionaryDetailResult">
        <include refid="selectDictionaryDetail"/>
        where icode = #{code}
        and ideleted = 0
        order by isort asc
    </select>
</mapper>