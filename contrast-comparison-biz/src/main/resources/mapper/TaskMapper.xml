<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.TaskMapper">

    <resultMap type="com.ideal.envc.model.entity.TaskEntity" id="TaskResult">
            <result property="id" column="iid"/>
            <result property="envcPlanId" column="ienvc_plan_id"/>
            <result property="cron" column="icron"/>
            <result property="enabled" column="ienabled"/>
            <result property="state" column="istate"/>
            <result property="sourceCenterId" column="isource_center_id"/>
            <result property="targetCenterId" column="itarget_center_id"/>
            <result property="scheduledId" column="ischeduled_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="createTime" column="icreate_time"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectTask">
        select iid, ienvc_plan_id, icron, ienabled, istate, isource_center_id, itarget_center_id, ischeduled_id,
        icreator_name, icreator_id, icreate_time, iupdator_id, iupdator_name, iupdate_time
        from ieai_envc_task
    </sql>

    <select id="selectTaskList" parameterType="com.ideal.envc.model.entity.TaskEntity" resultMap="TaskResult">
        <include refid="selectTask"/>
        <where>
                        <if test="envcPlanId != null ">
                            and ienvc_plan_id = #{envcPlanId}
                        </if>
                        <if test="cron != null  and cron != ''">
                            and icron = #{cron}
                        </if>
                        <if test="enabled != null ">
                            and ienabled = #{enabled}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="sourceCenterId != null">
                            and isource_center_id = #{sourceCenterId}
                        </if>
                        <if test="targetCenterId != null">
                            and itarget_center_id = #{targetCenterId}
                        </if>
                        <if test="scheduledId != null">
                            and ischeduled_id = #{scheduledId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="updatorId != null ">
                            and iupdator_id = #{updatorId}
                        </if>
                        <if test="updatorName != null  and updatorName != ''">
                            and iupdator_name like concat('%', #{updatorName}, '%')
                        </if>
                        <if test="updateTime != null ">
                            and iupdate_time = #{updateTime}
                        </if>
        </where>
    </select>

    <select id="selectTaskById" parameterType="Long"
            resultMap="TaskResult">
            <include refid="selectTask"/>
            where iid = #{id}
    </select>

    <select id="selectTaskByScheduleId" parameterType="Long"
            resultMap="TaskResult">
        <include refid="selectTask"/>
        where ischeduled_id = #{scheduledId}
    </select>

    <insert id="insertTask" parameterType="com.ideal.envc.model.entity.TaskEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="envcPlanId != null">ienvc_plan_id,</if>
                    <if test="cron != null">icron,</if>
                    <if test="enabled != null">ienabled,</if>
                    <if test="state != null">istate,</if>
                    <if test="sourceCenterId != null">isource_center_id,</if>
                    <if test="targetCenterId != null">itarget_center_id,</if>
                    <if test="scheduledId != null">ischeduled_id,</if>
                    <if test="creatorName != null">icreator_name,</if>
                    <if test="creatorId != null">icreator_id,</if>
                    icreate_time,
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="envcPlanId != null">#{envcPlanId},</if>
                    <if test="cron != null">#{cron},</if>
                    <if test="enabled != null">#{enabled},</if>
                    <if test="state != null">#{state},</if>
                    <if test="sourceCenterId != null">#{sourceCenterId},</if>
                    <if test="targetCenterId != null">#{targetCenterId},</if>
                    <if test="scheduledId != null">#{scheduledId},</if>
                    <if test="creatorName != null">#{creatorName},</if>
                    <if test="creatorId != null">#{creatorId},</if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateTask" parameterType="com.ideal.envc.model.entity.TaskEntity">
        update ieai_envc_task
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcPlanId != null">ienvc_plan_id = #{envcPlanId},</if>
                    <if test="cron != null">icron = #{cron},</if>
                    <if test="enabled != null">ienabled = #{enabled},</if>
                    <if test="state != null">istate = #{state},</if>
                    <if test="sourceCenterId != null">isource_center_id = #{sourceCenterId},</if>
                    <if test="targetCenterId != null">itarget_center_id = #{targetCenterId},</if>
                    <if test="scheduledId != null">ischeduled_id = #{scheduledId},</if>
                    <if test="updatorId != null">iupdator_id = #{updatorId},</if>
                    <if test="updatorName != null">iupdator_name = #{updatorName},</if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteTaskById" parameterType="Long">
        delete
        from ieai_envc_task where iid = #{id}
    </delete>

    <delete id="deleteTaskByIds" parameterType="String">
        delete from ieai_envc_task where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="checkTaskExists" resultType="int">
        select count(1) from ieai_envc_task where ienvc_plan_id = #{envcPlanId} and icron = #{cron}
    </select>

    <select id="checkTaskExistsExcludeId" resultType="int">
        select count(1) from ieai_envc_task where ienvc_plan_id = #{envcPlanId} and icron = #{cron} and iid != #{id}
    </select>

    <update id="updateTaskCron">
        update ieai_envc_task set icron = #{cron} where iid = #{id}
    </update>

    <!-- 查询任务方案列表 -->
    <resultMap id="TaskPlanListResult" type="com.ideal.envc.model.bean.TaskPlanListBean">
        <result property="id" column="iid" />
        <result property="envcPlanId" column="ienvc_plan_id" />
        <result property="planName" column="iname" />
        <result property="planDesc" column="idesc" />
        <result property="cron" column="icron" />
        <result property="status" column="istatus" />
        <result property="createTime" column="icreate_time" />
        <result property="creatorId" column="icreator_id" />
        <result property="creatorName" column="icreator_name" />
        <result property="updateTime" column="iupdate_time" />
        <result property="updatorId" column="iupdator_id" />
        <result property="updatorName" column="iupdator_name" />
        <result property="sourceCenterId" column="isource_center_id" />
        <result property="targetCenterId" column="itarget_center_id" />
        <result property="scheduledId" column="ischeduled_id" />
    </resultMap>

    <select id="selectTaskPlanList" parameterType="com.ideal.envc.model.entity.TaskEntity" resultMap="TaskPlanListResult">
        SELECT
            t.iid,
            t.ienvc_plan_id,
            p.iname,
            p.idesc,
            t.icron,
            t.istatus,
            t.icreate_time,
            t.icreator_id,
            t.icreator_name,
            t.iupdate_time,
            t.iupdator_id,
            t.iupdator_name,
            t.isource_center_id,
            t.itarget_center_id,
            t.ischeduled_id
        FROM
            ieai_envc_task t
        LEFT JOIN
            ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        <where>
            <if test="id != null">
                AND t.iid = #{id}
            </if>
            <if test="envcPlanId != null">
                AND t.ienvc_plan_id = #{envcPlanId}
            </if>
            <if test="cron != null and cron != ''">
                AND t.icron = #{cron}
            </if>
            <if test="status != null">
                AND t.istatus = #{status}
            </if>
            <if test="scheduledId != null">
                AND t.ischeduled_id = #{scheduledId}
            </if>
        </where>
        ORDER BY t.icreate_time DESC
    </select>

    <!-- 根据任务ID集合查询定时ID集合 -->
    <select id="selectScheduledIdsByTaskIds" parameterType="java.util.List" resultType="java.lang.Long">
        SELECT
            ischeduled_id
        FROM
            ieai_envc_task
        WHERE
            iid IN
            <foreach collection="list" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
            AND ischeduled_id IS NOT NULL
    </select>

    <!-- 根据任务ID集合查询任务ID和定时ID信息 -->
    <select id="selectTaskScheduledIdInfoByIds" parameterType="java.util.List" resultMap="TaskListResult">
        SELECT
            iid,
            ischeduled_id
        FROM
            ieai_envc_task
        WHERE
            iid IN
            <foreach collection="list" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
            AND ischeduled_id IS NOT NULL
    </select>

    <!-- 根据任务ID集合查询任务基本信息用于组装名称映射 -->
    <select id="selectTaskNameInfoByIds" parameterType="java.util.List" resultMap="TaskListResult">
        SELECT
            t.iid,
            t.ienvc_plan_id,
            t.icron,
            p.iname as plan_name
        FROM
            ieai_envc_task t
        LEFT JOIN
            ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        WHERE
            t.iid IN
            <foreach collection="list" item="taskId" open="(" separator="," close=")">
                #{taskId}
            </foreach>
    </select>

    <!-- 根据任务ID集合批量更新任务状态 -->
    <update id="updateTaskStateByIds">
        UPDATE ieai_envc_task
        SET istate = #{state}
        WHERE iid IN
        <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </update>

    <!-- 根据任务ID更新定时ID和状态 -->
    <update id="updateTaskScheduledIdAndState">
        UPDATE ieai_envc_task
        SET ischeduled_id = #{scheduledId},
            istate = #{state},
            iupdate_time = NOW()
        WHERE iid = #{taskId}
    </update>

    <resultMap id="TaskListResult" type="com.ideal.envc.model.bean.TaskListBean">
        <result property="id" column="iid"/>
        <result property="envcPlanId" column="ienvc_plan_id"/>
        <result property="cron" column="icron"/>
        <result property="state" column="istate"/>
        <result property="enabled" column="ienabled"/>
        <result property="scheduledId" column="ischeduled_id"/>
        <result property="sourceCenterId" column="isource_center_id"/>
        <result property="targetCenterId" column="itarget_center_id"/>
        <result property="creatorId" column="icreator_id"/>
        <result property="creatorName" column="icreator_name"/>
        <result property="createTime" column="icreate_time"/>
        <result property="updatorId" column="iupdator_id"/>
        <result property="updatorName" column="iupdator_name"/>
        <result property="updateTime" column="iupdate_time"/>
        <result property="planName" column="plan_name"/>
        <result property="planDesc" column="iplan_desc"/>
    </resultMap>

    <select id="selectTaskListWithPlan" parameterType="com.ideal.envc.model.bean.TaskQueryBean" resultMap="TaskListResult">
        SELECT 
            t.iid,
            t.ienvc_plan_id,
            p.iname as plan_name,
            p.iplan_desc,
            t.icron,
            t.ienabled,
            t.istate,
            t.isource_center_id,
            t.itarget_center_id,
            t.ischeduled_id,
            t.icreator_id,
            t.icreator_name,
            t.icreate_time,
            t.iupdator_id,
            t.iupdator_name,
            t.iupdate_time
        FROM ieai_envc_task t
        LEFT JOIN ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        <where>
            <if test="id != null">
                AND t.iid = #{id}
            </if>
            <if test="envcPlanId != null">
                AND t.ienvc_plan_id = #{envcPlanId}
            </if>
            <if test="planName != null and planName != ''">
                AND p.iname LIKE CONCAT('%', #{planName}, '%')
            </if>
            <if test="cron != null and cron != ''">
                AND t.icron = #{cron}
            </if>
            <if test="enabled != null">
                AND t.ienabled = #{enabled}
            </if>
            <if test="state != null">
                AND t.istate = #{state}
            </if>
            <if test="sourceCenterId != null">
                AND t.isource_center_id = #{sourceCenterId}
            </if>
            <if test="targetCenterId != null">
                AND t.itarget_center_id = #{targetCenterId}
            </if>
            <if test="scheduledId != null">
                AND t.ischeduled_id = #{scheduledId}
            </if>
        </where>
        ORDER BY t.icreate_time DESC
    </select>

    <!-- 根据任务ID集合查询任务信息 -->
    <select id="selectTaskByIds" parameterType="java.util.List" resultMap="TaskResult">
        <include refid="selectTask"/>
        where iid in
        <foreach collection="list" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <!-- 根据任务ID列表查询任务信息（包含方案名称） -->
    <select id="selectTaskListByIds" resultMap="TaskListResult">
        SELECT
            t.iid,
            t.ienvc_plan_id,
            t.icron,
            t.istate,
            t.ienabled,
            t.ischeduled_id,
            t.isource_center_id,
            t.itarget_center_id,
            t.icreator_id,
            t.icreator_name,
            t.icreate_time,
            t.iupdator_id,
            t.iupdator_name,
            t.iupdate_time,
            p.iname as plan_name
        FROM ieai_envc_task t
        LEFT JOIN ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        WHERE t.iid IN
        <foreach collection="list" item="taskId" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </select>

    <!-- 查询任务详情（包含方案名称） -->
    <select id="selectTaskDetailById" resultMap="TaskListResult">
        SELECT
            t.iid,
            t.ienvc_plan_id,
            t.icron,
            t.istate,
            t.ienabled,
            t.ischeduled_id,
            t.isource_center_id,
            t.itarget_center_id,
            t.icreator_id,
            t.icreator_name,
            t.icreate_time,
            t.iupdator_id,
            t.iupdator_name,
            t.iupdate_time,
            p.iname as plan_name
        FROM ieai_envc_task t
        LEFT JOIN ieai_envc_plan p ON t.ienvc_plan_id = p.iid
        WHERE t.iid = #{id}
    </select>
</mapper>