<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunRuleSyncMapper">

    <resultMap type="com.ideal.envc.model.entity.RunRuleSyncEntity" id="RunRuleSyncResult">
            <result property="id" column="iid"/>
            <result property="envcRunRuleId" column="ienvc_run_rule_id"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="endTime" column="iend_time"/>
            <result property="result" column="iresult"/>
            <result property="state" column="istate"/>
            <result property="elapsedTime" column="ielapsed_time"/>
    </resultMap>

    <sql id="selectRunRuleSync">
        select iid, ienvc_run_rule_id, icreator_id, icreator_name, icreate_time, iend_time, iresult, istate, ielapsed_time
        from ieai_envc_run_rule_sync
    </sql>

    <select id="selectRunRuleSyncList" parameterType="com.ideal.envc.model.entity.RunRuleSyncEntity" resultMap="RunRuleSyncResult">
        <include refid="selectRunRuleSync"/>
        <where>
                        <if test="envcRunRuleId != null ">
                            and ienvc_run_rule_id = #{envcRunRuleId}
                        </if>
                        <if test="creatorId != null ">
                            and icreator_id = #{creatorId}
                        </if>
                        <if test="creatorName != null  and creatorName != ''">
                            and icreator_name like concat('%', #{creatorName}, '%')
                        </if>
                        <if test="createTime != null ">
                            and icreate_time = #{createTime}
                        </if>
                        <if test="endTime != null ">
                            and iend_time = #{endTime}
                        </if>
                        <if test="result != null ">
                            and iresult = #{result}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
                        <if test="elapsedTime != null ">
                            and ielapsed_time = #{elapsedTime}
                        </if>
        </where>
    </select>

    <select id="selectRunRuleSyncById" parameterType="Long"
            resultMap="RunRuleSyncResult">
            <include refid="selectRunRuleSync"/>
            where iid = #{id}
    </select>

    <insert id="insertRunRuleSync" parameterType="com.ideal.envc.model.entity.RunRuleSyncEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_run_rule_sync
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="envcRunRuleId != null">ienvc_run_rule_id,</if>
                    <if test="creatorId != null">icreator_id,</if>
                    <if test="creatorName != null">icreator_name,</if>
                    icreate_time,
                    <if test="endTime != null">iend_time,</if>
                    <if test="result != null">iresult,</if>
                    <if test="state != null">istate,</if>
                    <if test="elapsedTime != null">ielapsed_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="envcRunRuleId != null">#{envcRunRuleId},</if>
                    <if test="creatorId != null">#{creatorId},</if>
                    <if test="creatorName != null">#{creatorName},</if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="endTime != null">#{endTime},</if>
                    <if test="result != null">#{result},</if>
                    <if test="state != null">#{state},</if>
                    <if test="elapsedTime != null">#{elapsedTime},</if>
        </trim>
    </insert>

    <update id="updateRunRuleSync" parameterType="com.ideal.envc.model.entity.RunRuleSyncEntity">
        update ieai_envc_run_rule_sync
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcRunRuleId != null">ienvc_run_rule_id = #{envcRunRuleId},</if>
                    <if test="endTime != null">iend_time = #{endTime},</if>
                    <if test="result != null">iresult = #{result},</if>
                    <if test="state != null">istate = #{state},</if>
                    <if test="elapsedTime != null">ielapsed_time = #{elapsedTime},</if>
                    <if test="updatorId != null">iupdator_id = #{updatorId},</if>
                    <if test="updatorName != null">iupdator_name = #{updatorName},</if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteRunRuleSyncById" parameterType="Long">
        delete
        from ieai_envc_run_rule_sync where iid = #{id}
    </delete>

    <delete id="deleteRunRuleSyncByIds" parameterType="String">
        delete from ieai_envc_run_rule_sync where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>