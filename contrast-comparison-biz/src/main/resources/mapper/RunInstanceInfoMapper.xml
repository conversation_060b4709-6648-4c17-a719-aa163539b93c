<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunInstanceInfoMapper">

    <resultMap type="com.ideal.envc.model.entity.RunInstanceInfoEntity" id="RunInstanceInfoResult">
            <result property="id" column="iid"/>
            <result property="envcRunInstanceId" column="ienvc_run_instance_id"/>
            <result property="envcPlanId" column="ienvc_plan_id"/>
            <result property="businessSystemId" column="ibusiness_system_id"/>
            <result property="sourceCenterId" column="isource_center_id"/>
            <result property="sourceCenterName" column="isource_center_name"/>
            <result property="targetCenterId" column="itarget_center_id"/>
            <result property="targetCenterName" column="itarget_center_name"/>
            <result property="sourceComputerId" column="isource_computer_id"/>
            <result property="sourceComputerIp" column="isource_computer_ip"/>
            <result property="sourceComputerPort" column="isource_computer_port"/>
            <result property="sourceComputerOs" column="isource_computer_os"/>
            <result property="targetComputerId" column="itarget_computer_id"/>
            <result property="targetComputerIp" column="itarget_computer_ip"/>
            <result property="targetComputerPort" column="itarget_computer_port"/>
            <result property="targetComputerOs" column="itarget_computer_os"/>
            <result property="storeTime" column="istore_time"/>
            <result property="result" column="iresult"/>
            <result property="state" column="istate"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectRunInstanceInfo">
        select iid, ienvc_run_instance_id, ienvc_plan_id, ibusiness_system_id, isource_center_id, isource_center_name, itarget_center_id, itarget_center_name, isource_computer_id, isource_computer_ip, isource_computer_port, isource_computer_os, itarget_computer_id, itarget_computer_ip, itarget_computer_port, itarget_computer_os, istore_time, iresult, istate, iupdate_time
        from ieai_envc_run_instance_info
    </sql>

    <select id="selectRunInstanceInfoList" parameterType="com.ideal.envc.model.entity.RunInstanceInfoEntity" resultMap="RunInstanceInfoResult">
        <include refid="selectRunInstanceInfo"/>
        <where>
                        <if test="envcRunInstanceId != null ">
                            and ienvc_run_instance_id = #{envcRunInstanceId}
                        </if>
                        <if test="envcPlanId != null ">
                            and ienvc_plan_id = #{envcPlanId}
                        </if>
                        <if test="businessSystemId != null ">
                            and ibusiness_system_id = #{businessSystemId}
                        </if>
                        <if test="sourceCenterId != null ">
                            and isource_center_id = #{sourceCenterId}
                        </if>
                        <if test="sourceCenterName != null and sourceCenterName != ''">
                            and isource_center_name like concat('%', #{sourceCenterName}, '%')
                        </if>
                        <if test="targetCenterId != null ">
                            and itarget_center_id = #{targetCenterId}
                        </if>
                        <if test="targetCenterName != null and targetCenterName != ''">
                            and itarget_center_name like concat('%', #{targetCenterName}, '%')
                        </if>
                        <if test="sourceComputerId != null ">
                            and isource_computer_id = #{sourceComputerId}
                        </if>
                        <if test="sourceComputerIp != null  and sourceComputerIp != ''">
                            and isource_computer_ip = #{sourceComputerIp}
                        </if>
                        <if test="sourceComputerPort != null ">
                            and isource_computer_port = #{sourceComputerPort}
                        </if>
                        <if test="sourceComputerOs != null and sourceComputerOs != ''">
                            and isource_computer_os = #{sourceComputerOs}
                        </if>
                        <if test="targetComputerId != null ">
                            and itarget_computer_id = #{targetComputerId}
                        </if>
                        <if test="targetComputerIp != null  and targetComputerIp != ''">
                            and itarget_computer_ip = #{targetComputerIp}
                        </if>
                        <if test="targetComputerPort != null ">
                            and itarget_computer_port = #{targetComputerPort}
                        </if>
                        <if test="targetComputerOs != null and targetComputerOs != ''">
                            and itarget_computer_os = #{targetComputerOs}
                        </if>
                        <if test="storeTime != null ">
                            and istore_time = #{storeTime}
                        </if>
                        <if test="result != null ">
                            and iresult = #{result}
                        </if>
                        <if test="state != null ">
                            and istate = #{state}
                        </if>
        </where>
    </select>

    <select id="selectRunInstanceInfoById" parameterType="Long"
            resultMap="RunInstanceInfoResult">
            <include refid="selectRunInstanceInfo"/>
            where iid = #{id}
    </select>

    <insert id="insertRunInstanceInfo" parameterType="com.ideal.envc.model.entity.RunInstanceInfoEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_run_instance_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="envcRunInstanceId != null">ienvc_run_instance_id,</if>
                    <if test="envcPlanId != null">ienvc_plan_id,</if>
                    <if test="businessSystemId != null">ibusiness_system_id,</if>
                    <if test="sourceCenterId != null">isource_center_id,</if>
                    <if test="sourceCenterName != null">isource_center_name,</if>
                    <if test="targetCenterId != null">itarget_center_id,</if>
                    <if test="targetCenterName != null">itarget_center_name,</if>
                    <if test="sourceComputerId != null">isource_computer_id,</if>
                    <if test="sourceComputerIp != null">isource_computer_ip,</if>
                    <if test="sourceComputerPort != null">isource_computer_port,</if>
                    <if test="sourceComputerOs != null">isource_computer_os,</if>
                    <if test="targetComputerId != null">itarget_computer_id,</if>
                    <if test="targetComputerIp != null">itarget_computer_ip,</if>
                    <if test="targetComputerPort != null">itarget_computer_port,</if>
                    <if test="targetComputerOs != null">itarget_computer_os,</if>
                    istore_time,
                    <if test="result != null">iresult,</if>
                    <if test="state != null">istate,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="envcRunInstanceId != null">#{envcRunInstanceId},</if>
                    <if test="envcPlanId != null">#{envcPlanId},</if>
                    <if test="businessSystemId != null">#{businessSystemId},</if>
                    <if test="sourceCenterId != null">#{sourceCenterId},</if>
                    <if test="sourceCenterName != null">#{sourceCenterName},</if>
                    <if test="targetCenterId != null">#{targetCenterId},</if>
                    <if test="targetCenterName != null">#{targetCenterName},</if>
                    <if test="sourceComputerId != null">#{sourceComputerId},</if>
                    <if test="sourceComputerIp != null">#{sourceComputerIp},</if>
                    <if test="sourceComputerPort != null">#{sourceComputerPort},</if>
                    <if test="sourceComputerOs != null">#{sourceComputerOs},</if>
                    <if test="targetComputerId != null">#{targetComputerId},</if>
                    <if test="targetComputerIp != null">#{targetComputerIp},</if>
                    <if test="targetComputerPort != null">#{targetComputerPort},</if>
                    <if test="targetComputerOs != null">#{targetComputerOs},</if>
                    ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="result != null">#{result},</if>
                    <if test="state != null">#{state},</if>
        </trim>
    </insert>

    <update id="updateRunInstanceInfo" parameterType="com.ideal.envc.model.entity.RunInstanceInfoEntity">
        update ieai_envc_run_instance_info
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcRunInstanceId != null">ienvc_run_instance_id = #{envcRunInstanceId},</if>
                    <if test="envcPlanId != null">ienvc_plan_id = #{envcPlanId},</if>
                    <if test="businessSystemId != null">ibusiness_system_id = #{businessSystemId},</if>
                    <if test="sourceCenterId != null">isource_center_id = #{sourceCenterId},</if>
                    <if test="sourceCenterName != null">isource_center_name = #{sourceCenterName},</if>
                    <if test="targetCenterId != null">itarget_center_id = #{targetCenterId},</if>
                    <if test="targetCenterName != null">itarget_center_name = #{targetCenterName},</if>
                    <if test="sourceComputerId != null">isource_computer_id = #{sourceComputerId},</if>
                    <if test="sourceComputerIp != null">isource_computer_ip = #{sourceComputerIp},</if>
                    <if test="sourceComputerPort != null">isource_computer_port = #{sourceComputerPort},</if>
                    <if test="sourceComputerOs != null">isource_computer_os = #{sourceComputerOs},</if>
                    <if test="targetComputerId != null">itarget_computer_id = #{targetComputerId},</if>
                    <if test="targetComputerIp != null">itarget_computer_ip = #{targetComputerIp},</if>
                    <if test="targetComputerPort != null">itarget_computer_port = #{targetComputerPort},</if>
                    <if test="targetComputerOs != null">itarget_computer_os = #{targetComputerOs},</if>
                    <if test="storeTime != null">istore_time = #{storeTime},</if>
                    <if test="result != null">iresult = #{result},</if>
                    <if test="state != null">istate = #{state},</if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteRunInstanceInfoById" parameterType="Long">
        delete
        from ieai_envc_run_instance_info where iid = #{id}
    </delete>

    <delete id="deleteRunInstanceInfoByIds" parameterType="String">
        delete from ieai_envc_run_instance_info where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 根据运行实例ID查询关联的运行实例信息列表 -->
    <select id="selectInstanceInfosByInstanceId" parameterType="Long" resultMap="RunInstanceInfoResult">
        <include refid="selectRunInstanceInfo"/>
        WHERE ienvc_run_instance_id = #{instanceId}
    </select>

    <!-- 根据ID和时间戳更新运行实例信息的状态和结果 -->
    <update id="updateStateAndResultByIdAndTimestamp">
        UPDATE ieai_envc_run_instance_info
        SET istate = #{state},
            iresult = #{result},
            iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()}
        WHERE iid = #{id}
    </update>

</mapper>