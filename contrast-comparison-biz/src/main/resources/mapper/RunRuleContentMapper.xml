<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.RunRuleContentMapper">

    <resultMap type="com.ideal.envc.model.entity.RunRuleContentEntity" id="RunRuleContentResult">
        <id property="id" column="iid"/>
        <result property="envcRunRuleId" column="ienvc_run_rule_id"/>
        <result property="content" column="icontent"/>
    </resultMap>

    <sql id="selectRunRuleContentVo">
        select iid, ienvc_run_rule_id, icontent
        from ieai_envc_run_rule_content
    </sql>

    <select id="selectRunRuleContentById" parameterType="java.lang.Long" resultMap="RunRuleContentResult">
        <include refid="selectRunRuleContentVo"/>
        where iid = #{id}
    </select>

    <select id="selectRunRuleContentList" parameterType="com.ideal.envc.model.entity.RunRuleContentEntity" resultMap="RunRuleContentResult">
        <include refid="selectRunRuleContentVo"/>
        <where>
            <if test="id != null">
                and iid = #{id}
            </if>
            <if test="envcRunRuleId != null">
                and ienvc_run_rule_id = #{envcRunRuleId}
            </if>
        </where>
    </select>

    <select id="selectRunRuleContentListByRuleId" parameterType="java.lang.Long" resultMap="RunRuleContentResult">
        <include refid="selectRunRuleContentVo"/>
        where ienvc_run_rule_id = #{envcRunRuleId}
    </select>

    <insert id="insertRunRuleContent" parameterType="com.ideal.envc.model.entity.RunRuleContentEntity" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_envc_run_rule_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">iid,</if>
            <if test="envcRunRuleId != null">ienvc_run_rule_id,</if>
            <if test="content != null">icontent,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="envcRunRuleId != null">#{envcRunRuleId},</if>
            <if test="content != null">#{content},</if>
        </trim>
    </insert>

    <update id="updateRunRuleContent" parameterType="com.ideal.envc.model.entity.RunRuleContentEntity">
        update ieai_envc_run_rule_content
        <trim prefix="SET" suffixOverrides=",">
            <if test="envcRunRuleId != null">ienvc_run_rule_id = #{envcRunRuleId},</if>
            <if test="content != null">icontent = #{content},</if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteRunRuleContentById" parameterType="java.lang.Long">
        delete from ieai_envc_run_rule_content where iid = #{id}
    </delete>

    <delete id="deleteRunRuleContentByIds" parameterType="java.lang.Long">
        delete from ieai_envc_run_rule_content where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRunRuleContentByRuleId" parameterType="java.lang.Long">
        delete from ieai_envc_run_rule_content where ienvc_run_rule_id = #{envcRunRuleId}
    </delete>
</mapper>
