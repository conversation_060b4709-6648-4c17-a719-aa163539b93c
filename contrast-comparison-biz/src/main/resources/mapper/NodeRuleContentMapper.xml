<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.NodeRuleContentMapper">

    <resultMap type="com.ideal.envc.model.entity.NodeRuleContentEntity" id="NodeRuleContentResult">
            <result property="id" column="iid"/>
            <result property="envcNodeRelationId" column="ienvc_node_relation_id"/>
            <result property="ruleContent" column="irule_content"/>
    </resultMap>

    <sql id="selectNodeRuleContent">
        select iid, ienvc_node_relation_id, irule_content
        from ieai_envc_node_rule_content
    </sql>

    <select id="selectNodeRuleContentList" parameterType="com.ideal.envc.model.entity.NodeRuleContentEntity" resultMap="NodeRuleContentResult">
        <include refid="selectNodeRuleContent"/>
        <where>
                        <if test="envcNodeRelationId != null ">
                            and ienvc_node_relation_id = #{envcNodeRelationId}
                        </if>
                        <if test="ruleContent != null  and ruleContent != ''">
                            and irule_content = #{ruleContent}
                        </if>
        </where>
    </select>

    <select id="selectNodeRuleContentById" parameterType="Long"
            resultMap="NodeRuleContentResult">
            <include refid="selectNodeRuleContent"/>
            where iid = #{id}
    </select>

    <insert id="insertNodeRuleContent" parameterType="com.ideal.envc.model.entity.NodeRuleContentEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_node_rule_content
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,</if>
                    <if test="envcNodeRelationId != null">ienvc_node_relation_id,</if>
                    <if test="ruleContent != null">irule_content,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},</if>
                    <if test="envcNodeRelationId != null">#{envcNodeRelationId},</if>
                    <if test="ruleContent != null">#{ruleContent},</if>
        </trim>
    </insert>

    <update id="updateNodeRuleContent" parameterType="com.ideal.envc.model.entity.NodeRuleContentEntity">
        update ieai_envc_node_rule_content
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcNodeRelationId != null">ienvc_node_relation_id = #{envcNodeRelationId},</if>
                    <if test="ruleContent != null">irule_content = #{ruleContent},</if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteNodeRuleContentById" parameterType="Long">
        delete
        from ieai_envc_node_rule_content where iid = #{id}
    </delete>

    <delete id="deleteNodeRuleContentByIds" >
        delete from ieai_envc_node_rule_content where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteNodeRuleContentByNodeRelationIds">
        delete from ieai_envc_node_rule_content where ienvc_node_relation_id in
        <foreach item="envcNodeRelationId" collection="array" open="(" separator="," close=")">
            #{envcNodeRelationId}
        </foreach>
    </delete>

    <delete id="deleteNodeRuleContentBySystemComputerNodeIds">
        delete from ieai_envc_node_rule_content
        where ienvc_node_relation_id in (
            select iid from ieai_envc_node_relation
            where ienvc_system_computer_node_id in
            <foreach item="systemComputerNodeId" collection="array" open="(" separator="," close=")">
                #{systemComputerNodeId}
            </foreach>
        )
    </delete>
</mapper>