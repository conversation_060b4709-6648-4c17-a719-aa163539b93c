<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.PlanRelationMapper">

    <resultMap type="com.ideal.envc.model.entity.PlanRelationEntity" id="PlanRelationResult">
            <result property="id" column="iid"/>
            <result property="envcPlanId" column="ienvc_plan_id"/>
            <result property="businessSystemId" column="ibusiness_system_id"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
    </resultMap>

    <sql id="selectPlanRelation">
        select iid, ienvc_plan_id, ibusiness_system_id, icreator_id, icreator_name, icreate_time
        from ieai_envc_plan_relation
    </sql>

    <resultMap id="PlanRelationListResult" type="com.ideal.envc.model.bean.PlanRelationListBean">
        <result property="id" column="iid"/>
        <result property="envcPlanId" column="ienvc_plan_id"/>
        <result property="businessSystemId" column="ibusiness_system_id"/>
        <result property="businessSystemName" column="ibusiness_system_name"/>
        <result property="createTime" column="icreate_time"/>
    </resultMap>

    <select id="selectPlanRelationList" parameterType="com.ideal.envc.model.dto.PlanRelationQueryDto" resultMap="PlanRelationListResult">
        SELECT DISTINCT
            r.iid,
            r.ienvc_plan_id,
            r.ibusiness_system_id,
            p.ibusiness_system_name,
            r.icreate_time
        FROM ieai_envc_plan_relation r
        LEFT JOIN ieai_envc_project p ON r.ibusiness_system_id = p.ibusiness_system_id
        <where>
            <if test="envcPlanId != null">
                AND r.ienvc_plan_id = #{envcPlanId}
            </if>
            <if test="businessSystemId != null">
                AND r.ibusiness_system_id = #{businessSystemId}
            </if>
            <if test="businessSystemName != null and businessSystemName != ''">
                AND p.ibusiness_system_name LIKE CONCAT('%', #{businessSystemName}, '%')
            </if>
        </where>
        ORDER BY r.icreate_time DESC
    </select>

    <select id="selectPlanRelationById" parameterType="Long"
            resultMap="PlanRelationResult">
            <include refid="selectPlanRelation"/>
            where iid = #{id}
    </select>

    <insert id="insertPlanRelation" parameterType="com.ideal.envc.model.entity.PlanRelationEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_plan_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="envcPlanId != null">ienvc_plan_id,
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="envcPlanId != null">#{envcPlanId},
                    </if>
                    <if test="businessSystemId != null">#{businessSystemId},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updatePlanRelation" parameterType="com.ideal.envc.model.entity.PlanRelationEntity">
        update ieai_envc_plan_relation
        <trim prefix="SET" suffixOverrides=",">
                    <if test="envcPlanId != null">ienvc_plan_id =
                        #{envcPlanId},
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id =
                        #{businessSystemId},
                    </if>
                    <if test="creatorId != null">icreator_id =
                        #{creatorId},
                    </if>
                    <if test="creatorName != null">icreator_name =
                        #{creatorName},
                    </if>
                    <if test="createTime != null">icreate_time =
                        #{createTime},
                    </if>
        </trim>
        where iid = #{id}
    </update>

    <delete id="deletePlanRelationById" parameterType="Long">
        delete
        from ieai_envc_plan_relation where iid = #{id}
    </delete>

    <delete id="deletePlanRelationByIds" parameterType="String">
        delete from ieai_envc_plan_relation where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deletePlanRelationByBusinessSystemIds" parameterType="java.util.List">
        delete from ieai_envc_plan_relation
        where ibusiness_system_id in
        <foreach collection="businessSystemIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询方案绑定系统列表 -->
    <resultMap id="PlanSystemListResult" type="com.ideal.envc.model.bean.PlanSystemListBean">
        <result property="id" column="iid" />
        <result property="planId" column="ienvc_plan_id" />
        <result property="businessSystemId" column="ibusiness_system_id" />
        <result property="businessSystemName" column="ibusiness_system_name" />
        <result property="businessSystemDesc" column="ibusiness_system_desc" />
        <result property="createTime" column="icreate_time" />
        <result property="creatorName" column="icreator_name" />
        <result property="businessSystemCode" column="ibusiness_system_code" />
    </resultMap>

    <select id="selectPlanSystemList" resultMap="PlanSystemListResult">
        SELECT
            r.iid,
            r.ienvc_plan_id,
            p.ibusiness_system_id,
            p.ibusiness_system_name,
            p.ibusiness_system_desc,
            p.ibusiness_system_code,
            r.icreate_time,
            r.icreator_name
        FROM
            ieai_envc_plan_relation r
        INNER JOIN
            ieai_envc_project p ON r.ibusiness_system_id = p.ibusiness_system_id
        WHERE
            r.ienvc_plan_id = #{planId}
        <if test="businessSystemName != null and businessSystemName != ''">
            AND p.ibusiness_system_name LIKE CONCAT('%', #{businessSystemName}, '%')
        </if>
        ORDER BY r.icreate_time DESC
    </select>

    <select id="selectPlanRelationByPlanAndSystem" resultMap="PlanRelationResult">
        select iid, 
               ienvc_plan_id, 
               ibusiness_system_id, 
               icreator_id, 
               icreator_name, 
               icreate_time
        from ieai_envc_plan_relation
        where ienvc_plan_id = #{envcPlanId}
        and ibusiness_system_id = #{businessSystemId}
    </select>

    <!-- 批量新增方案与系统关系 -->
    <insert id="batchInsertPlanRelation">
        insert into ieai_envc_plan_relation (
            ienvc_plan_id, ibusiness_system_id, icreator_id, icreator_name, icreate_time
        ) values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.envcPlanId}, #{item.businessSystemId}, 
                #{user.id}, #{user.name}, ${@com.ideal.common.util.DbUtils@getCurrentTime()}
            )
        </foreach>
    </insert>

    <!-- 根据方案ID查询已绑定的系统ID列表 -->
    <select id="selectBoundSystemIdsByPlanId" resultType="java.lang.Long">
        SELECT ibusiness_system_id
        FROM ieai_envc_plan_relation
        WHERE ienvc_plan_id = #{planId}
    </select>

    <!-- 查询可绑定的系统列表 -->
    <select id="selectAvailablePlanSystemList" resultMap="PlanSystemListResult">
        SELECT 
            #{planId} as ienvc_plan_id,
            p.ibusiness_system_id,
            p.ibusiness_system_name,
            p.ibusiness_system_desc,
            p.ibusiness_system_code,
            p.icreate_time,
            p.icreator_name
        FROM ieai_envc_project p
        WHERE 1=1
        <if test="boundSystemIds != null and boundSystemIds.size() > 0">
            AND p.ibusiness_system_id NOT IN
            <foreach collection="boundSystemIds" item="systemId" open="(" separator="," close=")">
                #{systemId}
            </foreach>
        </if>
        <if test="businessSystemName != null and businessSystemName != ''">
            AND p.ibusiness_system_name LIKE CONCAT('%', #{businessSystemName}, '%')
        </if>
        ORDER BY p.ibusiness_system_name
    </select>
</mapper>