<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.envc.mapper.ProjectMapper">

    <resultMap type="com.ideal.envc.model.entity.ProjectEntity" id="ProjectResult">
            <result property="id" column="iid"/>
            <result property="businessSystemId" column="ibusiness_system_id"/>
            <result property="businessSystemCode" column="ibusiness_system_code"/>
            <result property="businessSystemName" column="ibusiness_system_name"/>
            <result property="businessSystemUnique" column="ibusiness_system_unique"/>
            <result property="businessSystemDesc" column="ibusiness_system_desc"/>
            <result property="creatorId" column="icreator_id"/>
            <result property="creatorName" column="icreator_name"/>
            <result property="createTime" column="icreate_time"/>
            <result property="status" column="istatus"/>
            <result property="updatorId" column="iupdator_id"/>
            <result property="updatorName" column="iupdator_name"/>
            <result property="updateTime" column="iupdate_time"/>
    </resultMap>

    <sql id="selectProject">
        select iid, ibusiness_system_id, ibusiness_system_code, ibusiness_system_name, ibusiness_system_unique, ibusiness_system_desc, icreator_id, icreator_name, icreate_time, istatus, iupdator_id, iupdator_name, iupdate_time
        from ieai_envc_project
    </sql>

    <select id="selectProjectList" parameterType="com.ideal.envc.model.entity.ProjectEntity" resultMap="ProjectResult">
        <include refid="selectProject"/>
        <where>
                    <if test="project.businessSystemId != null ">
                        and ibusiness_system_id = #{project.businessSystemId}
                    </if>
                    <if test="project.businessSystemCode != null  and project.businessSystemCode != ''">
                        and ibusiness_system_code = #{project.businessSystemCode}
                    </if>
                    <if test="project.businessSystemName != null  and project.businessSystemName != ''">
                        and ibusiness_system_name like concat('%', #{project.businessSystemName}, '%')
                    </if>
                    <if test="project.businessSystemUnique != null  and project.businessSystemUnique != ''">
                        and ibusiness_system_unique = #{project.businessSystemUnique}
                    </if>
                    <if test="project.businessSystemDesc != null  and project.businessSystemDesc != ''">
                        and ibusiness_system_desc = #{project.businessSystemDesc}
                    </if>
                    <if test="project.creatorId != null ">
                        and icreator_id = #{project.creatorId}
                    </if>
                    <if test="project.creatorName != null  and project.creatorName != ''">
                        and icreator_name like concat('%', #{project.creatorName}, '%')
                    </if>
                    <if test="project.createTime != null ">
                        and icreate_time = #{project.createTime}
                    </if>
                    <if test="project.status != null ">
                        and istatus = #{project.status}
                    </if>
                    <if test="project.updatorId != null ">
                        and iupdator_id = #{project.updatorId}
                    </if>
                    <if test="project.updatorName != null  and project.updatorName != ''">
                        and iupdator_name like concat('%', #{project.updatorName}, '%')
                    </if>
                    <if test="project.updateTime != null ">
                        and iupdate_time = #{project.updateTime}
                    </if>
                    <!-- 处理新增的 List<Long> 类型参数 -->
                    <if test="businessSystemIdList != null and businessSystemIdList.size > 0">
                        AND (
                        <foreach collection="businessSystemIdList" index="index" item="item" separator=" OR ">
                            <if test="index % 999 == 0">
                                ibusiness_system_id IN
                                <foreach collection="businessSystemIdList" item="businessSystemIdItem" index="businessSystemIdIndex" open="(" separator="," close=")">
                                    <if test="businessSystemIdIndex >= index and businessSystemIdIndex &lt; index + 999">
                                        #{businessSystemIdItem}
                                    </if>
                                </foreach>
                            </if>
                        </foreach>
                        )
                    </if>
        </where>
    </select>

    <select id="selectProjectById" parameterType="Long"
            resultMap="ProjectResult">
            <include refid="selectProject"/>
            where iid = #{id}
    </select>

    <insert id="insertProject" parameterType="com.ideal.envc.model.entity.ProjectEntity" useGeneratedKeys="true"
            keyProperty="id">
        insert into ieai_envc_project
        <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="id != null">iid,
                    </if>
                    <if test="businessSystemId != null">ibusiness_system_id,
                    </if>
                    <if test="businessSystemCode != null">ibusiness_system_code,
                    </if>
                    <if test="businessSystemName != null">ibusiness_system_name,
                    </if>
                    <if test="businessSystemUnique != null">ibusiness_system_unique,
                    </if>
                    <if test="businessSystemDesc != null">ibusiness_system_desc,
                    </if>
                    <if test="creatorId != null">icreator_id,
                    </if>
                    <if test="creatorName != null">icreator_name,
                    </if>
                    icreate_time,
                    <if test="status != null">istatus,
                    </if>
                    <if test="updatorId != null">iupdator_id,
                    </if>
                    <if test="updatorName != null">iupdator_name,
                    </if>
                    iupdate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                    <if test="id != null">#{id},
                    </if>
                    <if test="businessSystemId != null">#{businessSystemId},
                    </if>
                    <if test="businessSystemCode != null">#{businessSystemCode},
                    </if>
                    <if test="businessSystemName != null">#{businessSystemName},
                    </if>
                    <if test="businessSystemUnique != null">#{businessSystemUnique},
                    </if>
                    <if test="businessSystemDesc != null">#{businessSystemDesc},
                    </if>
                    <if test="creatorId != null">#{creatorId},
                    </if>
                    <if test="creatorName != null">#{creatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
                    <if test="status != null">#{status},
                    </if>
                    <if test="updatorId != null">#{updatorId},
                    </if>
                    <if test="updatorName != null">#{updatorName},
                    </if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <update id="updateProject" parameterType="com.ideal.envc.model.entity.ProjectEntity">
        update ieai_envc_project
        <trim prefix="SET" suffixOverrides=",">
                    <if test="businessSystemId != null">ibusiness_system_id =
                        #{businessSystemId},
                    </if>
                    <if test="businessSystemCode != null">ibusiness_system_code =
                        #{businessSystemCode},
                    </if>
                    <if test="businessSystemName != null">ibusiness_system_name =
                        #{businessSystemName},
                    </if>
                    <if test="businessSystemUnique != null">ibusiness_system_unique =
                        #{businessSystemUnique},
                    </if>
                    <if test="businessSystemDesc != null">ibusiness_system_desc =
                        #{businessSystemDesc},
                    </if>

                    <if test="status != null">istatus =
                        #{status},
                    </if>
                    <if test="updatorId != null">iupdator_id =
                        #{updatorId},
                    </if>
                    <if test="updatorName != null">iupdator_name =
                        #{updatorName},
                    </if>
                    iupdate_time = ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
        where iid = #{id}
    </update>

    <delete id="deleteProjectById" parameterType="Long">
        delete
        from ieai_envc_project where iid = #{id}
    </delete>

    <delete id="deleteProjectByIds" parameterType="String">
        delete from ieai_envc_project where iid in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAllProjectIds" resultType="java.lang.Long">
        SELECT DISTINCT ibusiness_system_id
        FROM ieai_envc_project
        WHERE istatus = 1
        <if test="businessSystemIdList != null and businessSystemIdList.size() > 0">
            AND (
            <foreach collection="businessSystemIdList" index="index" item="item" separator=" OR ">
                <if test="index % 999 == 0">
                    ibusiness_system_id IN
                    <foreach collection="businessSystemIdList" item="businessSystemIdItem" index="businessSystemIdIndex" open="(" separator="," close=")">
                        <if test="businessSystemIdIndex >= index and businessSystemIdIndex &lt; index + 999">
                            #{businessSystemIdItem}
                        </if>
                    </foreach>
                </if>
            </foreach>
            )
        </if>
    </select>

    <select id="selectProjectByIds" parameterType="java.lang.Long" resultMap="ProjectResult">
        <include refid="selectProject"/>
        <where>
            iid in
            <foreach collection="array" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <select id="selectProjectByBusinessSystemIds" parameterType="java.util.List" resultMap="ProjectResult">
        <include refid="selectProject"/>
        <where>
            ibusiness_system_id in
            <foreach collection="businessSystemIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>