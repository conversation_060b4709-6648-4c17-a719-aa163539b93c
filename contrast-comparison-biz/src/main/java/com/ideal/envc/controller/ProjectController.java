package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.ProjectDto;
import com.ideal.envc.model.dto.ProjectQueryDto;
import com.ideal.envc.service.IProjectService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.enums.ResponseCodeEnum;

import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 系统配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/project")
@MethodPermission("@dp.hasBtnPermission('system-configuration')")
public class ProjectController {
    private final Logger logger = LoggerFactory.getLogger(ProjectController.class);

    private final IProjectService projectService;
    private final UserinfoComponent userinfoComponent;

    public ProjectController(IProjectService projectService, UserinfoComponent userinfoComponent) {
        this.projectService = projectService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 查询已添加比对业务系统列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @MethodPermission("@dp.hasBtnPermission('system-configuration') or @dp.hasBtnPermission('comparison-settings')")
    public R<PageInfo<ProjectDto>> list(@RequestBody TableQueryDto<ProjectQueryDto> tableQueryDto) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            PageInfo<ProjectDto> list = projectService.selectProjectList(
                    tableQueryDto.getQueryParam(),
                    userDto,
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询已添加比对业务系统列表失败", e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询已添加比对业务系统列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询待添加比对业务系统列表（底层调用平台管理接口）
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/pendingSystemList")
    @MethodPermission("@dp.hasBtnPermission('saveSystem')")
    public R<PageInfo<ProjectDto>> getPendingSystemList(@RequestBody TableQueryDto<ProjectQueryDto> tableQueryDto) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            PageInfo<ProjectDto> list = projectService.getPendingSystemList(
                    tableQueryDto.getQueryParam(),
                    userDto,
                    tableQueryDto.getPageNum(),
                    tableQueryDto.getPageSize()
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询待添加比对业务系统列表失败", e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询待添加比对业务系统列表系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 根据一致性比对系统表主键查询比对业务系统详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<ProjectDto> selectProjectById(@RequestParam(value = "id")Long id) {
        try {
            ProjectDto projectDto = projectService.selectProjectById(id);
            if (projectDto == null) {
                return R.fail(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), ResponseCodeEnum.DATA_NOT_FOUND.getDesc());
            }
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), projectDto, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("查询比对业务系统详情失败", e);
            return R.fail(ResponseCodeEnum.QUERY_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("查询比对业务系统详情系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 新增保存比对业务系统
     *
     * @param projectDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('saveSystem')")
    public R<Void> save(@RequestBody ProjectDto projectDto) {
        try {
            if (projectService.insertProject(projectDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("新增比对业务系统失败", e);
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("新增比对业务系统系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 绑定比对业务系统
     *
     * @param businessSystemIds 添加数据
     * @return 添加结果
     */
    @PostMapping("/add")
    @MethodPermission("@dp.hasBtnPermission('saveSystem')")
    public R<Void> add(@RequestBody List<Long> businessSystemIds) {
        UserDto userDto = userinfoComponent.getUser();
        try {
            if (projectService.addProject(businessSystemIds, userDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("绑定比对业务系统失败", e);
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("绑定比对业务系统系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存比对业务系统
     *
     * @param projectDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody ProjectDto projectDto) {
        try {
            if (projectService.updateProject(projectDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), ResponseCodeEnum.UPDATE_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("修改比对业务系统失败", e);
            return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("修改比对业务系统系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 删除比对业务系统
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('removeSystem')")
    public R<Void> remove(@RequestBody Long[] ids) {
        try {
            if (projectService.deleteProjectByIds(ids) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.DELETE_FAIL.getCode(), ResponseCodeEnum.DELETE_FAIL.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("删除比对业务系统失败", e);
            return R.fail(ResponseCodeEnum.DELETE_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("删除比对业务系统系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
