package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.RunRuleDto;
import com.ideal.envc.model.dto.RunRuleQueryDto;
import com.ideal.envc.service.IRunRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 节点规则结果Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@RestController
@RequestMapping("/envc/rule")
public class RunRuleController {
    private final Logger logger = LoggerFactory.getLogger(RunRuleController.class);

    private final IRunRuleService runRuleService;

    public RunRuleController(IRunRuleService runRuleService) {
        this.runRuleService = runRuleService;
    }

    /**
     * 查询节点规则结果列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<RunRuleDto>> list(TableQueryDto<RunRuleQueryDto> tableQueryDto) {
        PageInfo<RunRuleDto> list = runRuleService.selectRunRuleList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询节点规则结果详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<RunRuleDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(runRuleService.selectRunRuleById(id));
    }

    /**
     * 新增保存节点规则结果
     *
     * @param runRuleDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody RunRuleDto runRuleDto) {
        try {
            if (runRuleService.insertRunRule(runRuleDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增节点规则结果系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存节点规则结果
     *
     * @param runRuleDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody RunRuleDto runRuleDto) {
        try {
            runRuleService.updateRunRule(runRuleDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("修改节点规则结果系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


    /**
     * 删除节点规则结果
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        try {
            runRuleService.deleteRunRuleByIds(ids);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("删除节点规则结果系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
