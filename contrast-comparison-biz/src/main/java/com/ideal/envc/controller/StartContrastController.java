package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.NodeLevelStartDto;
import com.ideal.envc.model.dto.PlanLevelStartDto;
import com.ideal.envc.model.dto.RuleLevelStartDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.SystemLevelStartDto;
import com.ideal.envc.model.dto.TaskLevelStartDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.model.enums.StartLevelEnums;
import com.ideal.envc.service.IStartContrastService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 一致性比对启动相关接口（方案管理里面各维度开始比对按钮调用接口）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/start")
public class StartContrastController {
    private final Logger logger = LoggerFactory.getLogger(StartContrastController.class);

    private final IStartContrastService startContrastService;
    private final UserinfoComponent userinfoComponent;

    public StartContrastController(IStartContrastService startContrastService, UserinfoComponent userinfoComponent) {
        this.startContrastService = startContrastService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 方案级启动
     *
     * @param startDto 方案级启动DTO
     * @return 启动结果
     */
    @PostMapping("/plans")
    @MethodPermission("@dp.hasBtnPermission('startImmePlanCompare')")
    public R<StartResult> startByPlans(@RequestBody @Valid PlanLevelStartDto startDto) {
        logger.info("方案级启动，参数：{}", startDto);
        try {
            // 设置用户信息
            UserDto userDto = userinfoComponent.getUser();
            startDto.setUserId(userDto.getId());
            startDto.setUserName(userDto.getFullName());
            // 方案级启动
            startDto.setStartType(StartLevelEnums.PLAN_LEVEL.getCode());

            StartResult result = startContrastService.startByPlans(startDto, userDto);
            if (result.isSuccess()) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());
            } else {
                return R.fail(ResponseCodeEnum.PLAN_START_FAIL.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            logger.error("方案级启动异常", e);
            return R.fail(ResponseCodeEnum.PLAN_START_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 系统级启动
     *
     * @param startDto 系统级启动DTO
     * @return 启动结果
     */
    @PostMapping("/systems")
    @MethodPermission("@dp.hasBtnPermission('startImmeSystemCompare')")
    public R<StartResult> startBySystems(@RequestBody @Valid SystemLevelStartDto startDto) {
        logger.info("系统级启动，参数：{}", startDto);
        try {
            // 设置用户信息
            UserDto userDto = userinfoComponent.getUser();
            startDto.setUserId(userDto.getId());
            startDto.setUserName(userDto.getFullName());
            // 系统级启动
            startDto.setStartType(StartLevelEnums.SYSTEM_LEVEL.getCode());

            StartResult result = startContrastService.startBySystems(startDto, userDto);
            if (result.isSuccess()) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());
            } else {
                return R.fail(ResponseCodeEnum.SYSTEM_START_FAIL.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            logger.error("系统级启动异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_START_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 节点级启动
     *
     * @param startDto 节点级启动DTO
     * @return 启动结果
     */
    @PostMapping("/nodes")
    @MethodPermission("@dp.hasBtnPermission('startImmeSourceTargetCompare')")
    public R<StartResult> startByNodes(@RequestBody @Valid NodeLevelStartDto startDto) {
        logger.info("节点级启动，参数：{}", startDto);
        try {
            // 设置用户信息
            UserDto userDto = userinfoComponent.getUser();
            startDto.setUserId(userDto.getId());
            startDto.setUserName(userDto.getFullName());
            // 节点级启动
            startDto.setStartType(StartLevelEnums.NODE_LEVEL.getCode());

            StartResult result = startContrastService.startByNodes(startDto, userDto);
            if (result.isSuccess()) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());
            } else {
                return R.fail(ResponseCodeEnum.NODE_START_FAIL.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            logger.error("节点级启动异常", e);
            return R.fail(ResponseCodeEnum.NODE_START_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 规则级启动
     *
     * @param startDto 规则级启动DTO
     * @return 启动结果
     */
    @PostMapping("/rules")
    @MethodPermission("@dp.hasBtnPermission('startImmeRuleCompare')")
    public R<StartResult> startByRules(@RequestBody @Valid RuleLevelStartDto startDto) {
        logger.info("规则级启动，参数：{}", startDto);
        try {
            // 设置用户信息
            UserDto userDto = userinfoComponent.getUser();
            startDto.setUserId(userDto.getId());
            startDto.setUserName(userDto.getFullName());
            // 规则级启动
            startDto.setStartType(StartLevelEnums.RULE_LEVEL.getCode());

            StartResult result = startContrastService.startByRules(startDto, userDto);
            if (result.isSuccess()) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());
            } else {
                return R.fail(ResponseCodeEnum.RULE_START_FAIL.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            logger.error("规则级启动异常", e);
            return R.fail(ResponseCodeEnum.RULE_START_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 任务级启动
     *
     * @param startDto 任务级启动DTO
     * @return 启动结果
     */
    @PostMapping("/tasks")
    @MethodPermission("@dp.hasBtnPermission('startImmeTaskCompare')")
    public R<StartResult> startByTasks(@RequestBody @Valid TaskLevelStartDto startDto) {
        logger.info("任务级启动，参数：{}", startDto);
        try {
            // 设置用户信息
            UserDto userDto = userinfoComponent.getUser();
            startDto.setUserId(userDto.getId());
            startDto.setUserName(userDto.getFullName());
            // 任务级启动
            startDto.setStartType(StartLevelEnums.TASK_LEVEL.getCode());

            StartResult result = startContrastService.startByTasks(startDto, userDto);
            if (result.isSuccess()) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), result, ResponseCodeEnum.SUCCESS.getDesc());
            } else {
                return R.fail(ResponseCodeEnum.TASK_START_FAIL.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            logger.error("任务级启动异常", e);
            return R.fail(ResponseCodeEnum.TASK_START_FAIL.getCode(), e.getMessage());
        }
    }
}
