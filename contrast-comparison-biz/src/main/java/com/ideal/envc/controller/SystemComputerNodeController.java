package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.SystemComputerNodeDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.ideal.envc.service.ISystemComputerNodeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 删除节点关系
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/computerNode")
public class SystemComputerNodeController {
    private final Logger logger = LoggerFactory.getLogger(SystemComputerNodeController.class);

    private final ISystemComputerNodeService systemComputerNodeService;
    private final UserinfoComponent userinfoComponent;

    public SystemComputerNodeController(ISystemComputerNodeService systemComputerNodeService, UserinfoComponent userinfoComponent) {
        this.systemComputerNodeService = systemComputerNodeService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 查询系统与设备节点关系列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<SystemComputerNodeDto>> list(@RequestBody TableQueryDto<SystemComputerNodeQueryDto> tableQueryDto) {
        PageInfo<SystemComputerNodeDto> list = systemComputerNodeService.selectSystemComputerNodeList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询系统与设备节点关系详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<SystemComputerNodeDto> getSystemComputerNodeDtoById(@RequestParam(value = "id")Long id) {
        return R.ok(systemComputerNodeService.selectSystemComputerNodeById(id));
    }

    /**
     * 新增保存系统与设备节点关系
     *
     * @param systemComputerNodeDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody SystemComputerNodeDto systemComputerNodeDto) {
        try {
            if (systemComputerNodeService.insertSystemComputerNode(systemComputerNodeDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增节点系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存系统与设备节点关系
     *
     * @param systemComputerNodeDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody SystemComputerNodeDto systemComputerNodeDto) {
        systemComputerNodeService.updateSystemComputerNode(systemComputerNodeDto);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
    }


    /**
     * 删除系统与设备节点关系
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestBody Long[] ids) {
        systemComputerNodeService.deleteSystemComputerNodeByIds(ids);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
    }




}
