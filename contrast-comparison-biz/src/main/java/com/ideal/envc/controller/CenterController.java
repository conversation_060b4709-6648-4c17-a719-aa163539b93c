package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.service.ICenterService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 物理中心
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/center")
public class CenterController {
    private final Logger logger = LoggerFactory.getLogger(CenterController.class);

    private final ICenterService centerService;

    public CenterController(ICenterService centerService) {
        this.centerService = centerService;
    }

    /**
     * 获取中心列表
     *
     * @return 中心列表
     */
    @PostMapping("/list")
    @MethodPermission("@dp.hasBtnPermission('comparison-settings')")
    public R<List<CenterDto>> list(@RequestBody CenterQueryDto centerQueryDto) {
        logger.info("获取中心列表");
        List<CenterDto> list = centerService.getCenterList(centerQueryDto);
        return R.ok(list);
    }
}
