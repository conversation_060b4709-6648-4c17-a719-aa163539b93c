package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.NodeBatchSaveRequestDto;
import com.ideal.envc.model.dto.SystemListDto;
import com.ideal.envc.model.dto.SystemListQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.SystemComputerNodeListDto;
import com.ideal.envc.model.dto.SystemComputerNodeQueryDto;
import com.ideal.envc.model.dto.SystemComputerNodeBatchDto;
import com.ideal.envc.model.dto.SystemComputerQueryPageDto;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryDto;
import com.ideal.envc.service.INodeIndexService;
import com.ideal.envc.service.ISystemComputerNodeService;
import com.ideal.envc.service.ISystemComputerService;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 规则配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/node")
@MethodPermission("@dp.hasBtnPermission('comparison-settings')")
public class NodeIndexController {
    private static final Logger logger = LoggerFactory.getLogger(NodeIndexController.class);
    private final INodeIndexService nodeIndexService;
    private final ISystemComputerService systemComputerService;
    private final UserinfoComponent userinfoComponent;
    private final ISystemComputerNodeService systemComputerNodeService;

    public NodeIndexController(INodeIndexService nodeIndexService, ISystemComputerService systemComputerService, UserinfoComponent userinfoComponent, ISystemComputerNodeService systemComputerNodeService) {
        this.nodeIndexService = nodeIndexService;
        this.systemComputerService = systemComputerService;
        this.userinfoComponent = userinfoComponent;
        this.systemComputerNodeService = systemComputerNodeService;
    }

    /**
     * 系统节点关系规则新增方法
     * @param nodeBatchSaveRequestDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/insertNode")
    public R<Void> save(@RequestBody NodeBatchSaveRequestDto nodeBatchSaveRequestDto) {
        try{
            if (nodeIndexService.batchSaveNodeRelation(nodeBatchSaveRequestDto,userinfoComponent.getUser()) ) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("系统节点关系规则新增方法异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询规则配置-节点关系-已绑定设备的业务系统列表
     *
     * @param tableQueryDto 查询条件
     * @return 业务系统列表
     */
    @PostMapping("/systemList")
    public R<PageInfo<SystemListDto>> systemList(@RequestBody TableQueryDto<SystemListQueryDto> tableQueryDto) {
        logger.info("查询已绑定设备的业务系统列表，查询条件：{}", tableQueryDto);
        UserDto userDto = userinfoComponent.getUser();
        PageInfo<SystemListDto> list = systemComputerService.selectSystemList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize(),
                userDto.getId()
        );
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询系统已绑定源目标设备列表
     *
     * @param tableQueryDto 查询条件
     * @return 系统已绑定源目标设备列表
     */
    @PostMapping("/nodeList")
    @MethodPermission("@dp.hasBtnPermission('comparison-settings')")
    public R<PageInfo<SystemComputerNodeListDto>> nodeList(@RequestBody TableQueryDto<SystemComputerNodeQueryDto> tableQueryDto) {
        logger.info("查询系统已绑定源目标设备列表，查询条件：{}", tableQueryDto);
        PageInfo<SystemComputerNodeListDto> list = systemComputerNodeService.selectSystemComputerNodeBeanList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 系统绑定源目标设备
     *
     * @param batchDto 批量绑定参数
     * @return 绑定结果
     */
    @PostMapping("/bind")
    @MethodPermission("@dp.hasBtnPermission('saveSourceComputer')")
    public R<Void> bind(@RequestBody SystemComputerNodeBatchDto batchDto) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            int result = systemComputerNodeService.batchBindSystemComputerNode(batchDto, userDto);
            if (result > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            } else {
                return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
            }
        } catch (ContrastBusinessException e) {
            logger.warn("批量绑定系统设备节点失败：{}", e.getMessage());
            String[] parts = e.getMessage().split("：", 2);
            String code = parts[0];
            String message = parts.length > 1 ? parts[1] : e.getMessage();
            return R.fail(code, message);
        } catch (Exception e) {
            logger.error("批量绑定系统设备节点异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 系统解绑源目标设备
     *
     * @param ids 主键列表
     * @return 解绑结果
     */
    @PostMapping("/unbindComputer")
    @MethodPermission("@dp.hasBtnPermission('removeSourceComputer')")
    public R<Void> unbind(@RequestBody Long[] ids) {
        logger.info("系统解绑源目标设备，IDs：{}", Arrays.toString(ids));
        systemComputerNodeService.deleteSystemComputerNodeByIds(ids);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 删除节点关系记录
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/computerNodeRemove")
    public R<Void> computerNodeRemove(@RequestBody Long[] ids) {
        systemComputerNodeService.deleteSystemComputerNodeByIds(ids);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询待绑定目标带绑定设备列表（分页）
     *
     * @param tableQueryDto 查询条件
     * @return 设备列表分页信息
     */
    @PostMapping("/computerListPage")
    public R<PageInfo<SystemComputerDto>> computerListPage(@RequestBody TableQueryDto<SystemComputerQueryPageDto> tableQueryDto) {
        try {
            if (tableQueryDto == null || tableQueryDto.getQueryParam() == null) {
                logger.warn("查询系统设备列表参数为空");
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "查询参数不能为空");
            }

            SystemComputerQueryPageDto queryDto = tableQueryDto.getQueryParam();
            // 参数校验
            if (queryDto.getBusinessSystemId() == null || queryDto.getSourceCenterId() == null 
                    || queryDto.getTargetCenterId() == null || queryDto.getSourceComputerId() == null) {
                logger.warn("查询系统设备列表参数不完整");
                return R.fail(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode(), "业务系统ID、源中心ID、目标中心ID和源设备ID不能为空");
            }

            logger.info("查询系统设备列表（分页），查询条件：{}", queryDto);
            PageInfo<SystemComputerDto> list = nodeIndexService.selectSystemComputerListPage(tableQueryDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.warn("查询系统设备列表失败：{}", e.getMessage());
            String[] parts = e.getMessage().split("：", 2);
            String code = parts[0];
            String message = parts.length > 1 ? parts[1] : e.getMessage();
            return R.fail(code, message);
        } catch (Exception e) {
            logger.error("查询系统设备列表异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 查询待绑定目标设备列表
     *
     * @param queryDto 查询条件
     * @return 设备列表
     */
    @PostMapping("/computerList")
    public R<List<SystemComputerDto>> computerList(@RequestBody SystemComputerQueryDto queryDto) {
        logger.info("查询系统设备列表（不分页），查询条件：{}", queryDto);

        List<SystemComputerDto> list = nodeIndexService.selectSystemComputerList(
                queryDto.getBusinessSystemId(),
                queryDto.getCenterId(),
                queryDto.getExcludeComputerIds()
        );

        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询待绑定源设备列表
     *
     * @param queryDto 查询条件
     * @return 设备列表
     */
    @PostMapping("/computerSourceList")
    public R<List<SystemComputerDto>> computerSourceList(@RequestBody SystemComputerQueryDto queryDto) {
        logger.info("查询系统源设备列表（不分页），查询条件：{}", queryDto);

        List<SystemComputerDto> list = nodeIndexService.selectSystemComputerList(
                queryDto.getBusinessSystemId(),
                queryDto.getCenterId(),
                queryDto.getExcludeComputerIds()
        );

        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }
}
