package com.ideal.envc.controller;

import com.ideal.common.dto.R;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字符集
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/charset")
public class CharsetController {
    private static final Logger logger = LoggerFactory.getLogger(CharsetController.class);

    /**
     * 获取所有可用的字符集列表
     * 返回字符集名称和编码的映射关系，用于前端下拉列表渲染
     *
     * @return 字符集列表
     */
    @PostMapping("/list")
    @MethodPermission("@dp.hasBtnPermission('comparison-settings') or @dp.hasBtnPermission('programme-management')")
    public R<List<Map<String, String>>> getCharsetList() {
        try {
            logger.info("获取字符集列表");
            List<Map<String, String>> charsetList = new ArrayList<>();
            
            // 获取所有可用的字符集
            Charset.availableCharsets().forEach((name, charset) -> {
                Map<String, String> charsetInfo = new HashMap<>();
                charsetInfo.put("name", name);  // 字符集名称
                charsetInfo.put("code", name);  // 字符集编码
                charsetList.add(charsetInfo);
            });
            
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), charsetList, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("获取字符集列表异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
