package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.DictionaryDto;
import com.ideal.envc.model.dto.DictionaryQueryDto;
import com.ideal.envc.service.IDictionaryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 字典管理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {
    private final Logger logger = LoggerFactory.getLogger(DictionaryController.class);

    private final IDictionaryService dictionaryService;

    public DictionaryController(IDictionaryService dictionaryService) {
        this.dictionaryService = dictionaryService;
    }

    /**
     * 查询字典码列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    public R<PageInfo<DictionaryDto>> list(@RequestBody TableQueryDto<DictionaryQueryDto> tableQueryDto) {
        PageInfo<DictionaryDto> list = dictionaryService.selectDictionaryList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(list);
    }

    /**
     * 查询字典码详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<DictionaryDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(dictionaryService.selectDictionaryById(id));
    }

    /**
     * 新增保存字典码
     *
     * @param dictionaryDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody DictionaryDto dictionaryDto) {
        try {
            if (dictionaryService.insertDictionary(dictionaryDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增字典系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存字典码
     *
     * @param dictionaryDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody DictionaryDto dictionaryDto) {
        try {
            dictionaryService.updateDictionary(dictionaryDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("修改字典系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


    /**
     * 删除字典码
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        try {
            dictionaryService.deleteDictionaryByIds(ids);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("删除字典系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 验证字典码是否存在
     *
     * @param code 字典码
     * @return 验证结果，true表示存在，false表示不存在
     */
    @GetMapping("/validateDictionaryCode")
    public R<Boolean> validateDictionaryCode(@RequestParam(value = "code") String code) {
        logger.info("验证字典码是否存在: {}", code);
        Boolean exists = dictionaryService.validateDictionaryCode(code);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), exists, ResponseCodeEnum.SUCCESS.getDesc());
    }
}
