package com.ideal.envc.controller;

import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.ContentDetailDto;
import com.ideal.envc.model.dto.ResultMonitorDto;
import com.ideal.envc.model.dto.ResultMonitorQueryDto;
import com.ideal.envc.service.IResultMonitorService;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 比对结果
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/result")
@MethodPermission("@dp.hasBtnPermission('comparison-results')")
public class ResultMonitorController {
    private static final Logger logger = LoggerFactory.getLogger(ResultMonitorController.class);

    private final IResultMonitorService resultMonitorService;

    public ResultMonitorController(IResultMonitorService resultMonitorService) {
        this.resultMonitorService = resultMonitorService;
    }

    /**
     * 查询比对结果列表
     *
     * @param tableQueryDto 查询条件
     * @return 比对结果列表
     */
    @PostMapping("/list")
    public R<PageInfo<ResultMonitorDto>> list(@RequestBody TableQueryDto<ResultMonitorQueryDto> tableQueryDto) {
        logger.info("查询比对结果列表，查询条件：{}", tableQueryDto);
        
        PageInfo<ResultMonitorDto> list = resultMonitorService.selectResultMonitorList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        
      
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询比对详情
     *
     * @param flowId 流程ID
     * @return 比对详情
     */
    @PostMapping("/content")
    public R<ContentDetailDto> detail(@RequestParam Long flowId) {
        logger.info("查询比对详情，flowId：{}", flowId);
        
        try {
            ContentDetailDto contentDetail = resultMonitorService.selectContentDetailByFlowId(flowId);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), contentDetail, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.warn("查询比对详情业务异常，flowId：{}，异常信息：{}", flowId, e.getMessage());
            return R.fail(ResponseCodeEnum.DATA_NOT_FOUND.getCode(), e.getMessage());
        }
       catch (Exception e) {
            logger.error("查询比对详情系统异常，flowId：{}", flowId, e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 根据流程ID导出比对报表
     *
     * @param flowId 流程ID
     * @param response HTTP响应对象
     */
    @PostMapping("/exportReport")
    @MethodPermission("@dp.hasBtnPermission('comparison-results')")
    public R<Object> exportReport(@RequestParam Long flowId,
                           HttpServletResponse response) {
        logger.info("导出比对报表，flowId：{}", flowId);

        try {
            resultMonitorService.exportComparisonReportByFlowId(flowId,  response);
            logger.info("导出比对报表成功，flowId：{}", flowId);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), true, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.warn("导出比对报表业务异常，flowId：{}，异常信息：{}", flowId, e.getMessage());
            return R.fail(ResponseCodeEnum.EXPORT_FAIL.getCode(), e.getMessage());
        } catch (Exception e) {
            logger.error("导出比对报表系统异常，flowId：{}", flowId, e);
            return R.fail(ResponseCodeEnum.EXPORT_FAIL.getCode(), e.getMessage());
        }
    }

}
