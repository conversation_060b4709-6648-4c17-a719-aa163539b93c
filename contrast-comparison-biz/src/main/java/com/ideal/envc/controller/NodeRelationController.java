package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.component.UserinfoComponent;
import com.ideal.envc.model.dto.NodeRelationListDto;
import com.ideal.envc.model.dto.NodeRelationQueryDto;
import com.ideal.envc.model.dto.NodeRelationDto;
import com.ideal.envc.model.dto.NodeRelationSaveDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.NodeRelationEnableDto;
import com.ideal.envc.service.INodeRelationService;
import com.ideal.system.common.component.aop.MethodPermission;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.exception.ContrastBusinessException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 规则配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/relation")
@MethodPermission("@dp.hasBtnPermission('comparison-settings')")
public class NodeRelationController {
    private static final Logger logger = LoggerFactory.getLogger(NodeRelationController.class);

    private final INodeRelationService nodeRelationService;
    private final UserinfoComponent userinfoComponent;
    public NodeRelationController(INodeRelationService nodeRelationService, UserinfoComponent userinfoComponent) {
        this.nodeRelationService = nodeRelationService;
        this.userinfoComponent = userinfoComponent;
    }

    /**
     * 查询节点关系规则列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @PostMapping("/list")
    @MethodPermission("@dp.hasBtnPermission('comparison-settings') or @dp.hasBtnPermission('programme-management')")
    public R<PageInfo<NodeRelationListDto>> list(@RequestBody TableQueryDto<NodeRelationQueryDto> tableQueryDto) {
        PageInfo<NodeRelationListDto> list = nodeRelationService.selectNodeRelationList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询节点关系规则详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<NodeRelationDto> getNodeRelationInfo(@RequestParam(value = "id")Long id) {
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), nodeRelationService.selectNodeRelationById(id), ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 新增保存节点关系规则
     *
     * @param nodeRelationSaveDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    @MethodPermission("@dp.hasBtnPermission('removeRule')")
    public R<Void> save(@RequestBody NodeRelationSaveDto nodeRelationSaveDto) {
        try {
            UserDto userDto = userinfoComponent.getUser();

            nodeRelationService.insertNodeRelation(nodeRelationSaveDto, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("新增节点关系规则异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存节点关系规则
     *
     * @param nodeRelationSaveDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    @MethodPermission("@dp.hasBtnPermission('updateRule')")
    public R<Void> update(@RequestBody NodeRelationSaveDto nodeRelationSaveDto) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            nodeRelationService.updateNodeRelation(nodeRelationSaveDto, userDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("修改节点关系规则业务异常：{}", e.getMessage());
            // 根据异常消息判断具体的错误类型
            if (e.getMessage().contains("参数不能为空")) {
                return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
            } else if (e.getMessage().contains("未找到要修改的记录")) {
                return R.fail(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode(), e.getMessage());
            } else {
                return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("修改节点关系规则系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


    /**
     * 删除节点关系规则
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    @MethodPermission("@dp.hasBtnPermission('removeRule')")
    public R<Void> remove(@RequestBody Long[] ids) {
        nodeRelationService.deleteNodeRelationByIds(ids);
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 节点信息配置启用或禁用
     *
     * @param nodeRelationEnableDto 启用禁用参数
     * @return 结果
     */
    @PostMapping("/changeEnabled")
    @MethodPermission("@dp.hasBtnPermission('enableRule') or @dp.hasBtnPermission('disableRule')")
    public R<Void> changeEnabled(@RequestBody NodeRelationEnableDto nodeRelationEnableDto) {
        try {
            UserDto userDto = userinfoComponent.getUser();
            nodeRelationService.updateNodeRelationEnabledByIds(
                nodeRelationEnableDto.getIds(), 
                nodeRelationEnableDto.getOperationType(),
                userDto
            );
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (ContrastBusinessException e) {
            logger.error("启用/禁用节点关系规则业务异常：{}", e.getMessage());
            // 根据异常消息判断具体的错误类型
            if (e.getMessage().contains("ID列表不能为空")) {
                return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
            } else if (e.getMessage().contains("操作类型参数错误")) {
                return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
            } else if (e.getMessage().contains("用户信息不能为空")) {
                return R.fail(ResponseCodeEnum.UPDATE_PARAM_ERROR.getCode(), e.getMessage());
            } else if (e.getMessage().contains("规则不存在")) {
                return R.fail(ResponseCodeEnum.UPDATE_DATA_NOT_FOUND.getCode(), e.getMessage());
            } else if (e.getMessage().contains("已经是")) {
                return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
            } else {
                return R.fail(ResponseCodeEnum.UPDATE_FAIL.getCode(), e.getMessage());
            }
        } catch (Exception e) {
            logger.error("启用/禁用节点关系规则系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


}
