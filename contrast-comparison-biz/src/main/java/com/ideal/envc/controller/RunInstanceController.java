package com.ideal.envc.controller;


import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.R;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.envc.model.dto.RunInstanceDto;
import com.ideal.envc.model.dto.RunInstanceQueryDto;
import com.ideal.envc.service.IRunInstanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
/**
 * 实例Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@RestController
@RequestMapping("/envc/instance")
public class RunInstanceController {
    private final Logger logger = LoggerFactory.getLogger(RunInstanceController.class);

    private final IRunInstanceService runInstanceService;

    public RunInstanceController(IRunInstanceService runInstanceService) {
        this.runInstanceService = runInstanceService;
    }

    /**
     * 查询实例列表
     *
     * @param tableQueryDto 查询条件
     * @return 查询结果
     */
    @GetMapping("/list")
    public R<PageInfo<RunInstanceDto>> list(TableQueryDto<RunInstanceQueryDto> tableQueryDto) {
        PageInfo<RunInstanceDto> list = runInstanceService.selectRunInstanceList(
                tableQueryDto.getQueryParam(),
                tableQueryDto.getPageNum(),
                tableQueryDto.getPageSize()
        );
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), list, ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 查询实例详细信息
     *
     * @param id 数据唯一标识
     * @return 查询结果
     */
    @GetMapping(value = "/get")
    public R<RunInstanceDto> getAgentInfoInfo(@RequestParam(value = "id")Long id) {
        return R.ok(ResponseCodeEnum.SUCCESS.getCode(), runInstanceService.selectRunInstanceById(id), ResponseCodeEnum.SUCCESS.getDesc());
    }

    /**
     * 新增保存实例
     *
     * @param runInstanceDto 添加数据
     * @return 添加结果
     */
    @PostMapping("/save")
    public R<Void> save(@RequestBody RunInstanceDto runInstanceDto) {
        try {
            if (runInstanceService.insertRunInstance(runInstanceDto) > 0) {
                return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
            }
            return R.fail(ResponseCodeEnum.ADD_FAIL.getCode(), ResponseCodeEnum.ADD_FAIL.getDesc());
        } catch (Exception e) {
            logger.error("新增实例系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * 修改保存实例
     *
     * @param runInstanceDto 更新数据
     * @return 更新结果
     */
    @PostMapping("/update")
    public R<Void> update(@RequestBody RunInstanceDto runInstanceDto) {
        try {
            runInstanceService.updateRunInstance(runInstanceDto);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("修改实例系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }


    /**
     * 删除实例
     *
     * @param ids 主键列表
     * @return 删除结果
     */
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids) {
        try {
            runInstanceService.deleteRunInstanceByIds(ids);
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), null, ResponseCodeEnum.SUCCESS.getDesc());
        } catch (Exception e) {
            logger.error("删除实例系统异常", e);
            return R.fail(ResponseCodeEnum.SYSTEM_ERROR.getCode(), ResponseCodeEnum.SYSTEM_ERROR.getDesc());
        }
    }
}
