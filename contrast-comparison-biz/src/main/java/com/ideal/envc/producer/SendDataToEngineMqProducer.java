package com.ideal.envc.producer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.config.ContrastConfiguration;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.enums.MessageTopicEnum;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 发送启动任务给引擎的中间MQ
 * <AUTHOR>
 */
@Component
public class SendDataToEngineMqProducer {
    private final IPublisher publisher;
    private final ContrastConfiguration contrastConfiguration;
    private static final Logger logger = LoggerFactory.getLogger(SendDataToEngineMqProducer.class);

    /**
     * 构造函数
     *
     * @param publisher 消息发布器
     * @param contrastConfiguration 对比配置
     */
    public SendDataToEngineMqProducer(IPublisher publisher, ContrastConfiguration contrastConfiguration) {
        this.publisher = publisher;
        this.contrastConfiguration = contrastConfiguration;
    }

    /**
     * 发送任务到引擎
     *
     * @param message 消息内容
     * @return 是否成功
     * @throws EngineServiceException 引擎服务异常
     */
    public boolean sendTaskToEngine(Object message) throws EngineServiceException {
        if (message == null) {
            logger.info("sendTaskToEngine Parameters:null");
            return false;
        }
        
        String runString = JSON.toJSONString(message);
        logger.info("Send tasks to the engine get MQ started!");
        
        try {
            // 优先使用配置中心的配置，如果没有则使用默认值
            String topicValue = getTopicFromConfig();
            logger.info("Send tasks to the engine get MQ topic:{}", topicValue);
            
            if (StringUtils.isBlank(topicValue)) {
                logger.error("MQ topic is empty!");
                return false;
            }
            
            // 使用配置的topic
            String channel = topicValue + "-out-0";
            this.publisher.apply(channel, runString);
            logger.info("Send task to engine get MQ end!");
            return true;
        } catch (CommunicationException e) {
            logger.error("Failed to send task to engine: {}", e.getMessage(), e);
            throw new EngineServiceException(e);
        }
    }

    /**
     * 从配置中获取主题
     *
     * @return 主题名称
     */
    private String getTopicFromConfig() {
        // 优先使用配置中心的配置
        if (contrastConfiguration != null &&
            contrastConfiguration.getEngine() != null &&
            contrastConfiguration.getEngine().getSendTask() != null &&
            StringUtils.isNotBlank(contrastConfiguration.getEngine().getSendTask().getTopic()) &&
            (contrastConfiguration.getEngine().getSendTask().getEnabled() == null ||
             Boolean.TRUE.equals(contrastConfiguration.getEngine().getSendTask().getEnabled()))) {
            return contrastConfiguration.getEngine().getSendTask().getTopic();
        }
        // 如果配置中心没有配置，则使用默认值
        return MessageTopicEnum.SEND_TASK_TO_ENGINE.getChannel();
    }

    /**
     * 发送启动任务流程数据
     *
     * @param startTaskFlowDtoList 启动任务流程数据列表
     * @return 是否成功
     * @throws EngineServiceException 引擎服务异常
     */
    public boolean sendStartTaskFlowData(Object startTaskFlowDtoList) throws EngineServiceException {
        return sendTaskToEngine(startTaskFlowDtoList);
    }
}
