package com.ideal.envc.producer;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.RunInstanceStateMessage;
import com.ideal.envc.model.enums.MessageTopicEnum;
import com.ideal.message.center.IPublisher;
import com.ideal.message.center.exception.CommunicationException;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 运行实例状态生产者
 * 用于在更新ieai_envc_run_instance_info表状态后发送消息，以更新ieai_envc_run_instance表状态
 * <AUTHOR>
 */
@Component
public class RunInstanceStateProducer {
    private static final Logger logger = LoggerFactory.getLogger(RunInstanceStateProducer.class);
    private static final String TOPIC = MessageTopicEnum.RUN_INSTANCE_STATE_TOPIC.getChannel();
    private final IPublisher publisher;

    public RunInstanceStateProducer(IPublisher publisher) {
        this.publisher = publisher;
    }

    /**
     * 发送运行实例状态消息
     *
     * @param message 运行实例状态消息
     * @return 是否成功
     */
    public boolean sendRunInstanceStateMessage(RunInstanceStateMessage message) {
        if (message == null) {
            logger.warn("发送运行实例状态消息失败：消息为空");
            return false;
        }

        try {
            String messageJson = JSON.toJSONString(message);
            String channel = TOPIC + "-out-0";
            
            /**
             * 使用MessageBuilder构建消息
             * 使用instanceId作为顺序消息的key，确保相同instanceId的消息发送到同一个分区
             */
            Message<String> msg = MessageBuilder
                .withPayload(messageJson)
                .setHeader("instanceId", message.getInstanceId())
                .build();
            
            this.publisher.apply(channel, msg);
            logger.info("发送运行实例状态消息成功，instanceId: {}, instanceInfoId: {}", 
                message.getInstanceId(), message.getInstanceInfoId());
            return true;
        } catch (CommunicationException e) {
            logger.error("发送运行实例状态消息失败，instanceId: {}, instanceInfoId: {}", 
                message.getInstanceId(), message.getInstanceInfoId(), e);
            return false;
        }
    }
} 