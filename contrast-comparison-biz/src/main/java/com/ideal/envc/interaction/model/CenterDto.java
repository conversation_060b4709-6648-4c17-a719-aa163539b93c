package com.ideal.envc.interaction.model;

import java.io.Serializable;

/**
 * 中心数据传输对象
 *
 * <AUTHOR>
 */
public class CenterDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键，中心ID
     */
    private Long id;
    
    /**
     * 中心名称
     */
    private String name;
    


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "CenterDto{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
