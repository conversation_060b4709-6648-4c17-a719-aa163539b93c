package com.ideal.envc.interaction.model;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ComputerJo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    private Long computerId;

    /**
     * 设备ip
     */
    private String computerIp;

    /**
     * 设备名称
     */
    private String computerName;

    /**
     * 设备型号id
     */
    private Long computerModelId;

    /**
     * 设备型号名称
     */
    private String computerModelName;

    /**
     * 设备型号编码
     */
    private String computerModelCode;

    /**
     * 设备类型id
     */
    private Long computerTypeId;

    /**
     * 设备类型名称
     */
    private String computerTypeName;

    /**
     * 机房
     */
    private String machineRoom;

    /**
     * 机柜
     */
    private String machineBox;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 设备分类（0：服务器设备，1：网络设备）  与平台管理属性key不一致需要额外封装
     */
    private Integer type;

    /**
     * 设备厂商id
     */
    private Long computerManufactureId;

    /**
     * 设备厂商名称
     */
    private String computerManufactureName;


    /**
     * 绑定id
     */
    private Long bindId;

    /**
     * 绑定类型
     */
    private Integer bindType;

    /**
     * 设备用户
     */
    private String computerUser;

    /**
     * 设备认证密码
     */
    private String computerSign;


    /**
     * 堡垒机IP
     */
    private String fortIp;

    /**
     * 堡垒机端口
     */
    private Integer fortPort;




    /**
     * agnetId
     */
    private Long agentId;
    /**
     * agnet名称
     */
    private String agentName;

    /**
     * agentIp
     */
    private String agentIp;

    /**
     * agent端口
     */
    private String agentPort;


    /**
     * 登录协议
     */
    private String loginAgreement;



    /**
     * 数据中心主键
     */
    private Long centerId;

    /**
     * 数据中心名称
     */
    private String centerName;

    /**
     * 扩展对象
     */
    private String extendJson;
    /**
     * 系统名称
     */
    private String osName;
    /**
     * 系统版本
     */
    private String osVersion;



    /**
     * 标签.与平台管理属性key不一致需要额外封装
     */
    private String tags;

    private String computerHighUser;

    /**
     * 设备运行状态
     * 0 未启动
     * 2 部分启动
     * 3 已启动
     */
    private Long runState;



    /**
     * 所有agent信息集合（包括启用、禁用Agent）
     */
    private List<AgentInfoJo> allAgentInfoList;




    /**
     * 操作类型标识（用于消息推送）
     * <p>add:新增，update:编辑，delete:删除
     */
    private String operateType;

    /**
     * 变化的有哪些字段（用于消息推送）
     */
    private List<String> updatedFields;



    public List<AgentInfoJo> getAllAgentInfoList() {
        return allAgentInfoList;
    }

    public void setAllAgentInfoList(List<AgentInfoJo> allAgentInfoList) {
        this.allAgentInfoList = allAgentInfoList;
    }


    public String getOperateType() {
        return operateType;
    }

    public void setOperateType(String operateType) {
        this.operateType = operateType;
    }

    public List<String> getUpdatedFields() {
        return updatedFields;
    }

    public void setUpdatedFields(List<String> updatedFields) {
        this.updatedFields = updatedFields;
    }

    public Long getRunState() {
        return runState;
    }

    public void setRunState(Long runState) {
        this.runState = runState;
    }

    public Long getComputerId() {
        return computerId;
    }

    public void setComputerId(Long computerId) {
        this.computerId = computerId;
    }

    public String getComputerIp() {
        return computerIp;
    }

    public void setComputerIp(String computerIp) {
        this.computerIp = computerIp;
    }

    public String getComputerName() {
        return computerName;
    }

    public void setComputerName(String computerName) {
        this.computerName = computerName;
    }

    public Long getComputerModelId() {
        return computerModelId;
    }

    public void setComputerModelId(Long computerModelId) {
        this.computerModelId = computerModelId;
    }

    public String getComputerModelName() {
        return computerModelName;
    }

    public void setComputerModelName(String computerModelName) {
        this.computerModelName = computerModelName;
    }

    public String getComputerModelCode() {
        return computerModelCode;
    }

    public void setComputerModelCode(String computerModelCode) {
        this.computerModelCode = computerModelCode;
    }

    public Long getComputerTypeId() {
        return computerTypeId;
    }

    public void setComputerTypeId(Long computerTypeId) {
        this.computerTypeId = computerTypeId;
    }

    public String getComputerTypeName() {
        return computerTypeName;
    }

    public void setComputerTypeName(String computerTypeName) {
        this.computerTypeName = computerTypeName;
    }

    public String getMachineRoom() {
        return machineRoom;
    }

    public void setMachineRoom(String machineRoom) {
        this.machineRoom = machineRoom;
    }

    public String getMachineBox() {
        return machineBox;
    }

    public void setMachineBox(String machineBox) {
        this.machineBox = machineBox;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getComputerManufactureId() {
        return computerManufactureId;
    }

    public void setComputerManufactureId(Long computerManufactureId) {
        this.computerManufactureId = computerManufactureId;
    }

    public String getComputerManufactureName() {
        return computerManufactureName;
    }

    public void setComputerManufactureName(String computerManufactureName) {
        this.computerManufactureName = computerManufactureName;
    }

    public Long getBindId() {
        return bindId;
    }

    public void setBindId(Long bindId) {
        this.bindId = bindId;
    }

    public Integer getBindType() {
        return bindType;
    }

    public void setBindType(Integer bindType) {
        this.bindType = bindType;
    }

    public String getComputerUser() {
        return computerUser;
    }

    public void setComputerUser(String computerUser) {
        this.computerUser = computerUser;
    }

    public String getComputerSign() {
        return computerSign;
    }

    public void setComputerSign(String computerSign) {
        this.computerSign = computerSign;
    }

    public String getFortIp() {
        return fortIp;
    }

    public void setFortIp(String fortIp) {
        this.fortIp = fortIp;
    }

    public Integer getFortPort() {
        return fortPort;
    }

    public void setFortPort(Integer fortPort) {
        this.fortPort = fortPort;
    }

    public Long getAgentId() {
        return agentId;
    }

    public void setAgentId(Long agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getAgentIp() {
        return agentIp;
    }

    public void setAgentIp(String agentIp) {
        this.agentIp = agentIp;
    }

    public String getAgentPort() {
        return agentPort;
    }

    public void setAgentPort(String agentPort) {
        this.agentPort = agentPort;
    }

    public String getLoginAgreement() {
        return loginAgreement;
    }

    public void setLoginAgreement(String loginAgreement) {
        this.loginAgreement = loginAgreement;
    }



    public Long getCenterId() {
        return centerId;
    }

    public void setCenterId(Long centerId) {
        this.centerId = centerId;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public String getExtendJson() {
        return extendJson;
    }

    public void setExtendJson(String extendJson) {
        this.extendJson = extendJson;
    }

    public String getOsName() {
        return osName;
    }

    public void setOsName(String osName) {
        this.osName = osName;
    }

    public String getOsVersion() {
        return osVersion;
    }

    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }



    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getComputerHighUser() {
        return computerHighUser;
    }

    public void setComputerHighUser(String computerHighUser) {
        this.computerHighUser = computerHighUser;
    }

    @Override
    public String toString() {
        return "ComputerJo{" +
                "computerId=" + computerId +
                ", computerIp='" + computerIp + '\'' +
                ", computerName='" + computerName + '\'' +
                ", computerModelId=" + computerModelId +
                ", computerModelName='" + computerModelName + '\'' +
                ", computerModelCode='" + computerModelCode + '\'' +
                ", computerTypeId=" + computerTypeId +
                ", computerTypeName='" + computerTypeName + '\'' +
                ", machineRoom='" + machineRoom + '\'' +
                ", machineBox='" + machineBox + '\'' +
                ", serialNumber='" + serialNumber + '\'' +
                ", type=" + type +
                ", computerManufactureId=" + computerManufactureId +
                ", computerManufactureName='" + computerManufactureName + '\'' +
                ", bindId=" + bindId +
                ", bindType=" + bindType +
                ", computerUser='" + computerUser + '\'' +
                ", computerSign='" + computerSign + '\'' +
                ", fortIp='" + fortIp + '\'' +
                ", fortPort=" + fortPort +
                ", agentId=" + agentId +
                ", agentName='" + agentName + '\'' +
                ", agentIp='" + agentIp + '\'' +
                ", agentPort='" + agentPort + '\'' +
                ", loginAgreement='" + loginAgreement + '\'' +
                ", centerId=" + centerId +
                ", centerName='" + centerName + '\'' +
                ", extendJson='" + extendJson + '\'' +
                ", osName='" + osName + '\'' +
                ", osVersion='" + osVersion + '\'' +
                ", tags='" + tags + '\'' +
                ", computerHighUser='" + computerHighUser + '\'' +
                ", runState=" + runState +
                ", allAgentInfoList=" + allAgentInfoList +
                ", operateType='" + operateType + '\'' +
                ", updatedFields=" + updatedFields +
                '}';
    }
}
