package com.ideal.envc.interaction.model;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class AgentInfoJo implements Serializable {
    private static final long serialVersionUID = 1L;


    /** 主键 */
    private Long id;


    /** 设备表主键 */
    private Long sysmComputerListId;


    /** agent名称 */
    private String name;


    /** agentIP */
    private String ip;


    /** agent端口 */
    private Integer port;


    /** agent描述 */
    private String description;


    /** 是否ssl，0：否，1：是 */
    private Integer ifSsl;




    /** 删除标记，0否 1是 */
    private Integer deleted;

    /**
     * 模块类型
     */
    private String modelType;



    /**
     * 设备名称
     */
    private String computerListName;



    /**
     * 启用0，停用1
     */
    private Integer enabled;

    /**
     * 业务类型
     */
    private String businessType;


    /**
     * 是否默认代理
     */
    private Integer ifDefaultAgent;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSysmComputerListId() {
        return sysmComputerListId;
    }

    public void setSysmComputerListId(Long sysmComputerListId) {
        this.sysmComputerListId = sysmComputerListId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getIfSsl() {
        return ifSsl;
    }

    public void setIfSsl(Integer ifSsl) {
        this.ifSsl = ifSsl;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public String getComputerListName() {
        return computerListName;
    }

    public void setComputerListName(String computerListName) {
        this.computerListName = computerListName;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getIfDefaultAgent() {
        return ifDefaultAgent;
    }

    public void setIfDefaultAgent(Integer ifDefaultAgent) {
        this.ifDefaultAgent = ifDefaultAgent;
    }

    @Override
    public String toString() {
        return "AgentInfoJo{" +
                "id=" + getId() +
                ", sysmComputerListId=" + getSysmComputerListId() +
                ", name='" + getName() + '\'' +
                ", ip='" + getIp() + '\'' +
                ", port=" + getPort() +
                ", description='" + getDescription() + '\'' +
                ", ifSsl=" + getIfSsl() +
                ", deleted=" + getDeleted() +
                ", modelType='" + getModelType() + '\'' +
                ", computerListName='" + getComputerListName() + '\'' +
                ", enabled=" + getEnabled() +
                ", businessType='" + getBusinessType() + '\'' +
                ", ifDefaultAgent=" + getIfDefaultAgent() +
                '}';
    }
}
