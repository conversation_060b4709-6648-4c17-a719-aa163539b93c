package com.ideal.envc.common;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Decoder;
import com.ideal.envc.config.Base64SecurityConfig;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.Arrays;
import java.util.HashSet;

/**
 * <AUTHOR>
 */
public class ContrastToolUtils {
    private static final Logger logger = LoggerFactory.getLogger(ContrastToolUtils.class);



    /**
     * base64解码（安全版本）
     *
     * @param s 加密串
     * @return 解密串，如果输入无效或超出安全限制则返回null
     */
    public static String getFromBase64(String s) {
        if (StringUtils.isBlank(s)) {
            return null;
        }

        try {
            // 1. 输入长度安全检查
            int maxInputLength = Base64SecurityConfig.getMaxInputLength();
            if (s.length() > maxInputLength) {
                logger.warn("Base64输入长度超出安全限制，输入长度：{}，最大允许：{}", s.length(), maxInputLength);
                return null;
            }

            // 2. Base64格式预验证
            String trimmedInput = s.trim();

            // 3. 执行解码
            byte[] bytes = new BASE64Decoder().decodeBuffer(trimmedInput);

            // 4. 解码后内容大小安全检查
            int maxDecodedSize = Base64SecurityConfig.getMaxDecodedSize();
            if (bytes.length > maxDecodedSize) {
                logger.warn("Base64解码后内容大小超出安全限制，解码后大小：{}字节，最大允许：{}字节",
                        bytes.length, maxDecodedSize);
                return null;
            }

            // 4. 转换为字符串（使用系统编码）
            return new String(bytes);

        } catch (Exception e) {
            logger.error("Base64解码失败，输入：{}，异常：{}",
                    s.substring(0, Math.min(s.length(), 100)) + "...", e.getMessage());
            return null;
        }
    }



    public static void main(String[] args) {
        System.out.println(getFromBase64( "cHl0aG9uOiBjYW4ndCBvcGVuIGZpbGUgJy4vYmFzZVNjcmlwdC9kaXJfY29tcGFyZS5weSc6IFtF\ncnJubyAyXSBObyBzdWNoIGZpbGUgb3IgZGlyZWN0b3J5Cg=="));
    }
    /**
     * 解析输出内容
     * @param output 输出内容
     * @return 解析后的Map
     */
    public static Map<String, Object> analysisOutPut(String output) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isBlank(output)) {
            return result;
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            result = mapper.readValue(output, Map.class);

            // 处理base64编码的字段
            String stdout = (String) result.get("stdout");
            if (stdout != null) {
                result.put("stdout", getFromBase64(stdout));
            }

            String stderr = (String) result.get("stderr");
            if (stderr != null) {
                result.put("stderr", getFromBase64(stderr));
            }

            String lastLine = (String) result.get("lastLine");
            if (lastLine != null) {
                result.put("lastLine", getFromBase64(lastLine));
            }
        } catch (JsonProcessingException e) {
            logger.error("解析内容不是json格式");
            result.put("err", output);
        }
        return result;
    }

    /**
     * 与当前时间差，单位为秒
     * @param createTime
     * @return
     */
    public static long getSecondsDifference(Date createTime) {
        if (createTime == null) {
            throw new IllegalArgumentException("createTime 不能为空");
        }
        // 当前时间毫秒数
        long currentTimeMillis = System.currentTimeMillis();
        // 指定时间毫秒数
        long createTimestamp = createTime.getTime();
        // 毫秒转秒
        return (currentTimeMillis - createTimestamp) / 1000;
    }

    /**
     * 数字字符串逗号分隔字符串转Long数组
     * @param input
     * @return
     *
     */
    public static Long[] convertStringToLongArray(String input) {
        if (input == null || input.trim().isEmpty()) {
            // 或者返回 null，根据业务需求决定
            return new Long[0];
        }

        return Arrays.stream(input.split(","))
                .map(String::trim)
                .map(Long::valueOf)
                .toArray(Long[]::new);
    }

    /**
     * 计算 array1 - array2 的差集，不改变原数组。
     * @param array1 原始数组1
     * @param array2 原始数组2
     * @return 差集结果的 Long[] 数组
     */
    public static Long[] subtract(Long[] array1, Long[] array2) {
        // 使用 HashSet 以加快查找速度
        Set<Long> set2 = new HashSet<>(Arrays.asList(array2));

        // 使用 Stream API 进行过滤和收集结果
        return Arrays.stream(array1)
                .filter(item -> !set2.contains(item))
                .toArray(Long[]::new);
    }

    /**
     * 计算 array1 和 array2 的交集（两个数组中都存在的元素）
     * @param array1 原始数组1
     * @param array2 原始数组2
     * @return 交集结果的 Long[] 数组
     */
    public static Long[] intersection(Long[] array1, Long[] array2) {
        Set<Long> set1 = new HashSet<>(Arrays.asList(array1));
        Set<Long> set2 = new HashSet<>(Arrays.asList(array2));
        // 保留共有的元素
        set1.retainAll(set2);
        return set1.toArray(new Long[0]);
    }

    /**
     * 验证cron表达式格式
     *
     * @param cronExpression cron表达式
     * @return 验证结果，如果验证通过返回true，否则返回false
     */
    public static boolean isValidCronExpression(String cronExpression) {
        if (StringUtils.isBlank(cronExpression)) {
            return false;
        }
        try {
            return CronExpression.isValidExpression(cronExpression.trim());
        } catch (Exception e) {
            logger.error("cron表达式格式验证失败：{}", cronExpression, e);
            return false;
        }
    }

    /**
     * 验证cron表达式格式并抛出异常
     *
     * @param cronExpression cron表达式
     * @throws RuntimeException 如果cron表达式格式不正确
     */
    public static void validateCronExpression(String cronExpression) {
        if (StringUtils.isBlank(cronExpression)) {
            throw new RuntimeException("cron表达式不能为空");
        }
        try {
            if (!CronExpression.isValidExpression(cronExpression.trim())) {
                throw new RuntimeException("cron表达式格式不正确");
            }
        } catch (Exception e) {
            logger.error("cron表达式格式验证失败：{}", cronExpression, e);
            throw new RuntimeException("cron表达式格式不正确：" + e.getMessage());
        }
    }

     /**
     * 格式化耗时
     *
     * @param elapsedTime 耗时（秒）
     * @return 格式化后的耗时字符串
     */
    public static String formatElapsedTime(Long elapsedTime) {
        if (elapsedTime == null || elapsedTime <= 0) {
            return "0秒";
        }
        //毫秒转换为秒
        elapsedTime = elapsedTime / 1000;
        long days = elapsedTime / (24 * 3600);
        long remainingSeconds = elapsedTime % (24 * 3600);
        long hours = remainingSeconds / 3600;
        remainingSeconds = remainingSeconds % 3600;
        long minutes = remainingSeconds / 60;
        long seconds = remainingSeconds % 60;

        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            sb.append(days).append("天");
            sb.append(hours).append("时");
            sb.append(minutes).append("分");
            sb.append(seconds).append("秒");
        } else if (hours > 0) {
            sb.append(hours).append("时");
            sb.append(minutes).append("分");
            sb.append(seconds).append("秒");
        } else if (minutes > 0) {
            sb.append(minutes).append("分");
            sb.append(seconds).append("秒");
        } else {
            sb.append(seconds).append("秒");
        }

        return sb.toString();
    }
}
