package com.ideal.envc.common;

/**
 * <AUTHOR>
 */
public class ContrastConstants {

    /**
     * 对比模板工程名称
     */
    public static final String CONTRAST_PRJ_NAME = "CompareTemplate";

    /**
     * 文件比对工作流名称
     */
    public static final String CONTRAST_FILE_COMPARE_FLOW_NAME = "ActExec_Compare";

    /**
     * 目录比对工作流名称
     */
    public static final String CONTRAST_DIR_COMPARE_FLOW_NAME = "ActExec_Compare_Dir";

    /**
     * 对比模板工程类型
     */
    public static final int CONTRAST_PRJ_TYPE = 13;

    /**
     * 对比模板工程环境变量：butterflyversion
     */
    public static final String CONTRAST_FLOW_ENV_BUTTERFLY_VERSION = "butterflyversion";

    /**
     * 比对结果内容
     */
    public static final String  COMPARE_RESULT = "compareResult";

    /**
     * 比对结果返回值
     */
    public static final String  RET = "ret";

    /**
     * 同步结果返回值
     */
    public static final String  SYNC_RESULT = "syncResult";

    /**
     * 脚本执行活动输出
     */
    public static final String  STDOUT = "stdout";

    /**
     * 脚本执行活动错误输出
     */
    public static final String  STDERR = "stderr";

    public static final String  ERR = "err";


    //  运行实例状态回更包含详情记录数计数器前缀
    public static final String RUN_INSTANCE_COUNTOR_PREFIX  = "RunInstanceState_";
    //  运行实例详情状态回更包含规则记录数计数器前缀
    public static final String RUN_INSTANCE_INFO_COUNTOR_PREFIX  = "RunInstanceInfoState_";

    // 计数器分布式锁前缀
    /**
     * 运行实例计数器锁前缀
     */
    public static final String RUN_INSTANCE_COUNTER_LOCK_PREFIX = "RUN_INSTANCE_COUNTER_LOCK_";
    
    /**
     * 运行实例详情计数器锁前缀
     */
    public static final String RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX = "RUN_INSTANCE_INFO_COUNTER_LOCK_";


}
