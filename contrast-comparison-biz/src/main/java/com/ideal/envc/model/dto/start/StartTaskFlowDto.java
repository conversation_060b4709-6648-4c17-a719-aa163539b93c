package com.ideal.envc.model.dto.start;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 业务任务集合bean
 * <AUTHOR>
 */
public class StartTaskFlowDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long uniqueTaskId;
    private List<StartFlowDto> tasks = new ArrayList<>();

    public Long getUniqueTaskId() {
        return uniqueTaskId;
    }

    public void setUniqueTaskId(Long uniqueTaskId) {
        this.uniqueTaskId = uniqueTaskId;
    }

    public List<StartFlowDto> getTasks() {
        return tasks;
    }

    public void setTasks(List<StartFlowDto> tasks) {
        this.tasks = tasks;
    }

}
