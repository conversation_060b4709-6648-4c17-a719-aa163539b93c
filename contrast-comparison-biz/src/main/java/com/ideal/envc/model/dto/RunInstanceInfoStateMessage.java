package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 运行实例信息状态消息
 * 用于在更新ieai_envc_run_rule表后发送消息，以更新ieai_envc_run_instance_info表状态
 */
public class RunInstanceInfoStateMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 运行实例信息ID
     */
    private Long instanceInfoId;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * ruleId对应状态
     */
    private int status;

    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getInstanceInfoId() {
        return instanceInfoId;
    }

    public void setInstanceInfoId(Long instanceInfoId) {
        this.instanceInfoId = instanceInfoId;
    }

    public Long getRuleId() {
        return ruleId;
    }

    public void setRuleId(Long ruleId) {
        this.ruleId = ruleId;
    }


    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}