package com.ideal.envc.model.bean;

/**
 * <AUTHOR>
 */
public class EngineActOutputBean implements java.io.Serializable{
    private static final long serialVersionUID = 1L;

    private Long id ;

    private String reqId;

    private Integer type;

    private String output;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getReqId() {
        return reqId;
    }

    public void setReqId(String reqId) {
        this.reqId = reqId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOutput() {
        return output;
    }

    public void setOutput(String output) {
        this.output = output;
    }
}
