package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 节点关系规则启用禁用数据传输对象
 *
 * <AUTHOR>
 */
public class NodeRelationEnableDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 操作类型（0：启用，1：禁用）
     */
    private Integer operationType;

    /**
     * 节点关系规则ID集合
     */
    private Long[] ids;

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public Long[] getIds() {
        return ids;
    }

    public void setIds(Long[] ids) {
        this.ids = ids;
    }

    @Override
    public String toString() {
        return "NodeRelationEnableDto{" +
                "operationType=" + operationType +
                ", ids=" + java.util.Arrays.toString(ids) +
                '}';
    }
}
