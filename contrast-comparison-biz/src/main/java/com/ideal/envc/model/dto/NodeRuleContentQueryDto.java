package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 节点关系规则对象 ieai_envc_node_rule_content
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class NodeRuleContentQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 信息配置ID */
    private Long envcNodeRelationId;
    /** 规则内容 */
    private String ruleContent;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcNodeRelationId(Long envcNodeRelationId){
        this.envcNodeRelationId = envcNodeRelationId;
    }

    public Long getEnvcNodeRelationId(){
        return envcNodeRelationId;
    }

    public void setRuleContent(String ruleContent){
        this.ruleContent = ruleContent;
    }

    public String getRuleContent(){
        return ruleContent;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
        " ["+
        "Hash = "+hashCode()+
            ",id="+getId()+
            ",envcNodeRelationId="+getEnvcNodeRelationId()+
            ",ruleContent="+getRuleContent()+
        "]";
    }
}

