package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.List;

/**
 * 比对业务系统对象 ieai_envc_project
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class ProjectQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 系统ID */
    private Long businessSystemId;
    /** 系统编码 */
    private String businessSystemCode;
    /** 系统名称 */
    private String businessSystemName;
    /** 系统唯一标识 */
    private String businessSystemUnique;
    /** 系统描述 */
    private String businessSystemDesc;

    /**
     * 排除的业务系统ID
     */
    private List<Long> excludeIds;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setBusinessSystemCode(String businessSystemCode){
        this.businessSystemCode = businessSystemCode;
    }

    public String getBusinessSystemCode(){
        return businessSystemCode;
    }

    public void setBusinessSystemName(String businessSystemName){
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemName(){
        return businessSystemName;
    }

    public void setBusinessSystemUnique(String businessSystemUnique){
        this.businessSystemUnique = businessSystemUnique;
    }

    public String getBusinessSystemUnique(){
        return businessSystemUnique;
    }

    public void setBusinessSystemDesc(String businessSystemDesc){
        this.businessSystemDesc = businessSystemDesc;
    }

    public String getBusinessSystemDesc(){
        return businessSystemDesc;
    }

    public List<Long> getExcludeIds() {
        return excludeIds;
    }

    public void setExcludeIds(List<Long> excludeIds) {
        this.excludeIds = excludeIds;
    }

    @Override
    public String toString() {
        return "ProjectQueryDto{" +
                "id=" + id +
                ", businessSystemId=" + businessSystemId +
                ", businessSystemCode='" + businessSystemCode + '\'' +
                ", businessSystemName='" + businessSystemName + '\'' +
                ", businessSystemUnique='" + businessSystemUnique + '\'' +
                ", businessSystemDesc='" + businessSystemDesc + '\'' +
                ", excludeIds=" + excludeIds +
                '}';
    }
}

