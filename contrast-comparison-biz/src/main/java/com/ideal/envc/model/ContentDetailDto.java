package com.ideal.envc.model;

import java.io.Serializable;

/**
 * 比对详情DTO
 *
 * <AUTHOR>
 */
public class ContentDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 目标内容 */
    private String targetContent;

    /** 源内容 */
    private String sourceContent;

    public String getTargetContent() {
        return targetContent;
    }

    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    public String getSourceContent() {
        return sourceContent;
    }

    public void setSourceContent(String sourceContent) {
        this.sourceContent = sourceContent;
    }

    @Override
    public String toString() {
        return "ContentDetailDto{" +
                "targetContent='" + targetContent + '\'' +
                ", sourceContent='" + sourceContent + '\'' +
                '}';
    }
}
