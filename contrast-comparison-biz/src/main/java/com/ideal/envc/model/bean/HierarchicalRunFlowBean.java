package com.ideal.envc.model.bean;

import com.ideal.envc.model.entity.RunFlowEntity;
import java.io.Serializable;

/**
 * 层次化流程对象
 *
 * <AUTHOR>
 */
public class HierarchicalRunFlowBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 流程ID */
    private Long flowid;
    /** 比对规则ID或者比对规则对应的同步id */
    private Long runBizId;
    /** 来源标识（0：比对，1：同步） */
    private Integer model;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    /** 耗时 */
    private Long elapsedTime;
    /** 执行结束码 */
    private String ret;
    /** 引擎消息时间字段 */
    private Long updateOrderTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFlowid() {
        return flowid;
    }

    public void setFlowid(Long flowid) {
        this.flowid = flowid;
    }

    public Long getRunBizId() {
        return runBizId;
    }

    public void setRunBizId(Long runBizId) {
        this.runBizId = runBizId;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getElapsedTime() {
        return elapsedTime;
    }

    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    public String getRet() {
        return ret;
    }

    public void setRet(String ret) {
        this.ret = ret;
    }

    public Long getUpdateOrderTime() {
        return updateOrderTime;
    }

    public void setUpdateOrderTime(Long updateOrderTime) {
        this.updateOrderTime = updateOrderTime;
    }

    /**
     * 从RunFlowEntity构建HierarchicalRunFlowBean
     *
     * @param entity RunFlowEntity实体
     * @return HierarchicalRunFlowBean对象
     */
    public static HierarchicalRunFlowBean fromEntity(RunFlowEntity entity) {
        if (entity == null) {
            return null;
        }

        HierarchicalRunFlowBean bean = new HierarchicalRunFlowBean();
        bean.setId(entity.getId());
        bean.setFlowid(entity.getFlowid());
        bean.setRunBizId(entity.getRunBizId());
        bean.setModel(entity.getModel());
        bean.setState(entity.getState());
        bean.setElapsedTime(entity.getElapsedTime());
        bean.setRet(entity.getRet());
        bean.setUpdateOrderTime(entity.getUpdateOrderTime());

        return bean;
    }

    @Override
    public String toString() {
        return "HierarchicalRunFlowBean{" +
                "id=" + id +
                ", flowid=" + flowid +
                ", runBizId=" + runBizId +
                ", model=" + model +
                ", state=" + state +
                ", elapsedTime=" + elapsedTime +
                ", ret='" + ret + '\'' +
                ", updateOrderTime=" + updateOrderTime +
                '}';
    }
}
