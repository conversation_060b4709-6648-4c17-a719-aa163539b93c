package com.ideal.envc.model.dto;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * HTML比对请求DTO
 *
 * <AUTHOR>
 */
public class HtmlComparisonRequestDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * HTML内容字符串（通过flowId查询获取，不需要前台传递）
     */
    private String htmlContent;

    /**
     * 基线服务器名称
     */
    private String baselineServer;

    /**
     * 基线设备IP
     */
    private String baseServerIp;

    /**
     * 目标服务器名称
     */
    private String targetServer;

    /**
     * 目标设备IP
     */
    private String targetServerIp;

    /**
     * 比较描述
     */
    private String description;

    /**
     * 流程ID（必填，用于查询比对内容）
     */
    @NotNull(message = "流程ID不能为空")
    private Long flowId;

    /**
     * 源路径
     */
    private String sourcePath;

    /**
     * 路径
     */
    private String path;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 源设备ID
     */
    private Long sourceComputerId;

    /**
     * 目标设备ID
     */
    private Long targetComputerId;

    /**
     * 目标中心名称
     */
    private String targetCenterName;

    /**
     * 源中心名称
     */
    private String sourceCenterName;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    public String getHtmlContent() {
        return htmlContent;
    }

    public void setHtmlContent(String htmlContent) {
        this.htmlContent = htmlContent;
    }

    public String getBaselineServer() {
        return baselineServer;
    }

    public void setBaselineServer(String baselineServer) {
        this.baselineServer = baselineServer;
    }

    public String getBaseServerIp() {
        return baseServerIp;
    }

    public void setBaseServerIp(String baseServerIp) {
        this.baseServerIp = baseServerIp;
    }

    public String getTargetServer() {
        return targetServer;
    }

    public void setTargetServer(String targetServer) {
        this.targetServer = targetServer;
    }

    public String getTargetServerIp() {
        return targetServerIp;
    }

    public void setTargetServerIp(String targetServerIp) {
        this.targetServerIp = targetServerIp;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public Long getTargetComputerId() {
        return targetComputerId;
    }

    public void setTargetComputerId(Long targetComputerId) {
        this.targetComputerId = targetComputerId;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    @Override
    public String toString() {
        return "HtmlComparisonRequestDto{" +
                "htmlContent='" + (htmlContent != null ? htmlContent.substring(0, Math.min(htmlContent.length(), 100)) + "..." : null) + '\'' +
                ", baselineServer='" + baselineServer + '\'' +
                ", baseServerIp='" + baseServerIp + '\'' +
                ", targetServer='" + targetServer + '\'' +
                ", targetServerIp='" + targetServerIp + '\'' +
                ", description='" + description + '\'' +
                ", flowId=" + flowId +
                ", sourcePath='" + sourcePath + '\'' +
                ", path='" + path + '\'' +
                ", businessSystemId=" + businessSystemId +
                ", sourceComputerId=" + sourceComputerId +
                ", targetComputerId=" + targetComputerId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", businessSystemName='" + businessSystemName + '\'' +
                '}';
    }
}
