package com.ideal.envc.model.dto;

import java.util.List;

/**
 * 规则级启动DTO
 *
 * <AUTHOR>
 */
public class RuleLevelStartDto extends StartContrastBaseDto {
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 业务系统ID
     */
    private Long systemId;

    /**
     * 节点设备ID
     */
    private Long nodeId;

    /**
     * 规则ID列表
     */
    private List<Long> ruleIds;

    /** 触发来源 */
    private Integer from;

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public List<Long> getRuleIds() {
        return ruleIds;
    }

    public void setRuleIds(List<Long> ruleIds) {
        this.ruleIds = ruleIds;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "RuleLevelStartDto{" +
                "planId=" + planId +
                ", systemId=" + systemId +
                ", nodeId=" + nodeId +
                ", ruleIds=" + ruleIds +
                ", from=" + from +
                '}';
    }
}
