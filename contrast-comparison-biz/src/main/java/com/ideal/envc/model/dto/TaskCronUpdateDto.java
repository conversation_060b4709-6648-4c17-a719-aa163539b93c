package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 任务周期表达式更新数据传输对象
 *
 * <AUTHOR>
 */
public class TaskCronUpdateDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private Long id;

    /**
     * cron表达式
     */
    private String cron;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    @Override
    public String toString() {
        return "TaskCronUpdateDto{" +
                "id=" + id +
                ", cron='" + cron + '\'' +
                '}';
    }
}
