package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 方案信息对象 ieai_envc_plan
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class PlanDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;
    /** 方案名称 */
    private String name;
    /** 方案描述 */
    private String planDesc;
    /** 创建人名称 */
    private String creatorName;
    /** 创建人ID */
    private Long creatorId;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /**  */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setName(String name){
        this.name = name;
    }

    public String getName(){
        return name;
    }

    public void setPlanDesc(String planDesc){
        this.planDesc = planDesc;
    }

    public String getPlanDesc(){
        return planDesc;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",name="+getName()+
                    ",planDesc="+getPlanDesc()+
                    ",creatorName="+getCreatorName()+
                    ",creatorId="+getCreatorId()+
                    ",createTime="+getCreateTime()+
                    ",updatorId="+getUpdatorId()+
                    ",updatorName="+getUpdatorName()+
                    ",updateTime="+getUpdateTime()+
                "]";
    }
}

