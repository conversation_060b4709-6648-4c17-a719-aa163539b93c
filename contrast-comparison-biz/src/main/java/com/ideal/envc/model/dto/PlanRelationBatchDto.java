package com.ideal.envc.model.dto;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 方案与系统关系批量保存DTO
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public class PlanRelationBatchDto {
    
    /**
     * 方案ID
     */
    @NotNull(message = "方案ID不能为空")
    private Long envcPlanId;

    /**
     * 业务系统ID列表
     */
    @NotNull(message = "业务系统ID列表不能为空")
    private List<Long> businessSystemIdList;

    public Long getEnvcPlanId() {
        return envcPlanId;
    }

    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }

    public List<Long> getBusinessSystemIdList() {
        return businessSystemIdList;
    }

    public void setBusinessSystemIdList(List<Long> businessSystemIdList) {
        this.businessSystemIdList = businessSystemIdList;
    }
} 