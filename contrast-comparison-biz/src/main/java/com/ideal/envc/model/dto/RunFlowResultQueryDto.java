package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 流程输出结果对象 ieai_envc_run_flow_result
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class RunFlowResultQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 节点规则流程主键 */
    private Long envcRunFlowId;
    /** 流程ID */
    private Long flowid;
    /** 输出内容 */
    private String content;
    /** 错误输出内容 */
    private String stderr;
    /** 结果入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date storeTime;
    /** 引擎消息时间字段 */
    private Long updateOrderTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcRunFlowId(Long envcRunFlowId){
        this.envcRunFlowId = envcRunFlowId;
    }

    public Long getEnvcRunFlowId(){
        return envcRunFlowId;
    }

    public void setFlowid(Long flowid){
        this.flowid = flowid;
    }

    public Long getFlowid(){
        return flowid;
    }

    public void setContent(String content){
        this.content = content;
    }

    public String getContent(){
        return content;
    }

    public void setStderr(String stderr){
        this.stderr = stderr;
    }

    public String getStderr(){
        return stderr;
    }

    public void setStoreTime(Date storeTime){
        this.storeTime = storeTime;
    }

    public Date getStoreTime(){
        return storeTime;
    }

    public void setUpdateOrderTime(Long updateOrderTime){
        this.updateOrderTime = updateOrderTime;
    }

    public Long getUpdateOrderTime(){
        return updateOrderTime;
    }

    @Override
    public String toString(){
        return getClass().getSimpleName()+
        " ["+
        "Hash = "+hashCode()+
            ",id="+getId()+
            ",envcRunFlowId="+getEnvcRunFlowId()+
            ",flowid="+getFlowid()+
            ",content="+getContent()+
            ",stderr="+getStderr()+
            ",storeTime="+getStoreTime()+
            ",updateOrderTime="+getUpdateOrderTime()+
        "]";
    }
}

