package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 文本行比对结果DTO
 *
 * <AUTHOR>
 */
public class TextLineComparisonDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 行号
     */
    private Integer lineNumber;

    /**
     * 比对状态：一致、不一致、缺失、多出
     */
    private String status;

    /**
     * 基线内容
     */
    private String baselineContent;

    /**
     * 目标内容
     */
    private String targetContent;

    /**
     * 差异说明
     */
    private String differenceDescription;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 基线行号
     */
    private Integer baselineLineNumber;

    /**
     * 目标行号
     */
    private Integer targetLineNumber;

    public TextLineComparisonDto() {
    }

    public TextLineComparisonDto(Integer lineNumber, String status, String baselineContent, 
                               String targetContent, String differenceDescription) {
        this.lineNumber = lineNumber;
        this.status = status;
        this.baselineContent = baselineContent;
        this.targetContent = targetContent;
        this.differenceDescription = differenceDescription;
    }

    public Integer getLineNumber() {
        return lineNumber;
    }

    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getBaselineContent() {
        return baselineContent;
    }

    public void setBaselineContent(String baselineContent) {
        this.baselineContent = baselineContent;
    }

    public String getTargetContent() {
        return targetContent;
    }

    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    public String getDifferenceDescription() {
        return differenceDescription;
    }

    public void setDifferenceDescription(String differenceDescription) {
        this.differenceDescription = differenceDescription;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getBaselineLineNumber() {
        return baselineLineNumber;
    }

    public void setBaselineLineNumber(Integer baselineLineNumber) {
        this.baselineLineNumber = baselineLineNumber;
    }

    public Integer getTargetLineNumber() {
        return targetLineNumber;
    }

    public void setTargetLineNumber(Integer targetLineNumber) {
        this.targetLineNumber = targetLineNumber;
    }

    @Override
    public String toString() {
        return "TextLineComparisonDto{" +
                "lineNumber=" + lineNumber +
                ", status='" + status + '\'' +
                ", baselineContent='" + baselineContent + '\'' +
                ", targetContent='" + targetContent + '\'' +
                ", differenceDescription='" + differenceDescription + '\'' +
                ", remark='" + remark + '\'' +
                ", baselineLineNumber=" + baselineLineNumber +
                ", targetLineNumber=" + targetLineNumber +
                '}';
    }
}
