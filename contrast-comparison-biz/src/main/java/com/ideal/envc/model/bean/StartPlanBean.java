package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 启动功能-方案信息Bean，包含方案基本信息和关联的系统列表
 *
 * <AUTHOR>
 */
public class StartPlanBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;
    /** 方案名称 */
    private String name;
    /** 关联的系统列表 */
    private List<StartSystemBean> systems;

    /** 任务ID */
    private Long taskId;
    /** 任务cron表达式 */
    private String taskCron;
    /** 任务启用状态（1：启用，0：禁用） */
    private Integer taskEnabled;
    /** 任务运行状态（0：启动，1：停止） */
    private Integer taskState;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<StartSystemBean> getSystems() {
        return systems;
    }

    public void setSystems(List<StartSystemBean> systems) {
        this.systems = systems;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getTaskCron() {
        return taskCron;
    }

    public void setTaskCron(String taskCron) {
        this.taskCron = taskCron;
    }

    public Integer getTaskEnabled() {
        return taskEnabled;
    }

    public void setTaskEnabled(Integer taskEnabled) {
        this.taskEnabled = taskEnabled;
    }

    public Integer getTaskState() {
        return taskState;
    }

    public void setTaskState(Integer taskState) {
        this.taskState = taskState;
    }

    @Override
    public String toString() {
        return "StartPlanBean{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", systems=" + systems +
                ", taskId=" + taskId +
                ", taskCron='" + taskCron + '\'' +
                ", taskEnabled=" + taskEnabled +
                ", taskState=" + taskState +
                '}';
    }
}
