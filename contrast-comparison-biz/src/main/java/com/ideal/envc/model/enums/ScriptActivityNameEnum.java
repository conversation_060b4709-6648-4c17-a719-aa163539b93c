package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 脚本执行活动的活动名称枚举
 * <AUTHOR>
 */
public enum ScriptActivityNameEnum {

    /**
     * 目录同步SSH活动
     */
    SYNC_FILE_SSH("SyncFileSSH", "目录同步SSH活动"),

    /**
     * 目录同步socket活动
     */
    SYNC_FILE_SOCKET("SyncFileSocket", "目录同步socket活动"),

    /**
     * 目录比对SSH活动
     */
    COMPARE_FILE_SSH("CompareFileSSH", "目录比对SSH活动"),

    /**
     * 目录比对socket活动
     */
    COMPARE_FILE_SOCKET("CompareFileSocket", "目录比对socket活动");

    /**
     * 活动名称代码
     */
    private final String code;

    /**
     * 活动名称描述
     */
    private final String description;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<String, ScriptActivityNameEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ScriptActivityNameEnum::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 活动名称代码
     * @param description 活动名称描述
     */
    ScriptActivityNameEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取活动名称代码
     * @return 活动名称代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取活动名称描述
     * @return 活动名称描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据活动名称代码获取枚举实例
     * @param code 活动名称代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static ScriptActivityNameEnum getByCode(String code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据活动名称代码获取活动名称描述
     * @param code 活动名称代码
     * @return 活动名称描述，如果不存在则返回"未知活动名称"
     */
    public static String getDescriptionByCode(String code) {
        return Optional.ofNullable(getByCode(code))
                .map(ScriptActivityNameEnum::getDescription)
                .orElse("未知活动名称");
    }

    /**
     * 判断给定的活动名称代码是否在枚举允许的范围内
     * @param code 活动名称代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
