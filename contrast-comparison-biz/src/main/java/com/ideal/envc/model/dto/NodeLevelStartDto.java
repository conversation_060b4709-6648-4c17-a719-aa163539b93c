package com.ideal.envc.model.dto;

import java.util.List;

/**
 * 节点级启动DTO
 *
 * <AUTHOR>
 */
public class NodeLevelStartDto extends StartContrastBaseDto {
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 业务系统ID
     */
    private Long systemId;

    /**
     * 节点设备ID列表
     */
    private List<Long> nodeIds;

    /** 触发来源 */
    private Integer from;

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public Long getSystemId() {
        return systemId;
    }

    public void setSystemId(Long systemId) {
        this.systemId = systemId;
    }

    public List<Long> getNodeIds() {
        return nodeIds;
    }

    public void setNodeIds(List<Long> nodeIds) {
        this.nodeIds = nodeIds;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "NodeLevelStartDto{" +
                "planId=" + planId +
                ", systemId=" + systemId +
                ", nodeIds=" + nodeIds +
                ", from=" + from +
                '}';
    }
}
