package com.ideal.envc.model.dto.start;

import java.io.Serializable;
import java.util.List;

/**
 * 流程监控使用dto
 * <AUTHOR>
 */
public class WorkflowLogConfigDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private long runinfoId = -1;
    private String runinfoActName = null;
    private String acttype = "";
    private List<String> logTypes;

    public long getRuninfoId() {
        return runinfoId;
    }

    public void setRuninfoId(long runinfoId) {
        this.runinfoId = runinfoId;
    }

    public String getRuninfoActName() {
        return runinfoActName;
    }

    public void setRuninfoActName(String runinfoActName) {
        this.runinfoActName = runinfoActName;
    }

    public String getActtype() {
        return acttype;
    }

    public void setActtype(String acttype) {
        this.acttype = acttype;
    }

    public List<String> getLogTypes() {
        return logTypes;
    }

    public void setLogTypes(List<String> logTypes) {
        this.logTypes = logTypes;
    }

}

