package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.List;

/**
 * 启动功能-系统信息Bean，包含系统基本信息和关联的节点关系列表
 *
 * <AUTHOR>
 */
public class StartSystemBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 业务系统ID */
    private Long businessSystemId;
    /** 业务系统名称 */
    private String businessSystemName;

    /** 关联的节点关系列表 */
    private List<StartComputerNodeBean> computerNodeBeans;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public List<StartComputerNodeBean> getComputerNodeBeans() {
        return computerNodeBeans;
    }

    public void setComputerNodeBeans(List<StartComputerNodeBean> computerNodeBeans) {
        this.computerNodeBeans = computerNodeBeans;
    }

    @Override
    public String toString() {
        return "StartSystemBean{" +
                "id=" + id +
                ", businessSystemId=" + businessSystemId +
                ", businessSystemName='" + businessSystemName + '\'' +
                ", computerNodeBeans=" + computerNodeBeans +
                '}';
    }
}
