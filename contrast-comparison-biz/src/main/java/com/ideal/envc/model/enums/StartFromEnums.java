package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 启动来源枚举
 * <AUTHOR>
 */
public enum StartFromEnums {

    /**
     * 周期触发
     */
    PERIOD_TRIGGER(1, "周期触发"),

    /**
     * 手动触发
     */
    MANUAL_TRIGGER(2, "手动触发"),

    /**
     * 重试
     */
    RETRY(3, "重试"),

    /**
     * 比对后同步
     */
    SYNC_AFTER_CONTRAST(4, "比对后同步");

    /**
     * 启动来源代码
     */
    private final Integer code;

    /**
     * 启动来源名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<Integer, StartFromEnums> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(StartFromEnums::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 启动来源代码
     * @param name 启动来源名称
     */
    StartFromEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取启动来源代码
     * @return 启动来源代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取启动来源名称
     * @return 启动来源名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据启动来源代码获取枚举实例
     * @param code 启动来源代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static StartFromEnums getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据启动来源代码获取启动来源名称
     * @param code 启动来源代码
     * @return 启动来源名称，如果不存在则返回"未知启动来源"
     */
    public static String getNameByCode(Integer code) {
        return Optional.ofNullable(getByCode(code))
                .map(StartFromEnums::getName)
                .orElse("未知启动来源");
    }

    /**
     * 判断给定的启动来源代码是否在枚举允许的范围内
     * @param code 启动来源代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
