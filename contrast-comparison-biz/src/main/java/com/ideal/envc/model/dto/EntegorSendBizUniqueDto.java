package com.ideal.envc.model.dto;

/**
 * 与引擎对接批次结果返回处理类
 * <AUTHOR>
 */
public class EntegorSendBizUniqueDto {

    private Long taskFlowId;
    private Boolean isException;

    public Long getTaskFlowId() {
        return taskFlowId;
    }

    public void setTaskFlowId(Long taskFlowId) {
        this.taskFlowId = taskFlowId;
    }

    public Boolean getIsException() {
        return isException;
    }

    public void setIsException(Boolean isException) {
        this.isException = isException;
    }


}
