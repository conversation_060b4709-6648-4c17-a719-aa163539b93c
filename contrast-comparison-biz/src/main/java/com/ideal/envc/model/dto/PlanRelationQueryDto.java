package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 方案信息对象 ieai_envc_plan_relation
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class PlanRelationQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;
    /** 方案ID */
    private Long envcPlanId;
    /** 业务系统ID */
    private Long businessSystemId;
    /** 业务系统名称 */
    private String businessSystemName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcPlanId(Long envcPlanId){
        this.envcPlanId = envcPlanId;
    }

    public Long getEnvcPlanId(){
        return envcPlanId;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setBusinessSystemName(String businessSystemName){
        this.businessSystemName = businessSystemName;
    }

    public String getBusinessSystemName(){
        return businessSystemName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
        " ["+
        "Hash = "+hashCode()+
            ",id="+getId()+
            ",envcPlanId="+getEnvcPlanId()+
            ",businessSystemId="+getBusinessSystemId()+
            ",businessSystemName="+getBusinessSystemName()+
            ",createTime="+getCreateTime()+
        "]";
    }
}

