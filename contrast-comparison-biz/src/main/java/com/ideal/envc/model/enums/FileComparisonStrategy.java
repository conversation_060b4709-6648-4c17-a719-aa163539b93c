package com.ideal.envc.model.enums;

/**
 * 文件比较策略枚举
 * 
 * <AUTHOR>
 */
public enum FileComparisonStrategy {
    
    /**
     * 仅基于MD5值比较
     * 只有MD5值不同才认为不一致
     */
    MD5_ONLY("MD5_ONLY", "仅MD5比较", "只比较MD5值，MD5相同则认为一致"),
    
    /**
     * 综合比较策略
     * 文件大小、权限、MD5任何一个不同都认为不一致
     */
    COMPREHENSIVE("COMPREHENSIVE", "综合比较", "比较文件大小、权限和MD5值，任何一个不同都认为不一致");
    
    /**
     * 策略代码
     */
    private final String code;
    
    /**
     * 策略名称
     */
    private final String name;
    
    /**
     * 策略描述
     */
    private final String description;
    
    FileComparisonStrategy(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取策略
     *
     * @param code 策略代码
     * @return 比较策略，如果找不到则返回默认策略（COMPREHENSIVE）
     */
    public static FileComparisonStrategy fromCode(String code) {
        if (code == null) {
            return COMPREHENSIVE; // 默认策略
        }

        for (FileComparisonStrategy strategy : values()) {
            if (strategy.code.equals(code)) {
                return strategy;
            }
        }

        return COMPREHENSIVE; // 默认策略
    }
    
    /**
     * 判断是否为综合比较策略
     * 
     * @return true表示综合比较，false表示仅MD5比较
     */
    public boolean isComprehensive() {
        return this == COMPREHENSIVE;
    }
    
    @Override
    public String toString() {
        return "FileComparisonStrategy{" +
                "code='" + code + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
