package com.ideal.envc.model.dto;



import java.sql.Timestamp;

/**
 * 流程监控交互数据传输对象
 * <AUTHOR>
 */
public class FlowMonitorInteractDto {
    private String projectUuid;
    private String projectName;
    private Integer projectType;
    private String flowName;
    private String flowInsName;
    private Long bizUniqueId;
    private String runServerIp;
    private Integer runServerPort;
    private Integer flowStatus;
    private Long flowId;
    private Timestamp dateTime;
    private Long updateOrderTime;
    private Long flowStartTime;
    private Long flowEndTime;

    private Integer ifFail;


    public String getProjectUuid() {
        return this.projectUuid;
    }

    public void setProjectUuid(String projectUuid) {
        this.projectUuid = projectUuid;
    }

    public String getProjectName() {
        return this.projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Integer getProjectType() {
        return this.projectType;
    }

    public void setProjectType(Integer projectType) {
        this.projectType = projectType;
    }

    public String getFlowName() {
        return this.flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public String getFlowInsName() {
        return this.flowInsName;
    }

    public void setFlowInsName(String flowInsName) {
        this.flowInsName = flowInsName;
    }

    public Long getBizUniqueId() {
        return this.bizUniqueId;
    }

    public void setBizUniqueId(Long bizUniqueId) {
        this.bizUniqueId = bizUniqueId;
    }

    public String getRunServerIp() {
        return this.runServerIp;
    }

    public void setRunServerIp(String runServerIp) {
        this.runServerIp = runServerIp;
    }

    public Integer getRunServerPort() {
        return this.runServerPort;
    }

    public void setRunServerPort(Integer runServerPort) {
        this.runServerPort = runServerPort;
    }

    public Integer getFlowStatus() {
        return this.flowStatus;
    }

    public void setFlowStatus(Integer flowStatus) {
        this.flowStatus = flowStatus;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public Timestamp getDateTime() {
        return this.dateTime;
    }

    public void setDateTime(Timestamp dateTime) {
        this.dateTime = dateTime;
    }


    public Long getUpdateOrderTime() {
        return this.updateOrderTime;
    }

    public void setUpdateOrderTime(Long updateOrderTime) {
        this.updateOrderTime = updateOrderTime;
    }

    public Long getFlowStartTime() {
        return this.flowStartTime;
    }

    public void setFlowStartTime(Long flowStartTime) {
        this.flowStartTime = flowStartTime;
    }

    public Long getFlowEndTime() {
        return this.flowEndTime;
    }

    public void setFlowEndTime(Long flowEndTime) {
        this.flowEndTime = flowEndTime;
    }


    public Integer getIfFail() {
        return ifFail;
    }

    public void setIfFail(Integer ifFail) {
        this.ifFail = ifFail;
    }

    @Override
    public String toString() {
        return "FlowMonitorInteractDto{" +
                "projectUuid='" + projectUuid + '\'' +
                ", projectName='" + projectName + '\'' +
                ", projectType=" + projectType +
                ", flowName='" + flowName + '\'' +
                ", flowInsName='" + flowInsName + '\'' +
                ", bizUniqueId=" + bizUniqueId +
                ", runServerIp='" + runServerIp + '\'' +
                ", runServerPort=" + runServerPort +
                ", flowStatus=" + flowStatus +
                ", flowId=" + flowId +
                ", dateTime=" + dateTime +
                ", updateOrderTime=" + updateOrderTime +
                ", flowStartTime=" + flowStartTime +
                ", flowEndTime=" + flowEndTime +
                ", ifFail=" + ifFail +
                '}';
    }
}
