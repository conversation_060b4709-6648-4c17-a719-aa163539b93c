package com.ideal.envc.model.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideal.envc.model.entity.RunInstanceEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 层次化实例对象，包含实例、实例详情、规则和规则同步信息
 *
 * <AUTHOR>
 */
public class HierarchicalRunInstanceBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;
    /** 方案ID */
    private Long envcPlanId;
    /** 周期任务ID（方案启动和重试无任务id） */
    private Long envcTaskId;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    /** 触发来源：（1：周期触发，2：手动触发，3：重试） */
    private Integer from;
    /** 启动人名称 */
    private String starterName;
    /** 启动人ID */
    private Long starterId;
    /** 启动时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /** 耗时 */
    private Long elapsedTime;
    
    /** 实例详情列表 */
    private List<HierarchicalRunInstanceInfoBean> instanceInfoList;
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getEnvcPlanId() {
        return envcPlanId;
    }
    
    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }
    
    public Long getEnvcTaskId() {
        return envcTaskId;
    }
    
    public void setEnvcTaskId(Long envcTaskId) {
        this.envcTaskId = envcTaskId;
    }
    
    public Integer getResult() {
        return result;
    }
    
    public void setResult(Integer result) {
        this.result = result;
    }
    
    public Integer getState() {
        return state;
    }
    
    public void setState(Integer state) {
        this.state = state;
    }
    
    public Integer getFrom() {
        return from;
    }
    
    public void setFrom(Integer from) {
        this.from = from;
    }
    
    public String getStarterName() {
        return starterName;
    }
    
    public void setStarterName(String starterName) {
        this.starterName = starterName;
    }
    
    public Long getStarterId() {
        return starterId;
    }
    
    public void setStarterId(Long starterId) {
        this.starterId = starterId;
    }
    
    public Date getStartTime() {
        return startTime;
    }
    
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }
    
    public Date getEndTime() {
        return endTime;
    }
    
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public Long getElapsedTime() {
        return elapsedTime;
    }
    
    public void setElapsedTime(Long elapsedTime) {
        this.elapsedTime = elapsedTime;
    }
    
    public List<HierarchicalRunInstanceInfoBean> getInstanceInfoList() {
        return instanceInfoList;
    }
    
    public void setInstanceInfoList(List<HierarchicalRunInstanceInfoBean> instanceInfoList) {
        this.instanceInfoList = instanceInfoList;
    }
    
    /**
     * 从RunInstanceEntity构建HierarchicalRunInstanceBean
     * 
     * @param entity RunInstanceEntity实体
     * @return HierarchicalRunInstanceBean对象
     */
    public static HierarchicalRunInstanceBean fromEntity(RunInstanceEntity entity) {
        if (entity == null) {
            return null;
        }
        
        HierarchicalRunInstanceBean bean = new HierarchicalRunInstanceBean();
        bean.setId(entity.getId());
        bean.setEnvcPlanId(entity.getEnvcPlanId());
        bean.setEnvcTaskId(entity.getEnvcTaskId());
        bean.setResult(entity.getResult());
        bean.setState(entity.getState());
        bean.setFrom(entity.getFrom());
        bean.setStarterName(entity.getStarterName());
        bean.setStarterId(entity.getStarterId());
        bean.setStartTime(entity.getStartTime());
        bean.setEndTime(entity.getEndTime());
        bean.setElapsedTime(entity.getElapsedTime());
        
        return bean;
    }
    
    @Override
    public String toString() {
        return "HierarchicalRunInstanceBean{" +
                "id=" + id +
                ", envcPlanId=" + envcPlanId +
                ", envcTaskId=" + envcTaskId +
                ", result=" + result +
                ", state=" + state +
                ", from=" + from +
                ", starterName='" + starterName + '\'' +
                ", starterId=" + starterId +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", elapsedTime=" + elapsedTime +
                ", instanceInfoList=" + (instanceInfoList != null ? instanceInfoList.size() : 0) +
                '}';
    }
}
