package com.ideal.envc.model.enums;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 操作工作流 活动  类型枚举
 * JYJ
 * <AUTHOR>
 */

public enum TaskFlowOperationTypeEnum {

    /**
     * 1:终止 killFlow
     */
    TASK_FLOW_DEFAULT(1L,"终止"),

    /**
     * 2:暂停 pauseFlow
     */
    TASK_FLOW_CHANGE(2L,"暂停"),

    /**
     * 3:恢复 resumeFlow
     */
    TASK_FLOW_MAINTENANCE(3L,"恢复"),

    /**
     * 4L:重试 retryActivity
     */
    TASK_ACTIVITY_RETRY(4L,"重试"),

    /**
     * 5L:略过 skipActivity
     */
    TASK_ACTIVITY_SKIP(5L,"略过"),

    /**
     * 6L:ut活动处理 utActivity
     */
    TASK_ACTIVITY_UT(6L,"任务操作"),
    ;

    private final Long code;
    private final String desc;

    // 新增日志记录器（需确保项目已引入SLF4J依赖）
    private static final Logger logger = LoggerFactory.getLogger(TaskFlowOperationTypeEnum.class);

    TaskFlowOperationTypeEnum(Long code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Long getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    /**
     * 校验code是否存在于枚举定义中
     * @param code 待校验的编码
     * @return 存在返回true，不存在返回false（并打印日志）
     */
    public static boolean isValidCode(Long code) {
        if (code == null) {
            logger.warn("校验的code为空，返回false");
            return false;
        }
        for (TaskFlowOperationTypeEnum type : TaskFlowOperationTypeEnum.values()) {
            if (code.equals(type.getCode())) {
                return true;
            }
        }
        logger.warn("未找到匹配的操作类型枚举，code={}", code);
        return false;
    }

    /**
     * 根据值获取枚举常量（原有方法保持不变）
     * @param code
     * @param desc
     * @return
     */
    public static TaskFlowOperationTypeEnum getColorByValue(Long code, String desc) {
        for (TaskFlowOperationTypeEnum taskOperationStatusEnum : TaskFlowOperationTypeEnum.values()) {
            if (code==null && desc==null) {
                return null;
            }
            if (code!=null && (code.equals(taskOperationStatusEnum.getCode()))) {
                    return taskOperationStatusEnum;

            }
            if (desc!=null && (desc.equals(taskOperationStatusEnum.getDesc()))) {
                return taskOperationStatusEnum;

            }

        }
        return null;
    }





}
