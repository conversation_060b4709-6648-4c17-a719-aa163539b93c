package com.ideal.envc.model.enums;

/**
 * <AUTHOR>
 */
public enum RuleModelEnum {

    /**
     * 比对
     */
    COMPARE(0, "比对"),
    /**
     * 同步
     */
    SYNC(1, "同步"),
    /**
     * 比对后同步
     */
    COMPARE_THEN_SYNC(2, "比对后同步");

    private final int code;
    private final String desc;

    RuleModelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
