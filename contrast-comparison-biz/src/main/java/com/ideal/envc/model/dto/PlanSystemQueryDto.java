package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 方案绑定系统查询条件数据传输对象
 *
 * <AUTHOR>
 */
public class PlanSystemQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 方案ID
     */
    private Long planId;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    public Long getPlanId() {
        return planId;
    }

    public void setPlanId(Long planId) {
        this.planId = planId;
    }

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    @Override
    public String toString() {
        return "PlanSystemQueryDto{" +
                "planId=" + planId +
                ", businessSystemName='" + businessSystemName + '\'' +
                '}';
    }
}
