package com.ideal.envc.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 文件比较结果DTO
 *
 * <AUTHOR>
 */
public class FileComparisonResultDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 基线服务器名称
     */
    private String baselineServer;

    /**
     * 目标服务器名称
     */
    private String targetServer;

    /**
     * 比较描述
     */
    private String description;

    /**
     * 源文件总数
     */
    private Integer totalSourceFiles;

    /**
     * 目标文件总数
     */
    private Integer totalTargetFiles;

    /**
     * 一致文件数
     */
    private Integer consistentCount;

    /**
     * 不一致文件数
     */
    private Integer inconsistentCount;

    /**
     * 缺失文件数
     */
    private Integer missingCount;

    /**
     * 多出文件数
     */
    private Integer extraCount;

    /**
     * 一致率
     */
    private BigDecimal consistentRate;

    /**
     * 不一致率
     */
    private BigDecimal inconsistentRate;

    /**
     * 缺失率
     */
    private BigDecimal missingRate;

    /**
     * 多出率
     */
    private BigDecimal extraRate;

    /**
     * 一致文件列表
     */
    private List<FileInfoDto> consistentFiles;

    /**
     * 不一致文件列表
     */
    private List<FileInfoDto> inconsistentFiles;

    /**
     * 缺失文件列表
     */
    private List<FileInfoDto> missingFiles;

    /**
     * 多出文件列表
     */
    private List<FileInfoDto> extraFiles;

    public String getBaselineServer() {
        return baselineServer;
    }

    public void setBaselineServer(String baselineServer) {
        this.baselineServer = baselineServer;
    }

    public String getTargetServer() {
        return targetServer;
    }

    public void setTargetServer(String targetServer) {
        this.targetServer = targetServer;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getTotalSourceFiles() {
        return totalSourceFiles;
    }

    public void setTotalSourceFiles(Integer totalSourceFiles) {
        this.totalSourceFiles = totalSourceFiles;
    }

    public Integer getTotalTargetFiles() {
        return totalTargetFiles;
    }

    public void setTotalTargetFiles(Integer totalTargetFiles) {
        this.totalTargetFiles = totalTargetFiles;
    }

    public Integer getConsistentCount() {
        return consistentCount;
    }

    public void setConsistentCount(Integer consistentCount) {
        this.consistentCount = consistentCount;
    }

    public Integer getInconsistentCount() {
        return inconsistentCount;
    }

    public void setInconsistentCount(Integer inconsistentCount) {
        this.inconsistentCount = inconsistentCount;
    }

    public Integer getMissingCount() {
        return missingCount;
    }

    public void setMissingCount(Integer missingCount) {
        this.missingCount = missingCount;
    }

    public Integer getExtraCount() {
        return extraCount;
    }

    public void setExtraCount(Integer extraCount) {
        this.extraCount = extraCount;
    }

    public BigDecimal getConsistentRate() {
        return consistentRate;
    }

    public void setConsistentRate(BigDecimal consistentRate) {
        this.consistentRate = consistentRate;
    }

    public BigDecimal getInconsistentRate() {
        return inconsistentRate;
    }

    public void setInconsistentRate(BigDecimal inconsistentRate) {
        this.inconsistentRate = inconsistentRate;
    }

    public BigDecimal getMissingRate() {
        return missingRate;
    }

    public void setMissingRate(BigDecimal missingRate) {
        this.missingRate = missingRate;
    }

    public BigDecimal getExtraRate() {
        return extraRate;
    }

    public void setExtraRate(BigDecimal extraRate) {
        this.extraRate = extraRate;
    }

    public List<FileInfoDto> getConsistentFiles() {
        return consistentFiles;
    }

    public void setConsistentFiles(List<FileInfoDto> consistentFiles) {
        this.consistentFiles = consistentFiles;
    }

    public List<FileInfoDto> getInconsistentFiles() {
        return inconsistentFiles;
    }

    public void setInconsistentFiles(List<FileInfoDto> inconsistentFiles) {
        this.inconsistentFiles = inconsistentFiles;
    }

    public List<FileInfoDto> getMissingFiles() {
        return missingFiles;
    }

    public void setMissingFiles(List<FileInfoDto> missingFiles) {
        this.missingFiles = missingFiles;
    }

    public List<FileInfoDto> getExtraFiles() {
        return extraFiles;
    }

    public void setExtraFiles(List<FileInfoDto> extraFiles) {
        this.extraFiles = extraFiles;
    }

    @Override
    public String toString() {
        return "FileComparisonResultDto{" +
                "baselineServer='" + baselineServer + '\'' +
                ", targetServer='" + targetServer + '\'' +
                ", description='" + description + '\'' +
                ", totalSourceFiles=" + totalSourceFiles +
                ", totalTargetFiles=" + totalTargetFiles +
                ", consistentCount=" + consistentCount +
                ", inconsistentCount=" + inconsistentCount +
                ", missingCount=" + missingCount +
                ", extraCount=" + extraCount +
                ", consistentRate=" + consistentRate +
                ", inconsistentRate=" + inconsistentRate +
                ", missingRate=" + missingRate +
                ", extraRate=" + extraRate +
                '}';
    }
}
