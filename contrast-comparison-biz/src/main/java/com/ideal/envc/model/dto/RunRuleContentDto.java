package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 节点规则内容DTO
 *
 * <AUTHOR>
 */
public class RunRuleContentDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 节点规则结果ID */
    private Long envcRunRuleId;
    /** 内容 */
    private String content;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcRunRuleId() {
        return envcRunRuleId;
    }

    public void setEnvcRunRuleId(Long envcRunRuleId) {
        this.envcRunRuleId = envcRunRuleId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "RunRuleContentDto{" +
                "id=" + id +
                ", envcRunRuleId=" + envcRunRuleId +
                ", content='" + content + '\'' +
                '}';
    }
}
