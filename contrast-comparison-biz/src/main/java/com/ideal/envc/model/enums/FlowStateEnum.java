package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 工作流状态枚举
 * <AUTHOR>
 */
public enum FlowStateEnum {

    /**
     * 运行状态
     */
    RUNNING(0, "运行"),

    /**
     * 完成状态
     */
    FINISHED(2, "完成"),

    /**
     * 终止状态
     */
    TERMINATED(4, "终止"),

    /**
     * 异常终止状态
     */
    UNEXPECTED_TERMINATED(25, "异常终止");

    /**
     * 状态代码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<Integer, FlowStateEnum> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(FlowStateEnum::getCode, Function.identity()));

    /**
     * 完成态状态映射，用于记录完成态的code和名称（包含FINISHED/TERMINATED/UNEXPECTED_TERMINATED）
     */
    private static final Map<Integer, String> COMPLETED_STATE_MAP = Arrays.stream(values())
            .filter(state -> state == FINISHED || state == TERMINATED || state == UNEXPECTED_TERMINATED)
            .collect(Collectors.toMap(FlowStateEnum::getCode, FlowStateEnum::getName));

    /**
     * 构造函数
     * @param code 状态代码
     * @param name 状态名称
     */
    FlowStateEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取状态代码
     * @return 状态代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取状态名称
     * @return 状态名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据状态代码获取枚举实例
     * @param code 状态代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static FlowStateEnum getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据状态代码获取状态名称
     * @param code 状态代码
     * @return 状态名称，如果不存在则返回"未知状态"
     */
    public static String getNameByCode(Integer code) {
        return Optional.ofNullable(getByCode(code))
                .map(FlowStateEnum::getName)
                .orElse("未知状态");
    }

    /**
     * 判断给定的状态代码是否在枚举允许的范围内
     * @param code 状态代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return code != null && CODE_MAP.containsKey(code);
    }

    /**
     * 判断传入的状态代码是否属于完成态（FINISHED/TERMINATED/UNEXPECTED_TERMINATED）
     * @param code 状态代码
     * @return 属于完成态返回true，否则返回false
     */
    public static boolean isCompletedState(Integer code) {
        return code != null && COMPLETED_STATE_MAP.containsKey(code);
    }
}
