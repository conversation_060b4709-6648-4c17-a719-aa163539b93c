package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 启动功能-节点关系信息Bean，包含节点关系基本信息和关联的规则列表
 *
 * <AUTHOR>
 */
public class StartNodeRelationBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 系统与设备节点关系ID */
    private Long envcSystemComputerNodeId;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 源中心名称 */
    private String sourceCenterName;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 目标中心名称 */
    private String targetCenterName;
    /** 源设备ID */
    private Long sourceComputerId;
    /** 源设备IP */
    private String sourceComputerIp;
    /** 源设备端口 */
    private Integer sourceComputerPort;
    /** 源设备操作系统 */
    private String sourceComputerOs;
    /** 目标设备ID */
    private Long targetComputerId;
    /** 目标设备IP */
    private String targetComputerIp;
    /** 目标设备端口 */
    private Integer targetComputerPort;
    /** 目标设备操作系统 */
    private String targetComputerOs;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建时间 */
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /** 更新时间 */
    private Date updateTime;
    
    /** 关联的规则列表 */
    private List<StartRuleBean> rules;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcSystemComputerNodeId() {
        return envcSystemComputerNodeId;
    }

    public void setEnvcSystemComputerNodeId(Long envcSystemComputerNodeId) {
        this.envcSystemComputerNodeId = envcSystemComputerNodeId;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public String getSourceComputerIp() {
        return sourceComputerIp;
    }

    public void setSourceComputerIp(String sourceComputerIp) {
        this.sourceComputerIp = sourceComputerIp;
    }

    public Integer getSourceComputerPort() {
        return sourceComputerPort;
    }

    public void setSourceComputerPort(Integer sourceComputerPort) {
        this.sourceComputerPort = sourceComputerPort;
    }

    public String getSourceComputerOs() {
        return sourceComputerOs;
    }

    public void setSourceComputerOs(String sourceComputerOs) {
        this.sourceComputerOs = sourceComputerOs;
    }

    public Long getTargetComputerId() {
        return targetComputerId;
    }

    public void setTargetComputerId(Long targetComputerId) {
        this.targetComputerId = targetComputerId;
    }

    public String getTargetComputerIp() {
        return targetComputerIp;
    }

    public void setTargetComputerIp(String targetComputerIp) {
        this.targetComputerIp = targetComputerIp;
    }

    public Integer getTargetComputerPort() {
        return targetComputerPort;
    }

    public void setTargetComputerPort(Integer targetComputerPort) {
        this.targetComputerPort = targetComputerPort;
    }

    public String getTargetComputerOs() {
        return targetComputerOs;
    }

    public void setTargetComputerOs(String targetComputerOs) {
        this.targetComputerOs = targetComputerOs;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<StartRuleBean> getRules() {
        return rules;
    }

    public void setRules(List<StartRuleBean> rules) {
        this.rules = rules;
    }

    @Override
    public String toString() {
        return "StartNodeRelationBean{" +
                "id=" + id +
                ", envcSystemComputerNodeId=" + envcSystemComputerNodeId +
                ", sourceCenterId=" + sourceCenterId +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", targetCenterId=" + targetCenterId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", sourceComputerId=" + sourceComputerId +
                ", sourceComputerIp='" + sourceComputerIp + '\'' +
                ", sourceComputerPort=" + sourceComputerPort +
                ", sourceComputerOs='" + sourceComputerOs + '\'' +
                ", targetComputerId=" + targetComputerId +
                ", targetComputerIp='" + targetComputerIp + '\'' +
                ", targetComputerPort=" + targetComputerPort +
                ", targetComputerOs='" + targetComputerOs + '\'' +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", createTime=" + createTime +
                ", updatorId=" + updatorId +
                ", updatorName='" + updatorName + '\'' +
                ", updateTime=" + updateTime +
                ", rules=" + (rules != null ? rules.size() : 0) +
                '}';
    }
}
