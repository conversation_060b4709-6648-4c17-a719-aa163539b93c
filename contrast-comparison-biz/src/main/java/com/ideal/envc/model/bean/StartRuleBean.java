package com.ideal.envc.model.bean;

import java.io.Serializable;

/**
 * 启动功能-规则信息Bean，包含规则基本信息
 *
 * <AUTHOR>
 */
public class StartRuleBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 节点关系ID */
    private Long envcNodeRelationId;
    /** 模式（0：比对，1：同步，2：比对后同步） */
    private Integer model;
    /** 模块类型（0：目录，1;文件，2：脚本） */
    private Long type;
    /** 路径 */
    private String path;
    /** 原路径 */
    private String sourcePath;
    /** 字符集 */
    private String encode;
    /** 方式（0：全部:1：部分） */
    private Integer way;
    /** 规则类型（0：匹配，1：排除） */
    private Integer ruleType;
    /** 是否有效（0：有效，1：无效） */
    private Integer enabled;
    /** 是否子集（0:是，1：否） */
    private Integer childLevel;

    /** 规则内容 */
    private StartRuleContentBean contentBean;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcNodeRelationId() {
        return envcNodeRelationId;
    }

    public void setEnvcNodeRelationId(Long envcNodeRelationId) {
        this.envcNodeRelationId = envcNodeRelationId;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getEncode() {
        return encode;
    }

    public void setEncode(String encode) {
        this.encode = encode;
    }

    public Integer getWay() {
        return way;
    }

    public void setWay(Integer way) {
        this.way = way;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getChildLevel() {
        return childLevel;
    }

    public void setChildLevel(Integer childLevel) {
        this.childLevel = childLevel;
    }



    public StartRuleContentBean getContentBean() {
        return contentBean;
    }

    public void setContentBean(StartRuleContentBean contentBean) {
        this.contentBean = contentBean;
    }

    @Override
    public String toString() {
        return "StartRuleBean{" +
                "id=" + id +
                ", envcNodeRelationId=" + envcNodeRelationId +
                ", model=" + model +
                ", type=" + type +
                ", path='" + path + '\'' +
                ", sourcePath='" + sourcePath + '\'' +
                ", encode='" + encode + '\'' +
                ", way=" + way +
                ", ruleType='" + ruleType + '\'' +
                ", enabled=" + enabled +
                ", childLevel=" + childLevel +
                ", contentBean=" + contentBean +
                '}';
    }
}
