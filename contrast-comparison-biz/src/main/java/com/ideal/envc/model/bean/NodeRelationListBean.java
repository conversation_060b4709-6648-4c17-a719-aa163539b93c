package com.ideal.envc.model.bean;

import java.io.Serializable;

/**
 * 节点关系规则列表查询结果Bean
 *
 * <AUTHOR>
 */
public class NodeRelationListBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 规则内容 */
    private String content;

    /** 节点关系ID */
    private Long envcSystemComputerNodeId;

    /** 模式（0：比对，1：同步，2：比对后同步） */
    private Integer model;

    /** 模块类型（0：目录，1：文件，2：脚本） */
    private Long type;

    /** 路径 */
    private String path;

    /** 原路径 */
    private String sourcePath;

    /** 字符集 */
    private String encode;

    /** 方式（0：全部，1：部分） */
    private Integer way;

    /** 规则类型（0：匹配，1：排除） */
    private Integer ruleType;

    /** 是否有效（0：有效，1：无效） */
    private Integer enabled;

    /** 是否子集（0：是，1：否） */
    private Integer childLevel;

    /** 创建人ID */
    private Long creatorId;

    /** 创建人名称 */
    private String creatorName;

    /** 创建时间 */
    private String createTime;

    /** 更新人ID */
    private Long updatorId;

    /** 更新人名称 */
    private String updatorName;

    /** 更新时间 */
    private String updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getEnvcSystemComputerNodeId() {
        return envcSystemComputerNodeId;
    }

    public void setEnvcSystemComputerNodeId(Long envcSystemComputerNodeId) {
        this.envcSystemComputerNodeId = envcSystemComputerNodeId;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public Long getType() {
        return type;
    }

    public void setType(Long type) {
        this.type = type;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getEncode() {
        return encode;
    }

    public void setEncode(String encode) {
        this.encode = encode;
    }

    public Integer getWay() {
        return way;
    }

    public void setWay(Integer way) {
        this.way = way;
    }

    public Integer getRuleType() {
        return ruleType;
    }

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getChildLevel() {
        return childLevel;
    }

    public void setChildLevel(Integer childLevel) {
        this.childLevel = childLevel;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }
} 