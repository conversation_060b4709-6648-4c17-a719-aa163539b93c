package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 系统与设备节点关系对象 ieai_envc_system_computer_node
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public class SystemComputerNodeDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 系统ID */
    private Long businessSystemId;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 源设备ID */
    private Long sourceComputerId;
    /** 源设备IP */
    private String sourceComputerIp;
    /** 目标设备ID */
    private Long targetComputerId;
    /** 目标设备IP */
    private String targetComputerIp;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setSourceCenterId(Long sourceCenterId){
        this.sourceCenterId = sourceCenterId;
    }

    public Long getSourceCenterId(){
        return sourceCenterId;
    }

    public void setTargetCenterId(Long targetCenterId){
        this.targetCenterId = targetCenterId;
    }

    public Long getTargetCenterId(){
        return targetCenterId;
    }

    public void setSourceComputerId(Long sourceComputerId){
        this.sourceComputerId = sourceComputerId;
    }

    public Long getSourceComputerId(){
        return sourceComputerId;
    }

    public void setSourceComputerIp(String sourceComputerIp){
        this.sourceComputerIp = sourceComputerIp;
    }

    public String getSourceComputerIp(){
        return sourceComputerIp;
    }

    public void setTargetComputerId(Long targetComputerId){
        this.targetComputerId = targetComputerId;
    }

    public Long getTargetComputerId(){
        return targetComputerId;
    }

    public void setTargetComputerIp(String targetComputerIp){
        this.targetComputerIp = targetComputerIp;
    }

    public String getTargetComputerIp(){
        return targetComputerIp;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",businessSystemId="+getBusinessSystemId()+
                    ",sourceCenterId="+getSourceCenterId()+
                    ",targetCenterId="+getTargetCenterId()+
                    ",sourceComputerId="+getSourceComputerId()+
                    ",sourceComputerIp="+getSourceComputerIp()+
                    ",targetComputerId="+getTargetComputerId()+
                    ",targetComputerIp="+getTargetComputerIp()+
                    ",creatorId="+getCreatorId()+
                    ",creatorName="+getCreatorName()+
                    ",createTime="+getCreateTime()+
                "]";
    }
}

