package com.ideal.envc.model.dto;

import cn.idev.excel.annotation.ExcelProperty;

import java.io.Serializable;

/**
 * 文件详情Excel导出DTO - 用于显示具体的文件列表
 *
 * <AUTHOR>
 */
public class FileDetailExportDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型", index = 0)
    private String type;

    /**
     * 文件路径
     */
    @ExcelProperty(value = "文件路径", index = 1)
    private String filePath;

    public FileDetailExportDto() {
    }

    public FileDetailExportDto(String type, String filePath) {
        this.type = type;
        this.filePath = filePath;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public String toString() {
        return "FileDetailExportDto{" +
                "type='" + type + '\'' +
                ", filePath='" + filePath + '\'' +
                '}';
    }
}
