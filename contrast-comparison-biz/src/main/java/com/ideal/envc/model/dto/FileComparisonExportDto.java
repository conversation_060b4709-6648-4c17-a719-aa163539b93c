package com.ideal.envc.model.dto;

import cn.idev.excel.annotation.ExcelProperty;
import cn.idev.excel.annotation.ExcelIgnore;

import java.io.Serializable;

/**
 * 文件比较Excel导出DTO - 严格按照提供的图片格式设计
 *
 * <AUTHOR>
 */
public class FileComparisonExportDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 服务器类型
     */
    @ExcelProperty(value = "服务器类型", index = 0)
    private String serverType;

    /**
     * 比对目录
     */
    @ExcelProperty(value = "比对目录", index = 1)
    private String comparisonPath;

    /**
     * IP地址
     */
    @ExcelProperty(value = "IP", index = 2)
    private String ip;

    /**
     * hostname
     */
    @ExcelProperty(value = "hostname", index = 3)
    private String hostname;

    /**
     * 汇总
     */
    @ExcelProperty(value = "汇总", index = 4)
    private Integer total;

    /**
     * 缺失
     */
    @ExcelProperty(value = "缺失", index = 5)
    private Integer missing;

    /**
     * 多出
     */
    @ExcelProperty(value = "多出", index = 6)
    private Integer extra;

    /**
     * 不一致
     */
    @ExcelProperty(value = "不一致", index = 7)
    private Integer inconsistent;

    /**
     * 一致
     */
    @ExcelProperty(value = "一致", index = 8)
    private Integer consistent;

    public FileComparisonExportDto() {
    }

    public FileComparisonExportDto(String serverType, String comparisonPath, String ip, String hostname, Integer total,
                                   Integer missing, Integer extra, Integer inconsistent, Integer consistent) {
        this.serverType = serverType;
        this.comparisonPath = comparisonPath;
        this.ip = ip;
        this.hostname = hostname;
        this.total = total;
        this.missing = missing;
        this.extra = extra;
        this.inconsistent = inconsistent;
        this.consistent = consistent;
    }

    /**
     * 兼容旧版本的构造函数（不包含比对目录）
     */
    public FileComparisonExportDto(String serverType, String ip, String hostname, Integer total,
                                   Integer missing, Integer extra, Integer inconsistent, Integer consistent) {
        this.serverType = serverType;
        this.comparisonPath = ""; // 默认为空
        this.ip = ip;
        this.hostname = hostname;
        this.total = total;
        this.missing = missing;
        this.extra = extra;
        this.inconsistent = inconsistent;
        this.consistent = consistent;
    }

    public String getServerType() {
        return serverType;
    }

    public void setServerType(String serverType) {
        this.serverType = serverType;
    }

    public String getComparisonPath() {
        return comparisonPath;
    }

    public void setComparisonPath(String comparisonPath) {
        this.comparisonPath = comparisonPath;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getMissing() {
        return missing;
    }

    public void setMissing(Integer missing) {
        this.missing = missing;
    }

    public Integer getExtra() {
        return extra;
    }

    public void setExtra(Integer extra) {
        this.extra = extra;
    }

    public Integer getInconsistent() {
        return inconsistent;
    }

    public void setInconsistent(Integer inconsistent) {
        this.inconsistent = inconsistent;
    }

    public Integer getConsistent() {
        return consistent;
    }

    public void setConsistent(Integer consistent) {
        this.consistent = consistent;
    }

    @Override
    public String toString() {
        return "FileComparisonExportDto{" +
                "serverType='" + serverType + '\'' +
                ", comparisonPath='" + comparisonPath + '\'' +
                ", ip='" + ip + '\'' +
                ", hostname='" + hostname + '\'' +
                ", total=" + total +
                ", missing=" + missing +
                ", extra=" + extra +
                ", inconsistent=" + inconsistent +
                ", consistent=" + consistent +
                '}';
    }
}
