package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class ContentCustomDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private String content;
    private boolean ret;
    private String sourceContent;
    private String targetContent;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public boolean isRet() {
        return ret;
    }

    public void setRet(boolean ret) {
        this.ret = ret;
    }

    public String getSourceContent() {
        return sourceContent;
    }

    public void setSourceContent(String sourceContent) {
        this.sourceContent = sourceContent;
    }

    public String getTargetContent() {
        return targetContent;
    }

    public void setTargetContent(String targetContent) {
        this.targetContent = targetContent;
    }

    @Override
    public String toString() {
        return "ContentCustomDto{" +
                "content='" + content + '\'' +
                ", ret=" + ret +
                ", sourceContent='" + sourceContent + '\'' +
                ", targetContent='" + targetContent + '\'' +
                '}';
    }
}
