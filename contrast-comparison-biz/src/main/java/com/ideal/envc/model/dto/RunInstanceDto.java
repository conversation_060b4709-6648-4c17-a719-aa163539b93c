package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 实例对象 ieai_envc_run_instance
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class RunInstanceDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键 */
    private Long id;
    /** 方案ID */
    private Long envcPlanId;
    /** 周期任务ID（方案启动和重试无任务id） */
    private Long envcTaskId;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    /** 触发来源：（1：周期触发，2：手动触发，3：重试） */
    private Integer from;
    /** 启动人名称 */
    private String starterName;
    /** 启动人ID */
    private Long starterId;
    /** 启动时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;
    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /** 耗时 */
    private Long elapsedTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcPlanId(Long envcPlanId){
        this.envcPlanId = envcPlanId;
    }

    public Long getEnvcPlanId(){
        return envcPlanId;
    }

    public void setEnvcTaskId(Long envcTaskId){
        this.envcTaskId = envcTaskId;
    }

    public Long getEnvcTaskId(){
        return envcTaskId;
    }

    public void setResult(Integer result){
        this.result = result;
    }

    public Integer getResult(){
        return result;
    }

    public void setState(Integer state){
        this.state = state;
    }

    public Integer getState(){
        return state;
    }

    public void setFrom(Integer from){
        this.from = from;
    }

    public Integer getFrom(){
        return from;
    }

    public void setStarterName(String starterName){
        this.starterName = starterName;
    }

    public String getStarterName(){
        return starterName;
    }

    public void setStarterId(Long starterId){
        this.starterId = starterId;
    }

    public Long getStarterId(){
        return starterId;
    }

    public void setStartTime(Date startTime){
        this.startTime = startTime;
    }

    public Date getStartTime(){
        return startTime;
    }

    public void setEndTime(Date endTime){
        this.endTime = endTime;
    }

    public Date getEndTime(){
        return endTime;
    }

    public void setElapsedTime(Long elapsedTime){
        this.elapsedTime = elapsedTime;
    }

    public Long getElapsedTime(){
        return elapsedTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",envcPlanId="+getEnvcPlanId()+
                    ",envcTaskId="+getEnvcTaskId()+
                    ",result="+getResult()+
                    ",state="+getState()+
                    ",from="+getFrom()+
                    ",starterName="+getStarterName()+
                    ",starterId="+getStarterId()+
                    ",startTime="+getStartTime()+
                    ",endTime="+getEndTime()+
                    ",elapsedTime="+getElapsedTime()+
                "]";
    }
}

