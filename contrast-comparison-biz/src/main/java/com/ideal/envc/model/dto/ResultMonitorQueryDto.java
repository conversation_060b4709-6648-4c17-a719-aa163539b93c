package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 比对结果列表查询条件数据传输对象
 *
 * <AUTHOR>
 */
public class ResultMonitorQueryDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务系统名称
     */
    private String businessSystemName;

    /**
     * 对比类型（0：比对，1：同步，2：比对后同步）
     */
    private Integer model;

    /**
     * 对比结果（-1:运行中，0:一致/成功，1：不一致/失败）
     */
    private Integer result;

    /***
     * 触发来源（1：手动触发，2：周期触发，3：重试，4：同步后触发）
     */
    private  Integer from;

    public String getBusinessSystemName() {
        return businessSystemName;
    }

    public void setBusinessSystemName(String businessSystemName) {
        this.businessSystemName = businessSystemName;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getFrom() {
        return from;
    }

    public void setFrom(Integer from) {
        this.from = from;
    }

    @Override
    public String toString() {
        return "ResultMonitorQueryDto{" +
                "businessSystemName='" + businessSystemName + '\'' +
                ", model=" + model +
                ", result=" + result +
                ", from=" + from +
                '}';
    }
}
