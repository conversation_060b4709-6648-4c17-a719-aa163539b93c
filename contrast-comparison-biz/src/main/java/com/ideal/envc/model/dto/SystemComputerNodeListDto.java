package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 系统已绑定源目标设备列表数据传输对象
 *
 * <AUTHOR>
 */
public class SystemComputerNodeListDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 源中心ID
     */
    private Long sourceCenterId;

    /**
     * 源中心名称
     */
    private String sourceCenterName;

    /**
     * 源设备IP
     */
    private String sourceComputerIp;

    /**
     * 目标中心ID
     */
    private Long targetCenterId;

    /**
     * 目标中心名称
     */
    private String targetCenterName;

    /**
     * 目标设备IP
     */
    private String targetComputerIp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getSourceComputerIp() {
        return sourceComputerIp;
    }

    public void setSourceComputerIp(String sourceComputerIp) {
        this.sourceComputerIp = sourceComputerIp;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public String getTargetComputerIp() {
        return targetComputerIp;
    }

    public void setTargetComputerIp(String targetComputerIp) {
        this.targetComputerIp = targetComputerIp;
    }

    @Override
    public String toString() {
        return "SystemComputerNodeListDto{" +
                "id=" + id +
                ", businessSystemId=" + businessSystemId +
                ", sourceCenterId=" + sourceCenterId +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", sourceComputerIp='" + sourceComputerIp + '\'' +
                ", targetCenterId=" + targetCenterId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", targetComputerIp='" + targetComputerIp + '\'' +
                '}';
    }
}
