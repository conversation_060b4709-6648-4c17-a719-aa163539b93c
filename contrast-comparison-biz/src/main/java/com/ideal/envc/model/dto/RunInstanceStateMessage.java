package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 运行实例状态消息
 * <AUTHOR>
 */
public class RunInstanceStateMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 时间戳
     */
    private Long timestamp;

    /**
     * 运行实例ID
     */
    private Long instanceId;

    /**
     * 运行实例信息ID
     */
    private Long instanceInfoId;

    /**
     * status instanceInfoId对应状态
     * @return
     */
    private int status;


    public Long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }

    public Long getInstanceId() {
        return instanceId;
    }

    public void setInstanceId(Long instanceId) {
        this.instanceId = instanceId;
    }

    public Long getInstanceInfoId() {
        return instanceInfoId;
    }

    public void setInstanceInfoId(Long instanceInfoId) {
        this.instanceInfoId = instanceInfoId;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}