package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 节点规则结果对象 ieai_envc_run_rule
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class RunRuleDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 实例详情ID */
    private Long envcRunInstanceInfoId;
    /** 模式（0：比对，1：同步，2：比对后同步） */
    private Integer model;
    /** 模块类型（0：目录，1;文件，2：脚本） */
    private Long type;
    /** 路径 */
    private String path;
    /** 原路径 */
    private String sourcePath;
    /** 字符集 */
    private String encode;
    /** 方式（0：全部:1：部分） */
    private Integer way;
    /** 规则类型（0：匹配，1：排除） */
    private Integer ruleType;
    /** 是否有效（0：有效，1：无效） */
    private Integer enabled;
    /** 是否子集（0:是，1：否） */
    private Integer childLevel;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;
    /** 耗时 */
    private Long elapsedTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcRunInstanceInfoId(Long envcRunInstanceInfoId){
        this.envcRunInstanceInfoId = envcRunInstanceInfoId;
    }

    public Long getEnvcRunInstanceInfoId(){
        return envcRunInstanceInfoId;
    }

    public void setModel(Integer model){
        this.model = model;
    }

    public Integer getModel(){
        return model;
    }

    public void setType(Long type){
        this.type = type;
    }

    public Long getType(){
        return type;
    }

    public void setPath(String path){
        this.path = path;
    }

    public String getPath(){
        return path;
    }

    public void setSourcePath(String sourcePath){
        this.sourcePath = sourcePath;
    }

    public String getSourcePath(){
        return sourcePath;
    }

    public void setEncode(String encode){
        this.encode = encode;
    }

    public String getEncode(){
        return encode;
    }

    public void setWay(Integer way){
        this.way = way;
    }

    public Integer getWay(){
        return way;
    }

    public void setRuleType(Integer ruleType){
        this.ruleType = ruleType;
    }

    public Integer getRuleType(){
        return ruleType;
    }

    public void setEnabled(Integer enabled){
        this.enabled = enabled;
    }

    public Integer getEnabled(){
        return enabled;
    }

    public void setChildLevel(Integer childLevel){
        this.childLevel = childLevel;
    }

    public Integer getChildLevel(){
        return childLevel;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setEndTime(Date endTime){
        this.endTime = endTime;
    }

    public Date getEndTime(){
        return endTime;
    }

    public void setResult(Integer result){
        this.result = result;
    }

    public Integer getResult(){
        return result;
    }

    public void setState(Integer state){
        this.state = state;
    }

    public Integer getState(){
        return state;
    }

    public void setElapsedTime(Long elapsedTime){
        this.elapsedTime = elapsedTime;
    }

    public Long getElapsedTime(){
        return elapsedTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",envcRunInstanceInfoId="+getEnvcRunInstanceInfoId()+
                    ",model="+getModel()+
                    ",type="+getType()+
                    ",path="+getPath()+
                    ",sourcePath="+getSourcePath()+
                    ",encode="+getEncode()+
                    ",way="+getWay()+
                    ",ruleType="+getRuleType()+
                    ",enabled="+getEnabled()+
                    ",childLevel="+getChildLevel()+
                    ",creatorId="+getCreatorId()+
                    ",creatorName="+getCreatorName()+
                    ",createTime="+getCreateTime()+
                    ",endTime="+getEndTime()+
                    ",result="+getResult()+
                    ",state="+getState()+
                    ",elapsedTime="+getElapsedTime()+
                "]";
    }
}

