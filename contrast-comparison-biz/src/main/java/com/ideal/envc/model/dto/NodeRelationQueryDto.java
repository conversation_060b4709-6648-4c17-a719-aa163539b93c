package com.ideal.envc.model.dto;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;

/**
 * 节点关系规则对象 ieai_envc_node_relation
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public class NodeRelationQueryDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 节点关系ID */
    private Long envcSystemComputerNodeId;
    /** 模式（0：比对，1：同步，2：比对后同步） */
    private Integer model;
    /** 模块类型（0：目录，1;文件，2：脚本） */
    private Long type;
    /** 路径 */
    private String path;
    /** 原路径 */
    private String sourcePath;
    /** 字符集 */
    private String encode;
    /** 方式（0：全部:1：部分） */
    private Integer way;
    /** 规则类型（0：匹配，1：排除） */
    private Integer ruleType;
    /** 是否有效（0：有效，1：无效） */
    private Integer enabled;
    /** 是否子集（0:是，1：否） */
    private Integer childLevel;
    /** 创建人ID */
    private Long creatorId;
    /** 创建人名称 */
    private String creatorName;
    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /** 更新人ID */
    private Long updatorId;
    /** 更新人名称 */
    private String updatorName;
    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date updateTime;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcSystemComputerNodeId(Long envcSystemComputerNodeId){
        this.envcSystemComputerNodeId = envcSystemComputerNodeId;
    }

    public Long getEnvcSystemComputerNodeId(){
        return envcSystemComputerNodeId;
    }

    public void setModel(Integer model){
        this.model = model;
    }

    public Integer getModel(){
        return model;
    }

    public void setType(Long type){
        this.type = type;
    }

    public Long getType(){
        return type;
    }

    public void setPath(String path){
        this.path = path;
    }

    public String getPath(){
        return path;
    }

    public void setSourcePath(String sourcePath) {
        this.sourcePath = sourcePath;
    }

    public String getSourcePath() {
        return sourcePath;
    }

    public void setEncode(String encode){
        this.encode = encode;
    }

    public String getEncode(){
        return encode;
    }

    public void setWay(Integer way){
        this.way = way;
    }

    public Integer getWay(){
        return way;
    }

    public void setRuleType(Integer ruleType){
        this.ruleType = ruleType;
    }

    public Integer getRuleType(){
        return ruleType;
    }

    public void setEnabled(Integer enabled){
        this.enabled = enabled;
    }

    public Integer getEnabled(){
        return enabled;
    }

    public void setChildLevel(Integer childLevel){
        this.childLevel = childLevel;
    }

    public Integer getChildLevel(){
        return childLevel;
    }

    public void setCreatorId(Long creatorId){
        this.creatorId = creatorId;
    }

    public Long getCreatorId(){
        return creatorId;
    }

    public void setCreatorName(String creatorName){
        this.creatorName = creatorName;
    }

    public String getCreatorName(){
        return creatorName;
    }

    public void setCreateTime(Date createTime){
        this.createTime = createTime;
    }

    public Date getCreateTime(){
        return createTime;
    }

    public void setUpdatorId(Long updatorId){
        this.updatorId = updatorId;
    }

    public Long getUpdatorId(){
        return updatorId;
    }

    public void setUpdatorName(String updatorName){
        this.updatorName = updatorName;
    }

    public String getUpdatorName(){
        return updatorName;
    }

    public void setUpdateTime(Date updateTime){
        this.updateTime = updateTime;
    }

    public Date getUpdateTime(){
        return updateTime;
    }


    @Override
    public String toString(){
        return getClass().getSimpleName()+
        " ["+
        "Hash = "+hashCode()+
            ",id="+getId()+
            ",envcSystemComputerNodeId="+getEnvcSystemComputerNodeId()+
            ",model="+getModel()+
            ",type="+getType()+
            ",path="+getPath()+
            ",sourcePath="+getSourcePath()+
            ",encode="+getEncode()+
            ",way="+getWay()+
            ",ruleType="+getRuleType()+
            ",enabled="+getEnabled()+
            ",childLevel="+getChildLevel()+
            ",creatorId="+getCreatorId()+
            ",creatorName="+getCreatorName()+
            ",createTime="+getCreateTime()+
            ",updatorId="+getUpdatorId()+
            ",updatorName="+getUpdatorName()+
            ",updateTime="+getUpdateTime()+
        "]";
    }
}

