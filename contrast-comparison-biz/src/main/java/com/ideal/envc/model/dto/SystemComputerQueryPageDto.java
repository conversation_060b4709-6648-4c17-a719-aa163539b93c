package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 系统设备分页查询数据传输对象
 *
 * <AUTHOR>
 */
public class SystemComputerQueryPageDto  implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 系统ID */
    private Long businessSystemId;
    /** 设备ID */
    private Long sourceComputerId;
    /** 代理IP */
    private Long sourceCenterId;
    /** 代理名称 */
    private Long targetCenterId ;

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }
}
