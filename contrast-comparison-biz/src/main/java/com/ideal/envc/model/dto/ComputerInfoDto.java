package com.ideal.envc.model.dto;

import java.io.Serializable;

/**
 * 设备信息数据传输对象
 *
 * <AUTHOR>
 */
public class ComputerInfoDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 端口
     */
    private Integer agentPort;

    /**
     * 操作系统名称
     */
    private String osName;

    public Integer getAgentPort() {
        return agentPort;
    }

    public void setAgentPort(Integer agentPort) {
        this.agentPort = agentPort;
    }

    public String getOsName() {
        return osName;
    }

    public void setOsName(String osName) {
        this.osName = osName;
    }

    @Override
    public String toString() {
        return "ComputerInfoDto{" +
                "agentPort=" + agentPort +
                ", osName='" + osName + '\'' +
                '}';
    }
}
