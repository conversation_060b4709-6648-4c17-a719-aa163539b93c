package com.ideal.envc.model.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 系统设备节点批量绑定数据传输对象
 *
 * <AUTHOR>
 */
public class SystemComputerNodeBatchDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务系统ID
     */
    private Long businessSystemId;

    /**
     * 源中心ID
     */
    private Long sourceCenterId;

    /**
     * 目标中心ID
     */
    private Long targetCenterId;

    /**
     * 源设备ID
     */
    private Long sourceComputerId;

    /**
     * 目标设备ID集合
     */
    private List<Long> targetComputerIdList;

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public List<Long> getTargetComputerIdList() {
        return targetComputerIdList;
    }

    public void setTargetComputerIdList(List<Long> targetComputerIdList) {
        this.targetComputerIdList = targetComputerIdList;
    }
} 