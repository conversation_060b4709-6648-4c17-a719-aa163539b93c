package com.ideal.envc.model.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 任务操作类型枚举
 * <AUTHOR>
 */
public enum TaskOperateEnums {

    /**
     * 启动操作
     */
    START(0, "启动"),

    /**
     * 停止操作
     */
    STOP(1, "停止");

    /**
     * 操作类型代码
     */
    private final Integer code;

    /**
     * 操作类型名称
     */
    private final String name;

    /**
     * 枚举值映射，用于根据code快速查找枚举
     */
    private static final Map<Integer, TaskOperateEnums> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(TaskOperateEnums::getCode, Function.identity()));

    /**
     * 构造函数
     * @param code 操作类型代码
     * @param name 操作类型名称
     */
    TaskOperateEnums(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取操作类型代码
     * @return 操作类型代码
     */
    public Integer getCode() {
        return code;
    }

    /**
     * 获取操作类型名称
     * @return 操作类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据操作类型代码获取枚举实例
     * @param code 操作类型代码
     * @return 枚举实例，如果不存在则返回null
     */
    public static TaskOperateEnums getByCode(Integer code) {
        return code == null ? null : CODE_MAP.get(code);
    }

    /**
     * 根据操作类型代码获取操作名称
     * @param code 操作类型代码
     * @return 操作名称，如果不存在则返回"未知操作"
     */
    public static String getNameByCode(Integer code) {
        return Optional.ofNullable(getByCode(code))
                .map(TaskOperateEnums::getName)
                .orElse("未知操作");
    }

    /**
     * 判断给定的操作类型代码是否在枚举允许的范围内
     * @param code 操作类型代码
     * @return 如果在允许的范围内返回true，否则返回false
     */
    public static boolean isValidCode(Integer code) {
        return code != null && CODE_MAP.containsKey(code);
    }
}
