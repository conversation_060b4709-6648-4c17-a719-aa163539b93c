package com.ideal.envc.model.dto.start;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 启动实例详情对象 ieai_envc_run_instance_info
 *
 * <AUTHOR>
 */
public class StartSmallRunInstanceInfoDto implements Serializable {
    private static final long serialVersionUID=1L;

    /** 主键ID */
    private Long id;
    /** 实例ID */
    private Long envcRunInstanceId;
    /** 方案ID */
    private Long envcPlanId;
    /** 系统ID */
    private Long businessSystemId;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 源设备ID */
    private Long sourceComputerId;
    /** 源设备IP */
    private String sourceComputerIp;
    /** 源设备端口 */
    private Integer sourceComputerPort;
    /** 源设备操作系统 */
    private String sourceComputerOs;
    /** 目标设备ID */
    private Long targetComputerId;
    /** 目标设备IP */
    private String targetComputerIp;
    /** 目标设备端口 */
    private Integer targetComputerPort;
    /** 目标设备操作系统 */
    private String targetComputerOs;
    /** 存储时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date storeTime;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;

    private List<StartSmallRunRuleDto> rules;

    public void setId(Long id){
        this.id = id;
    }

    public Long getId(){
        return id;
    }

    public void setEnvcRunInstanceId(Long envcRunInstanceId){
        this.envcRunInstanceId = envcRunInstanceId;
    }

    public Long getEnvcRunInstanceId(){
        return envcRunInstanceId;
    }

    public void setEnvcPlanId(Long envcPlanId){
        this.envcPlanId = envcPlanId;
    }

    public Long getEnvcPlanId(){
        return envcPlanId;
    }

    public void setBusinessSystemId(Long businessSystemId){
        this.businessSystemId = businessSystemId;
    }

    public Long getBusinessSystemId(){
        return businessSystemId;
    }

    public void setSourceCenterId(Long sourceCenterId){
        this.sourceCenterId = sourceCenterId;
    }

    public Long getSourceCenterId(){
        return sourceCenterId;
    }

    public void setTargetCenterId(Long targetCenterId){
        this.targetCenterId = targetCenterId;
    }

    public Long getTargetCenterId(){
        return targetCenterId;
    }

    public void setSourceComputerId(Long sourceComputerId){
        this.sourceComputerId = sourceComputerId;
    }

    public Long getSourceComputerId(){
        return sourceComputerId;
    }

    public void setSourceComputerIp(String sourceComputerIp){
        this.sourceComputerIp = sourceComputerIp;
    }

    public String getSourceComputerIp(){
        return sourceComputerIp;
    }

    public void setSourceComputerPort(Integer sourceComputerPort){
        this.sourceComputerPort = sourceComputerPort;
    }

    public Integer getSourceComputerPort(){
        return sourceComputerPort;
    }

    public void setTargetComputerId(Long targetComputerId){
        this.targetComputerId = targetComputerId;
    }

    public Long getTargetComputerId(){
        return targetComputerId;
    }

    public void setTargetComputerIp(String targetComputerIp){
        this.targetComputerIp = targetComputerIp;
    }

    public String getTargetComputerIp(){
        return targetComputerIp;
    }

    public void setTargetComputerPort(Integer targetComputerPort){
        this.targetComputerPort = targetComputerPort;
    }

    public Integer getTargetComputerPort(){
        return targetComputerPort;
    }

    public void setSourceComputerOs(String sourceComputerOs){
        this.sourceComputerOs = sourceComputerOs;
    }

    public String getSourceComputerOs(){
        return sourceComputerOs;
    }

    public void setTargetComputerOs(String targetComputerOs){
        this.targetComputerOs = targetComputerOs;
    }

    public String getTargetComputerOs(){
        return targetComputerOs;
    }

    public void setStoreTime(Date storeTime){
        this.storeTime = storeTime;
    }

    public Date getStoreTime(){
        return storeTime;
    }

    public void setResult(Integer result){
        this.result = result;
    }

    public Integer getResult(){
        return result;
    }

    public void setState(Integer state){
        this.state = state;
    }

    public Integer getState(){
        return state;
    }

    public List<StartSmallRunRuleDto> getRules() {
        return rules;
    }

    public void setRules(List<StartSmallRunRuleDto> rules) {
        this.rules = rules;
    }

    @Override
    public String toString(){
        return getClass().getSimpleName()+
                " ["+
                "Hash = "+hashCode()+
                    ",id="+getId()+
                    ",envcRunInstanceId="+getEnvcRunInstanceId()+
                    ",envcPlanId="+getEnvcPlanId()+
                    ",businessSystemId="+getBusinessSystemId()+
                    ",sourceCenterId="+getSourceCenterId()+
                    ",targetCenterId="+getTargetCenterId()+
                    ",sourceComputerId="+getSourceComputerId()+
                    ",sourceComputerIp="+getSourceComputerIp()+
                    ",sourceComputerPort="+getSourceComputerPort()+
                    ",targetComputerId="+getTargetComputerId()+
                    ",targetComputerIp="+getTargetComputerIp()+
                    ",targetComputerPort="+getTargetComputerPort()+
                    ",sourceComputerOs="+getSourceComputerOs()+
                    ",targetComputerOs="+getTargetComputerOs()+
                    ",storeTime="+getStoreTime()+
                    ",result="+getResult()+
                    ",state="+getState()+
                "]";
    }
}

