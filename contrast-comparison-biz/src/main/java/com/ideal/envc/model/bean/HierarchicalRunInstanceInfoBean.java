package com.ideal.envc.model.bean;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 层次化实例详情对象
 *
 * <AUTHOR>
 */
public class HierarchicalRunInstanceInfoBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;
    /** 实例ID */
    private Long envcRunInstanceId;
    /** 方案ID */
    private Long envcPlanId;
    /** 系统ID */
    private Long businessSystemId;
    /** 源中心ID */
    private Long sourceCenterId;
    /** 源中心名称 */
    private String sourceCenterName;
    /** 目标中心ID */
    private Long targetCenterId;
    /** 目标中心名称 */
    private String targetCenterName;
    /** 源设备ID */
    private Long sourceComputerId;
    /** 源设备IP */
    private String sourceComputerIp;
    /** 源设备端口 */
    private Integer sourceComputerPort;
    /** 源设备操作系统 */
    private String sourceComputerOs;
    /** 目标设备ID */
    private Long targetComputerId;
    /** 目标设备IP */
    private String targetComputerIp;
    /** 目标设备端口 */
    private Integer targetComputerPort;
    /** 目标设备操作系统 */
    private String targetComputerOs;
    /** 存储时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date storeTime;
    /** 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） */
    private Integer result;
    /** 启停状态（0：运行中，1：已完成，2：终止） */
    private Integer state;

    /** 规则列表 */
    private List<HierarchicalRunRuleBean> ruleList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcRunInstanceId() {
        return envcRunInstanceId;
    }

    public void setEnvcRunInstanceId(Long envcRunInstanceId) {
        this.envcRunInstanceId = envcRunInstanceId;
    }

    public Long getEnvcPlanId() {
        return envcPlanId;
    }

    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }

    public Long getBusinessSystemId() {
        return businessSystemId;
    }

    public void setBusinessSystemId(Long businessSystemId) {
        this.businessSystemId = businessSystemId;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public Long getSourceComputerId() {
        return sourceComputerId;
    }

    public void setSourceComputerId(Long sourceComputerId) {
        this.sourceComputerId = sourceComputerId;
    }

    public String getSourceComputerIp() {
        return sourceComputerIp;
    }

    public void setSourceComputerIp(String sourceComputerIp) {
        this.sourceComputerIp = sourceComputerIp;
    }

    public Integer getSourceComputerPort() {
        return sourceComputerPort;
    }

    public void setSourceComputerPort(Integer sourceComputerPort) {
        this.sourceComputerPort = sourceComputerPort;
    }

    public String getSourceComputerOs() {
        return sourceComputerOs;
    }

    public void setSourceComputerOs(String sourceComputerOs) {
        this.sourceComputerOs = sourceComputerOs;
    }

    public Long getTargetComputerId() {
        return targetComputerId;
    }

    public void setTargetComputerId(Long targetComputerId) {
        this.targetComputerId = targetComputerId;
    }

    public String getTargetComputerIp() {
        return targetComputerIp;
    }

    public void setTargetComputerIp(String targetComputerIp) {
        this.targetComputerIp = targetComputerIp;
    }

    public Integer getTargetComputerPort() {
        return targetComputerPort;
    }

    public void setTargetComputerPort(Integer targetComputerPort) {
        this.targetComputerPort = targetComputerPort;
    }

    public String getTargetComputerOs() {
        return targetComputerOs;
    }

    public void setTargetComputerOs(String targetComputerOs) {
        this.targetComputerOs = targetComputerOs;
    }

    public Date getStoreTime() {
        return storeTime;
    }

    public void setStoreTime(Date storeTime) {
        this.storeTime = storeTime;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public List<HierarchicalRunRuleBean> getRuleList() {
        return ruleList;
    }

    public void setRuleList(List<HierarchicalRunRuleBean> ruleList) {
        this.ruleList = ruleList;
    }

    /**
     * 从RunInstanceInfoEntity构建HierarchicalRunInstanceInfoBean
     *
     * @param entity RunInstanceInfoEntity实体
     * @return HierarchicalRunInstanceInfoBean对象
     */
    public static HierarchicalRunInstanceInfoBean fromEntity(RunInstanceInfoEntity entity) {
        if (entity == null) {
            return null;
        }

        HierarchicalRunInstanceInfoBean bean = new HierarchicalRunInstanceInfoBean();
        bean.setId(entity.getId());
        bean.setEnvcRunInstanceId(entity.getEnvcRunInstanceId());
        bean.setEnvcPlanId(entity.getEnvcPlanId());
        bean.setBusinessSystemId(entity.getBusinessSystemId());
        bean.setSourceCenterId(entity.getSourceCenterId());
        bean.setSourceCenterName(entity.getSourceCenterName());
        bean.setTargetCenterId(entity.getTargetCenterId());
        bean.setTargetCenterName(entity.getTargetCenterName());
        bean.setSourceComputerId(entity.getSourceComputerId());
        bean.setSourceComputerIp(entity.getSourceComputerIp());
        bean.setSourceComputerPort(entity.getSourceComputerPort());
        bean.setSourceComputerOs(entity.getSourceComputerOs());
        bean.setTargetComputerId(entity.getTargetComputerId());
        bean.setTargetComputerIp(entity.getTargetComputerIp());
        bean.setTargetComputerPort(entity.getTargetComputerPort());
        bean.setTargetComputerOs(entity.getTargetComputerOs());
        bean.setStoreTime(entity.getStoreTime());
        bean.setResult(entity.getResult());
        bean.setState(entity.getState());

        return bean;
    }

    @Override
    public String toString() {
        return "HierarchicalRunInstanceInfoBean{" +
                "id=" + id +
                ", envcRunInstanceId=" + envcRunInstanceId +
                ", envcPlanId=" + envcPlanId +
                ", businessSystemId=" + businessSystemId +
                ", sourceCenterId=" + sourceCenterId +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", targetCenterId=" + targetCenterId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", sourceComputerId=" + sourceComputerId +
                ", sourceComputerIp='" + sourceComputerIp + '\'' +
                ", sourceComputerPort=" + sourceComputerPort +
                ", sourceComputerOs='" + sourceComputerOs + '\'' +
                ", targetComputerId=" + targetComputerId +
                ", targetComputerIp='" + targetComputerIp + '\'' +
                ", targetComputerPort=" + targetComputerPort +
                ", targetComputerOs='" + targetComputerOs + '\'' +
                ", storeTime=" + storeTime +
                ", result=" + result +
                ", state=" + state +
                ", ruleList=" + (ruleList != null ? ruleList.size() : 0) +
                '}';
    }
}
