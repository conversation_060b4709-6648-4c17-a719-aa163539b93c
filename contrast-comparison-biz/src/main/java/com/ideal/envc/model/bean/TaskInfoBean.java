package com.ideal.envc.model.bean;

import java.io.Serializable;
import java.util.Date;

/**
 * 任务信息Bean
 *
 * <AUTHOR>
 */
public class TaskInfoBean implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 方案ID
     */
    private Long envcPlanId;

    /**
     * 方案名称
     */
    private String envcPlanName;

    /**
     * 周期表达式
     */
    private String cron;

    /**
     * 是否启用（1：启用，0：禁用）
     */
    private Integer enabled;

    /**
     * 启停状态（0：启动，1：停止）
     */
    private Integer state;

    /**
     * 源中心ID
     */
    private Long sourceCenterId;

    /**
     * 源中心名称
     */
    private String sourceCenterName;

    /**
     * 目标中心ID
     */
    private Long targetCenterId;

    /**
     * 目标中心名称
     */
    private String targetCenterName;

    /**
     * 定时ID
     */
    private Long scheduledId;

    /**
     * 创建人ID
     */
    private Long creatorId;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人ID
     */
    private Long updatorId;

    /**
     * 更新人名称
     */
    private String updatorName;

    /**
     * 更新时间
     */
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEnvcPlanId() {
        return envcPlanId;
    }

    public void setEnvcPlanId(Long envcPlanId) {
        this.envcPlanId = envcPlanId;
    }

    public String getEnvcPlanName() {
        return envcPlanName;
    }

    public void setEnvcPlanName(String envcPlanName) {
        this.envcPlanName = envcPlanName;
    }

    public String getCron() {
        return cron;
    }

    public void setCron(String cron) {
        this.cron = cron;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getSourceCenterId() {
        return sourceCenterId;
    }

    public void setSourceCenterId(Long sourceCenterId) {
        this.sourceCenterId = sourceCenterId;
    }

    public String getSourceCenterName() {
        return sourceCenterName;
    }

    public void setSourceCenterName(String sourceCenterName) {
        this.sourceCenterName = sourceCenterName;
    }

    public Long getTargetCenterId() {
        return targetCenterId;
    }

    public void setTargetCenterId(Long targetCenterId) {
        this.targetCenterId = targetCenterId;
    }

    public String getTargetCenterName() {
        return targetCenterName;
    }

    public void setTargetCenterName(String targetCenterName) {
        this.targetCenterName = targetCenterName;
    }

    public Long getScheduledId() {
        return scheduledId;
    }

    public void setScheduledId(Long scheduledId) {
        this.scheduledId = scheduledId;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdatorId() {
        return updatorId;
    }

    public void setUpdatorId(Long updatorId) {
        this.updatorId = updatorId;
    }

    public String getUpdatorName() {
        return updatorName;
    }

    public void setUpdatorName(String updatorName) {
        this.updatorName = updatorName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "TaskInfoBean{" +
                "id=" + id +
                ", envcPlanId=" + envcPlanId +
                ", envcPlanName='" + envcPlanName + '\'' +
                ", cron='" + cron + '\'' +
                ", enabled=" + enabled +
                ", state=" + state +
                ", sourceCenterId=" + sourceCenterId +
                ", sourceCenterName='" + sourceCenterName + '\'' +
                ", targetCenterId=" + targetCenterId +
                ", targetCenterName='" + targetCenterName + '\'' +
                ", scheduledId=" + scheduledId +
                ", creatorId=" + creatorId +
                ", creatorName='" + creatorName + '\'' +
                ", createTime=" + createTime +
                ", updatorId=" + updatorId +
                ", updatorName='" + updatorName + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }
}
