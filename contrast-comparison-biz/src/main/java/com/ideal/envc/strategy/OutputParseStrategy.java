package com.ideal.envc.strategy;

import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.OutputParseResult;
import java.util.List;

/**
 * 输出解析策略接口
 * <AUTHOR>
 */
public interface OutputParseStrategy {
    
    /**
     * 解析输出
     * @param actOutputs 活动输出列表
     * @param actDefName 活动类型
     * @return 解析结果
     */
    OutputParseResult parse(List<EngineActOutputBean> actOutputs,String actDefName);
    
    /**
     * 获取策略类型
     * @return 策略类型
     */
    String getType();
} 