package com.ideal.envc.exception;


/**
 * xxJob异常
 * <AUTHOR> lch
 */
public class ScheduleJobOperateException extends Exception {

    public ScheduleJobOperateException(String message) {
        super(message);
    }

    public ScheduleJobOperateException(String code, String message) {
        super(code+message);
    }

    public ScheduleJobOperateException(String code, String message, Throwable cause) {
        super(code+message, cause);
    }

    public ScheduleJobOperateException(String message, Exception e) {
        super(message, e);
    }
    public ScheduleJobOperateException(Exception e) {
        super(e);
    }

    public ScheduleJobOperateException(Throwable e) {
        super(e);
    }

    public ScheduleJobOperateException(NoSuchMethodException e) {
        super(e);
    }
}
