package com.ideal.envc.exception;

/**
 * <AUTHOR>
 */
public class EngineServiceException extends Exception {

    public EngineServiceException(String message) {
        super(message);
    }

    public EngineServiceException(String code, String message) {
        super(String.format("[%s] %s", code, message));
    }

    public EngineServiceException(String code, String message, Throwable cause) {
        super(String.format("[%s] %s", code, message),cause);
    }

    public EngineServiceException(String message, Exception e) {
        super(message, e);
    }
    public EngineServiceException(Exception e) {
        super(e);
    }

    public EngineServiceException(Throwable e) {
        super(e);
    }

    public EngineServiceException(NoSuchMethodException e) {
        super(e);
    }
}
