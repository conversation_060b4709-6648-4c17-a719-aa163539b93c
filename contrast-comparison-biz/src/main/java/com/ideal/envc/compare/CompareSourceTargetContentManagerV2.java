package com.ideal.envc.compare;

import com.alibaba.fastjson2.JSON;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.ChangeDelta;
import com.github.difflib.patch.Chunk;
import com.github.difflib.patch.DeleteDelta;
import com.github.difflib.patch.InsertDelta;
import com.github.difflib.patch.Patch;
import com.github.difflib.text.DiffRow;
import com.github.difflib.text.DiffRowGenerator;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * CompareSourceTargetContentManager升级版本
 * 基于java-diff-utils 4.12版本实现
 * 保持与原版完全相同的输入输出接口和业务逻辑
 * 
 * <AUTHOR>
 */
public class CompareSourceTargetContentManagerV2 {
    // 格式验证和文件名提取的正则表达式（与原版保持一致）
    private static final Pattern FORMAT_PATTERN = Pattern.compile(
        "^.+\\s+\\(size:\\s+[\\d.]+\\s*[KMGT]?B?" +
        "(?:,\\s*permissions:\\s+[rwx-]+,\\s*MD5:\\s+[a-fA-F0-9]+)?\\)$"
    );
    private static final Pattern FILENAME_PATTERN = Pattern.compile("^(.*?)\\s+\\(size: ");

    // 结果常量（与原版保持一致）
    public static final String RESULTEQUAL = "EQUAL";
    public static final String RESULTCHANGE = "CHANGE";
    public static final String RESULTINSERT = "INSERT";
    public static final String RESULTDELETE = "DELETE";
    public static final String COMPARERESULT = "compareResult";
    public static final String SOURCECONTENT = "sourceContent";
    public static final String TARGETCONTENT = "targetContent";
    public static final String WORKFLOWID = "workFlowId";
    public static final String SOURCETYPE = "sourceActType";
    public static final String TARGETTYPE = "targetActType";
    public static final String CALLFLOW = "CallFlow";

    /**
     * 核心比对方法
     * 与原版CompareSourceTargetContentManager.compare()方法保持完全相同的接口和输出格式
     * 
     * @param aLines 源文件行列表
     * @param bLines 目标文件行列表
     * @return 比对结果Map，包含compareResult(HTML)和ret(boolean)
     */
    public Map<String, Object> compare(List<String> aLines, List<String> bLines) {
        Map<String, Object> outputs = new HashMap<>();
        StringBuilder result = new StringBuilder();
        StringBuilder compareResult = new StringBuilder();
        
        // 生成HTML表格头部（与原版完全一致）
        compareResult.append("<div class=\"comparison_space\">"
                + "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" class=\"comparison_tab\">"
                + "<tr class=\"cp_title\"><td><span>Source</span></td><td width=\"5\"></td>"
                + "<td class=\"cp_line\"></td><td width=\"5\"></td><td><span>Target</span></td></tr>");
        
        boolean ret = true;
        
        // 行数限制检查（与原版保持一致）
        if (aLines.size() > 10000) {
            result.append("The number of lines in the source file exceeds 10,000!!!");
            outputs.put(COMPARERESULT, result.toString());
            outputs.put("ret", false);
            return outputs;
        }
        if (bLines.size() > 10000) {
            result.append("The number of lines in the target file exceeds 10,000!!!");
            outputs.put(COMPARERESULT, result.toString());
            outputs.put("ret", false);
            return outputs;
        }

        // 使用新版本API创建DiffRowGenerator
        DiffRowGenerator.Builder builder = DiffRowGenerator.create();
        DiffRowGenerator dfg = builder.build();

        // 根据格式生成不同的Patch（保持原版逻辑）
        Patch<String> patch;
        boolean isFormatValid = isAllLinesValidFormat(aLines) && isAllLinesValidFormat(bLines);
        if (isFormatValid) {
            // 符合格式：使用自定义Patch（按文件名比对）
            patch = generateCustomFileNamePatch(aLines, bLines);
        } else {
            // 不符合格式：使用默认Patch
            patch = DiffUtils.diff(aLines, bLines);
        }
        
        // 生成差异行
        List<DiffRow> rows = dfg.generateDiffRows(aLines, patch);
        int rowsSize = rows.size();
        int insertSize = 0;
        int deleteSize = 0;
        int oldSize = aLines.size();
        int newSize = bLines.size();
        
        // 处理每一行差异（保持原版逻辑）
        for (int i = 0; i < rowsSize; i++) {
            DiffRow diffRow = rows.get(i);
            String tag = diffRow.getTag().toString();
            DiffRow.Tag tag1 = diffRow.getTag();
            String oldLine = diffRow.getOldLine();
            String newLine = diffRow.getNewLine();
            oldLine = getReplace(oldLine);
            newLine = getReplace(newLine);
            String oldNum = String.valueOf(i + 1 - insertSize);
            String newNum = String.valueOf(i + 1 - deleteSize);
            
            // 行号计算逻辑（与原版完全一致）
            if ((i + 1 - insertSize) > oldSize) {
                ret = false;
                tag1 = DiffRow.Tag.INSERT;
                tag = RESULTINSERT;
                insertSize++;
                oldNum = "";
                newNum = String.valueOf(i + 1 - deleteSize);
            } else if ((i + 1 - deleteSize) > newSize) {
                ret = false;
                tag1 = DiffRow.Tag.DELETE;
                tag = RESULTDELETE;
                deleteSize++;
                oldNum = String.valueOf(i + 1 - insertSize);
                newNum = "";
            } else if (RESULTCHANGE.equals(tag)) {
                ret = false;
                if ((i + 1 - insertSize) > oldSize || (oldLine != null && oldLine.length() == 0
                        && !oldLine.equals(aLines.get(i - insertSize)))) {
                    tag1 = DiffRow.Tag.INSERT;
                    tag = RESULTINSERT;
                    insertSize++;
                    oldNum = "";
                    newNum = String.valueOf(i + 1 - deleteSize);
                } else if ((i + 1 - deleteSize) > newSize
                        || newLine != null && newLine.length() == 0 && !newLine.equals(bLines.get(i - deleteSize))) {
                    tag1 = DiffRow.Tag.DELETE;
                    tag = RESULTDELETE;
                    deleteSize++;
                    oldNum = String.valueOf(i + 1 - insertSize);
                    newNum = "";
                }
            } else if (RESULTINSERT.equals(tag)) {
                insertSize++;
                ret = false;
                oldNum = "";
                newNum = String.valueOf(i + 1 - deleteSize);
            } else if (RESULTDELETE.equals(tag)) {
                deleteSize++;
                ret = false;
                oldNum = String.valueOf(i + 1 - insertSize);
                newNum = "";
            }
            diffRow.setTag(tag1);
            initResultStr(tag, compareResult, oldNum, oldLine, newNum, newLine);
        }
        
        compareResult.append("</table></div>");
        outputs.put(COMPARERESULT, compareResult.toString());
        outputs.put("ret", ret);
        return outputs;
    }

    // 格式验证方法（与原版保持一致）
    private boolean isAllLinesValidFormat(List<String> lines) {
        if (lines == null || lines.isEmpty()) {
            return true;
        }
        for (String line : lines) {
            if (line == null || !FORMAT_PATTERN.matcher(line).matches()) {
                return false;
            }
        }
        return true;
    }

    /**
     * HTML结果字符串生成方法（与原版完全一致）
     */
    public void initResultStr(String tag, StringBuilder compareResult, String oldNum, String oldLine, String newNum,
                              String newLine) {
        String str1 = "<tr height=\"5\"><td></td><td width=\"5\"></td><td class=\"cp_line\"></td><td width=\"5\"></td><td></td></tr><tr>";
        String str11 = "<tr height=\"5\" class=\"cps_tr1\"><td></td><td width=\"5\"></td><td class=\"cp_line\"></td><td width=\"5\"></td><td></td></tr>";
        String str2 = "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr>";
        String str3 = "</div></td><td><div class=\"cp_cn\">";
        String str4 = "</div></td></tr></table></td><td width=\"5\"></td><td class=\"cp_line\"></td>";
        String str5 = "<td><div class=\"placeholder\"></div></td><td><div class=\"cp_text\">";
        if (RESULTDELETE.equals(tag)) {
            compareResult.append(str1 + "<td class=\"cp_frame abnormal cpi_td_w\">" + str2
                    + "<td><div class=\"cp_icon icon_pos\"></div></td><td><div class=\"cp_text\">" + oldNum + str3
                    + oldLine + str4 + "<td width=\"5\"></td><td class=\"cp_frame cpi_td_w\">" + str2 + str5 + newNum
                    + str3 + newLine);
        } else if (RESULTINSERT.equals(tag)) {
            compareResult.append(str1 + "<td class=\"cp_frame cpi_td_w\">" + str2 + str5 + oldNum + str3 + oldLine
                    + str4 + "<td width=\"5\"></td><td class=\"cp_frame complete cpi_td_w\">" + str2
                    + "<td><div class=\"cp_icon icon_pos2\"></div></td><td><div class=\"cp_text\">" + newNum + str3
                    + newLine);
        } else if (RESULTCHANGE.equals(tag)) {
            compareResult.append(str1 + "<td class=\"cp_frame warning cpi_td_w\">" + str2
                    + "<td><div class=\"cp_icon icon_pos3\"></div></td><td><div class=\"cp_text\">" + oldNum + str3
                    + oldLine + str4 + "<td width=\"5\"></td><td class=\"cp_frame warning cpi_td_w\">" + str2
                    + "<td><div class=\"cp_icon icon_pos3\"></div></td><td><div class=\"cp_text\">" + newNum + str3
                    + newLine);
        } else {
            compareResult.append(str11 + "<td class=\"cp_frame cpi_td_w\">" + str2 + str5 + oldNum + str3 + oldLine
                    + str4 + "<td width=\"5\"></td><td class=\"cp_frame cpi_td_w\">" + str2 + str5 + newNum + str3
                    + newLine);
        }
        compareResult.append(str4 + "</tr>");
    }

    /**
     * 生成基于文件名的自定义Patch（使用新版本API重写）
     * 保持与原版完全相同的逻辑
     */
    private Patch<String> generateCustomFileNamePatch(List<String> original, List<String> revised) {
        Patch<String> patch = new Patch<String>();
        List<String> originalCopy = new ArrayList<String>(original);
        List<String> revisedCopy = new ArrayList<String>(revised);

        // 1. 处理文件名匹配的行（修改）
        for (int i = 0; i < originalCopy.size(); i++) {
            String origLine = originalCopy.get(i);
            String origName = extractFilename(origLine);
            if (origName == null) {
                continue;
            }

            for (int j = 0; j < revisedCopy.size(); j++) {
                String revLine = revisedCopy.get(j);
                String revName = extractFilename(revLine);
                if (revName != null && revName.equals(origName)) {
                    if (!origLine.equals(revLine)) {
                        List<String> origLines = new ArrayList<String>();
                        origLines.add(origLine);
                        Chunk<String> origChunk = new Chunk<String>(i, origLines);

                        List<String> revLines = new ArrayList<String>();
                        revLines.add(revLine);
                        Chunk<String> revChunk = new Chunk<String>(j, revLines);

                        // 使用新版本API创建ChangeDelta
                        patch.addDelta(new ChangeDelta<String>(origChunk, revChunk));
                    }
                    revisedCopy.remove(j);
                    originalCopy.set(i, null);
                    break;
                }
            }
        }

        // 2. 处理剩余原始行（删除）
        for (int i = 0; i < originalCopy.size(); i++) {
            String origLine = originalCopy.get(i);
            if (origLine != null) {
                List<String> origLines = new ArrayList<String>();
                origLines.add(origLine);
                Chunk<String> origChunk = new Chunk<String>(i, origLines);
                Chunk<String> revChunk = new Chunk<String>(i, new ArrayList<String>());

                // 使用新版本API创建DeleteDelta
                patch.addDelta(new DeleteDelta<String>(origChunk, revChunk));
            }
        }

        // 3. 处理剩余修订行（新增）
        for (int j = 0; j < revisedCopy.size(); j++) {
            String revLine = revisedCopy.get(j);
            // 插入位置固定为原始列表的尾部（original.size()是合法的插入位置）
            int insertPos = original.size();
            Chunk<String> origChunk = new Chunk<String>(insertPos, new ArrayList<String>());
            List<String> revLines = new ArrayList<String>();
            revLines.add(revLine);
            Chunk<String> revChunk = new Chunk<String>(insertPos, revLines);

            // 使用新版本API创建InsertDelta
            patch.addDelta(new InsertDelta<String>(origChunk, revChunk));
        }

        return patch;
    }

    /**
     * 提取文件名方法（与原版保持一致）
     */
    private String extractFilename(String line) {
        if (line == null) {
            return null;
        }
        Matcher matcher = FILENAME_PATTERN.matcher(line);
        return matcher.find() ? matcher.group(1).trim() : null;
    }

    /**
     * 字符串替换方法（与原版保持一致）
     */
    private String getReplace(String str) {
        String res = str;
        if (str.contains("<br>")) {
            res = str.replace("<br>", "");
        }
        return res;
    }

    /**
     * 优化版本的文件读取方法（与原版保持一致）
     * @param filePath 文件路径
     * @return 文件内容字符串
     * @throws IOException 当读取文件发生错误时抛出
     */
    public static String readFileToString(String filePath) throws IOException {
        // 使用BufferedReader处理大文件，优化内存使用
        StringBuilder content = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8))) {
            String line;
            boolean firstLine = true;
            while ((line = reader.readLine()) != null) {
                if (!firstLine) {
                    content.append('\n'); // 保留换行符
                }
                content.append(line);
                firstLine = false;
            }
        } catch (FileNotFoundException e) {
            throw new IOException("文件未找到: " + filePath, e);
        } catch (IOException e) {
            throw new IOException("读取文件失败: " + filePath, e);
        }
        return content.toString();
    }

    /**
     * 测试主方法（与原版保持一致）
     */
    public static void main(String[] args) {
        // 测试文件路径（需要根据实际情况调整）
        String fileA = "D://c1.txt";
        String fileB = "D://c2.txt";
        try {
            String contentA = readFileToString(fileA);
            String contentB = readFileToString(fileB);

            List<String> aLines = new ArrayList<>(Arrays.asList(contentA.split("\n")));
            List<String> bLines = new ArrayList<>(Arrays.asList(contentB.split("\n")));
            long start = System.currentTimeMillis();
            Map map = new CompareSourceTargetContentManagerV2().compare(aLines, bLines);
            System.out.println(JSON.toJSONString(map));
            long end = System.currentTimeMillis();

            long duration = end - start;
            System.out.println("V2版本测试耗时: " + duration + " 毫秒");

            // 与原版本进行对比测试
            System.out.println("=== 开始对比测试 ===");
            Map originalResult = new CompareSourceTargetContentManager().compare(aLines, bLines);
            System.out.println("原版本ret: " + originalResult.get("ret"));
            System.out.println("V2版本ret: " + map.get("ret"));
            System.out.println("ret结果一致: " + originalResult.get("ret").equals(map.get("ret")));

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
