package com.ideal.envc.compare;

import com.github.difflib.DiffUtils;
import com.github.difflib.patch.Patch;
import com.github.difflib.text.DiffRow;
import com.github.difflib.text.DiffRowGenerator;

import java.util.Arrays;
import java.util.List;

/**
 * 简单的编译测试，验证新版本API的正确使用
 */
public class CompileTest {
    
    public static void main(String[] args) {
        System.out.println("=== 编译测试开始 ===");
        
        try {
            // 测试基本的API调用
            List<String> aLines = Arrays.asList("line1", "line2");
            List<String> bLines = Arrays.asList("line1", "modified_line2");
            
            // 测试DiffUtils.diff
            Patch<String> patch = DiffUtils.diff(aLines, bLines);
            System.out.println("DiffUtils.diff 调用成功");
            
            // 测试DiffRowGenerator
            DiffRowGenerator.Builder builder = DiffRowGenerator.create();
            DiffRowGenerator dfg = builder.build();
            System.out.println("DiffRowGenerator 创建成功");
            
            // 验证patch包含目标文本信息
            System.out.println("Patch包含 " + patch.getDeltas().size() + " 个差异操作");
            patch.getDeltas().forEach(delta -> {
                System.out.println("Delta类型: " + delta.getType());
                System.out.println("  源文本: " + delta.getSource().getLines());
                System.out.println("  目标文本: " + delta.getTarget().getLines());
            });

            // 测试generateDiffRows - 使用正确的API
            List<DiffRow> rows = dfg.generateDiffRows(aLines, patch);
            System.out.println("generateDiffRows 调用成功，生成了 " + rows.size() + " 行差异");

            // 测试DiffRow的方法
            for (DiffRow row : rows) {
                System.out.println("Tag: " + row.getTag() +
                                 ", Old: '" + row.getOldLine() + "'" +
                                 ", New: '" + row.getNewLine() + "'");
            }

            // 验证目标文本确实参与了比对
            System.out.println("\n=== 验证目标文本参与比对 ===");
            boolean foundTargetContent = false;
            for (DiffRow row : rows) {
                if (row.getNewLine().contains("modified_line2")) {
                    foundTargetContent = true;
                    System.out.println("✓ 找到目标文本内容: " + row.getNewLine());
                }
            }
            if (!foundTargetContent) {
                System.out.println("✗ 未找到目标文本内容，可能有问题");
            }
            
            System.out.println("=== 编译测试成功 ===");
            
        } catch (Exception e) {
            System.err.println("编译测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
