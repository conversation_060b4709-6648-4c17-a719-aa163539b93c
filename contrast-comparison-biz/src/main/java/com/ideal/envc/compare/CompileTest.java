package com.ideal.envc.compare;

import com.github.difflib.DiffUtils;
import com.github.difflib.patch.Patch;
import com.github.difflib.text.DiffRow;
import com.github.difflib.text.DiffRowGenerator;

import java.util.Arrays;
import java.util.List;

/**
 * 简单的编译测试，验证新版本API的正确使用
 */
public class CompileTest {
    
    public static void main(String[] args) {
        System.out.println("=== 编译测试开始 ===");
        
        try {
            // 测试基本的API调用
            List<String> aLines = Arrays.asList("line1", "line2");
            List<String> bLines = Arrays.asList("line1", "modified_line2");
            
            // 测试DiffUtils.diff
            Patch<String> patch = DiffUtils.diff(aLines, bLines);
            System.out.println("DiffUtils.diff 调用成功");
            
            // 测试DiffRowGenerator
            DiffRowGenerator.Builder builder = DiffRowGenerator.create();
            DiffRowGenerator dfg = builder.build();
            System.out.println("DiffRowGenerator 创建成功");
            
            // 测试generateDiffRows - 使用正确的API
            List<DiffRow> rows = dfg.generateDiffRows(aLines, patch);
            System.out.println("generateDiffRows 调用成功，生成了 " + rows.size() + " 行差异");
            
            // 测试DiffRow的方法
            for (DiffRow row : rows) {
                System.out.println("Tag: " + row.getTag() + 
                                 ", Old: " + row.getOldLine() + 
                                 ", New: " + row.getNewLine());
            }
            
            System.out.println("=== 编译测试成功 ===");
            
        } catch (Exception e) {
            System.err.println("编译测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
