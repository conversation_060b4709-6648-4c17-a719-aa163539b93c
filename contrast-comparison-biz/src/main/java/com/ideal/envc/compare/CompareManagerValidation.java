package com.ideal.envc.compare;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * CompareSourceTargetContentManagerV2 验证程序
 * 用于验证升级版本与原版本的功能一致性
 */
public class CompareManagerValidation {

    public static void main(String[] args) {
        System.out.println("=== CompareSourceTargetContentManagerV2 功能验证 ===");
        
        CompareManagerValidation validator = new CompareManagerValidation();
        
        try {
            // 测试1: 相同内容
            validator.testIdenticalContent();
            
            // 测试2: 不同内容
            validator.testDifferentContent();
            
            // 测试3: 插入行
            validator.testInsertLines();
            
            // 测试4: 删除行
            validator.testDeleteLines();
            
            // 测试5: 文件格式
            validator.testFileFormat();
            
            // 测试6: 空列表
            validator.testEmptyLists();
            
            // 测试7: 性能对比
            validator.testPerformance();
            
            System.out.println("\n=== 所有测试完成 ===");
            
        } catch (Exception e) {
            System.err.println("测试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void testIdenticalContent() {
        System.out.println("\n--- 测试1: 相同内容比对 ---");
        
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            Map<String, Object> originalResult = original.compare(aLines, bLines);
            Map<String, Object> v2Result = v2.compare(aLines, bLines);

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本结果: " + originalRet);
            System.out.println("V2版本结果: " + v2Ret);
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (originalRet && v2Ret && originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试1失败: " + e.getMessage());
        }
    }

    private void testDifferentContent() {
        System.out.println("\n--- 测试2: 不同内容比对 ---");
        
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "modified_line2", "line3");

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            Map<String, Object> originalResult = original.compare(aLines, bLines);
            Map<String, Object> v2Result = v2.compare(aLines, bLines);

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本结果: " + originalRet);
            System.out.println("V2版本结果: " + v2Ret);
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (!originalRet && !v2Ret && originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试2失败: " + e.getMessage());
        }
    }

    private void testInsertLines() {
        System.out.println("\n--- 测试3: 插入行比对 ---");
        
        List<String> aLines = Arrays.asList("line1", "line2");
        List<String> bLines = Arrays.asList("line1", "line2", "line3");

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            Map<String, Object> originalResult = original.compare(aLines, bLines);
            Map<String, Object> v2Result = v2.compare(aLines, bLines);

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本结果: " + originalRet);
            System.out.println("V2版本结果: " + v2Ret);
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (!originalRet && !v2Ret && originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试3失败: " + e.getMessage());
        }
    }

    private void testDeleteLines() {
        System.out.println("\n--- 测试4: 删除行比对 ---");
        
        List<String> aLines = Arrays.asList("line1", "line2", "line3");
        List<String> bLines = Arrays.asList("line1", "line2");

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            Map<String, Object> originalResult = original.compare(aLines, bLines);
            Map<String, Object> v2Result = v2.compare(aLines, bLines);

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本结果: " + originalRet);
            System.out.println("V2版本结果: " + v2Ret);
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (!originalRet && !v2Ret && originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试4失败: " + e.getMessage());
        }
    }

    private void testFileFormat() {
        System.out.println("\n--- 测试5: 文件格式比对 ---");
        
        List<String> aLines = Arrays.asList(
            "file1.txt (size: 1024B)",
            "file2.txt (size: 2048B)"
        );
        List<String> bLines = Arrays.asList(
            "file1.txt (size: 1024B)",
            "file2.txt (size: 3072B)"
        );

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            Map<String, Object> originalResult = original.compare(aLines, bLines);
            Map<String, Object> v2Result = v2.compare(aLines, bLines);

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本结果: " + originalRet);
            System.out.println("V2版本结果: " + v2Ret);
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (!originalRet && !v2Ret && originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试5失败: " + e.getMessage());
        }
    }

    private void testEmptyLists() {
        System.out.println("\n--- 测试6: 空列表比对 ---");
        
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            Map<String, Object> originalResult = original.compare(aLines, bLines);
            Map<String, Object> v2Result = v2.compare(aLines, bLines);

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本结果: " + originalRet);
            System.out.println("V2版本结果: " + v2Ret);
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (originalRet && v2Ret && originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试6失败: " + e.getMessage());
        }
    }

    private void testPerformance() {
        System.out.println("\n--- 测试7: 性能对比 ---");
        
        // 创建较大的测试数据
        List<String> aLines = new ArrayList<>();
        List<String> bLines = new ArrayList<>();
        
        for (int i = 0; i < 1000; i++) {
            aLines.add("line" + i);
            bLines.add(i % 10 == 0 ? "modified_line" + i : "line" + i);
        }

        try {
            CompareSourceTargetContentManager original = new CompareSourceTargetContentManager();
            CompareSourceTargetContentManagerV2 v2 = new CompareSourceTargetContentManagerV2();

            // 测试原版本
            long originalStart = System.currentTimeMillis();
            Map<String, Object> originalResult = original.compare(aLines, bLines);
            long originalTime = System.currentTimeMillis() - originalStart;

            // 测试V2版本
            long v2Start = System.currentTimeMillis();
            Map<String, Object> v2Result = v2.compare(aLines, bLines);
            long v2Time = System.currentTimeMillis() - v2Start;

            boolean originalRet = (Boolean) originalResult.get("ret");
            boolean v2Ret = (Boolean) v2Result.get("ret");

            System.out.println("原版本耗时: " + originalTime + "ms");
            System.out.println("V2版本耗时: " + v2Time + "ms");
            System.out.println("性能提升: " + (originalTime > v2Time ? "是" : "否"));
            System.out.println("结果一致: " + (originalRet == v2Ret));
            System.out.println("测试通过: " + (originalRet == v2Ret));
        } catch (Exception e) {
            System.err.println("测试7失败: " + e.getMessage());
        }
    }
}
