package com.ideal.envc.mapper;

import com.ideal.envc.model.bean.StartContrastQueryBean;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.bean.StartTaskBean;
import com.ideal.envc.model.bean.TaskInfoBean;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleContentEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.entity.RunRuleSyncEntity;

import java.util.List;
import java.util.Map;



/**
 * 一致性比对启动Mapper接口
 *
 * <AUTHOR>
 */
public interface StartContrastMapper {

    /**
     * 根据查询条件查询方案信息（包含关联的系统、节点、规则）
     *
     * @param queryBean 查询条件
     * @return 方案信息列表
     */
    List<StartPlanBean> selectStartPlansByQuery(StartContrastQueryBean queryBean);

    /**
     * 根据查询条件查询任务级方案信息（包含关联的系统、节点、规则和任务信息）
     *
     * @param queryBean 查询条件
     * @return 方案信息列表
     */
    List<StartPlanBean> selectStartPlansByTaskQuery(StartContrastQueryBean queryBean);

    /**
     * 根据查询条件查询任务信息
     *
     * @param queryBean 查询条件
     * @return 任务信息列表
     */
    List<StartTaskBean> selectStartTasksByQuery(StartContrastQueryBean queryBean);


    /**
     * 根据任务ID列表查询任务信息
     *
     * @param taskIds 任务ID列表
     * @return 任务信息列表
     */
    List<TaskInfoBean> selectTaskInfoByIds(List<Long> taskIds);

    /**
     * 批量插入运行实例
     *
     * @param instances 运行实例列表
     * @return 影响行数
     */
    int batchInsertRunInstance(List<RunInstanceEntity> instances);

    /**
     * 批量插入实例详情
     *
     * @param instanceInfos 实例详情列表
     * @return 影响行数
     */
    int batchInsertRunInstanceInfo(List<RunInstanceInfoEntity> instanceInfos);

    /**
     * 批量插入节点规则运行
     *
     * @param rules 节点规则运行列表
     * @return 影响行数
     */
    int batchInsertRunRule(List<RunRuleEntity> rules);

    /**
     * 批量插入节点规则同步
     *
     * @param ruleSyncs 节点规则同步列表
     * @return 影响行数
     */
    int batchInsertRunRuleSync(List<RunRuleSyncEntity> ruleSyncs);

    /**
     * 批量插入节点规则内容
     *
     * @param ruleContents 节点规则内容列表
     * @return 影响行数
     */
    int batchInsertRunRuleContent(List<RunRuleContentEntity> ruleContents);

    /**
     * 批量插入节点规则流程
     *
     * @param flows 节点规则流程列表
     * @return 影响行数
     */
    int batchInsertRunFlow(List<RunFlowEntity> flows);

    /**
     * 批量更新节点规则流程状态
     *
     * @param params 参数，包含flowIds和state
     * @return 影响行数
     */
    int batchUpdateRunFlowState(Map<String, Object> params);


    /**
     * 更新节点规则流程状态
     *
     * @param flow 节点规则流程
     * @return 影响行数
     */
    int updateRunFlow(RunFlowEntity flow);
}
