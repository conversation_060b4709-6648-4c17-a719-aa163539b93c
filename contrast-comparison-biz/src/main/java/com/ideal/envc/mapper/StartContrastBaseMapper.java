package com.ideal.envc.mapper;

import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.entity.RunRuleSyncEntity;

import java.util.List;

/**
 * 对比基础数据Mapper接口
 *
 * <AUTHOR>
 */
public interface StartContrastBaseMapper {

    /**
     * 根据实例ID查询实例信息
     *
     * @param instanceId 实例ID
     * @return 实例信息
     */
    RunInstanceEntity selectRunInstanceById(Long instanceId);

    /**
     * 根据方案ID查询实例信息列表
     *
     * @param planId 方案ID
     * @return 实例信息列表
     */
    List<RunInstanceEntity> selectRunInstancesByPlanId(Long planId);

    /**
     * 根据任务ID查询实例信息列表
     *
     * @param taskId 任务ID
     * @return 实例信息列表
     */
    List<RunInstanceEntity> selectRunInstancesByTaskId(Long taskId);

    /**
     * 根据实例ID查询实例详情列表
     *
     * @param instanceId 实例ID
     * @return 实例详情列表
     */
    List<RunInstanceInfoEntity> selectRunInstanceInfosByInstanceId(Long instanceId);

    /**
     * 根据实例详情ID查询规则列表
     *
     * @param instanceInfoId 实例详情ID
     * @return 规则列表
     */
    List<RunRuleEntity> selectRunRulesByInstanceInfoId(Long instanceInfoId);

    /**
     * 根据规则ID查询规则同步信息
     *
     * @param ruleId 规则ID
     * @return 规则同步信息
     */
    RunRuleSyncEntity selectRunRuleSyncByRuleId(Long ruleId);

    /**
     * 根据实例ID查询完整的层次化实例信息
     *
     * @param instanceId 实例ID
     * @return 层次化实例信息
     */
    HierarchicalRunInstanceBean selectHierarchicalRunInstanceById(Long instanceId);

    /**
     * 根据方案ID查询完整的层次化实例信息列表
     *
     * @param planId 方案ID
     * @return 层次化实例信息列表
     */
    List<HierarchicalRunInstanceBean> selectHierarchicalRunInstancesByPlanId(Long planId);

    /**
     * 根据任务ID查询完整的层次化实例信息列表
     *
     * @param taskId 任务ID
     * @return 层次化实例信息列表
     */
    List<HierarchicalRunInstanceBean> selectHierarchicalRunInstancesByTaskId(Long taskId);

    /**
     * 根据实例ID更新实例状态为初始状态
     *
     * @param instanceId 实例ID
     * @return 影响行数
     */
    int updateRunInstanceStatusToInitial(Long instanceId);

    /**
     * 根据实例ID更新实例详情状态为初始状态
     *
     * @param instanceId 实例ID
     * @return 影响行数
     */
    int updateRunInstanceInfoStatusToInitial(Long instanceId);

    /**
     * 根据实例ID更新规则状态为初始状态
     *
     * @param instanceId 实例ID
     * @return 影响行数
     */
    int updateRunRuleStatusToInitial(Long instanceId);

    /**
     * 根据实例ID更新流程状态为初始状态
     *
     * @param instanceId 实例ID
     * @return 影响行数
     */
    int updateRunFlowStatusToInitial(Long instanceId);
}
