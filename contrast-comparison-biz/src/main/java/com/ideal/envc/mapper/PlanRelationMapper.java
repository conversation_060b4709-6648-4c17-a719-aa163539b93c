package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.bean.PlanSystemListBean;
import com.ideal.envc.model.entity.PlanRelationEntity;
import com.ideal.envc.model.dto.PlanRelationQueryDto;
import com.ideal.envc.model.bean.PlanRelationListBean;
import org.apache.ibatis.annotations.Param;
import com.ideal.envc.model.dto.PlanRelationDto;
import com.ideal.envc.model.dto.UserDto;

/**
 * 方案信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface PlanRelationMapper {
    /**
     * 查询方案信息
     *
     * @param id 方案信息主键
     * @return 方案信息
     */
    PlanRelationEntity selectPlanRelationById(Long id);

    /**
     * 查询方案信息列表
     *
     * @param queryDto 查询条件
     * @return 方案信息集合
     */
    List<PlanRelationListBean> selectPlanRelationList(PlanRelationQueryDto queryDto);

    /**
     * 新增方案信息
     *
     * @param planRelation 方案信息
     * @return 结果
     */
    int insertPlanRelation(PlanRelationEntity planRelation);

    /**
     * 修改方案信息
     *
     * @param planRelation 方案信息
     * @return 结果
     */
    int updatePlanRelation(PlanRelationEntity planRelation);

    /**
     * 删除方案信息
     *
     * @param id 方案信息主键
     * @return 结果
     */
    int deletePlanRelationById(Long id);

    /**
     * 批量删除方案信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePlanRelationByIds(Long[] ids);

    /**
     * 查询方案绑定系统列表
     *
     * @param planId 方案ID
     * @param businessSystemName 业务系统名称
     * @return 方案绑定系统列表
     */
    List<PlanSystemListBean> selectPlanSystemList(Long planId, String businessSystemName);

    /**
     * 根据业务系统ID集合批量删除方案关系
     * @param businessSystemIdList 业务系统ID集合
     * @return 删除数量
     */
    int deletePlanRelationByBusinessSystemIds(List<Long> businessSystemIdList);

    /**
     * 根据环境方案ID和业务系统ID查询关联关系
     *
     * @param envcPlanId 环境方案ID
     * @param businessSystemId 业务系统ID
     * @return 方案关联信息列表
     */
    List<PlanRelationEntity> selectPlanRelationByPlanAndSystem(@Param("envcPlanId") Long envcPlanId, @Param("businessSystemId") Long businessSystemId);

    /**
     * 批量新增方案与系统关系
     *
     * @param planRelationDtoList 方案与系统关系列表
     * @param userDto 当前用户信息
     * @return 结果
     */
    int batchInsertPlanRelation(@Param("list") List<PlanRelationDto> planRelationDtoList, @Param("user") UserDto userDto);

    /**
     * 根据方案ID查询已绑定的系统ID列表
     *
     * @param planId 方案ID
     * @return 已绑定的系统ID列表
     */
    List<Long> selectBoundSystemIdsByPlanId(@Param("planId") Long planId);

    /**
     * 查询可绑定的系统列表
     *
     * @param planId 方案ID
     * @param boundSystemIds 已绑定的系统ID列表
     * @param businessSystemName 业务系统名称（可选）
     * @return 可绑定的系统列表
     */
    List<PlanSystemListBean> selectAvailablePlanSystemList(@Param("planId") Long planId,
                                                          @Param("boundSystemIds") List<Long> boundSystemIds,
                                                          @Param("businessSystemName") String businessSystemName);
}
