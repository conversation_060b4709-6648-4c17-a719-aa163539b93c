package com.ideal.envc.mapper;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import com.ideal.envc.model.bean.SystemListBean;
import com.ideal.envc.model.entity.SystemComputerEntity;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 */
public interface SystemComputerMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    SystemComputerEntity selectSystemComputerById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param systemComputer 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    List<SystemComputerEntity> selectSystemComputerList(SystemComputerEntity systemComputer);

    /**
     * 新增【请填写功能名称】
     *
     * @param systemComputer 【请填写功能名称】
     * @return 结果
     */
    int insertSystemComputer(SystemComputerEntity systemComputer);

    /**
     * 修改【请填写功能名称】
     *
     * @param systemComputer 【请填写功能名称】
     * @return 结果
     */
    int updateSystemComputer(SystemComputerEntity systemComputer);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    int deleteSystemComputerById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSystemComputerByIds(Long[] ids);

    /**
     * 查询已绑定设备的业务系统列表
     *
     * @param businessSystemName 业务系统名称
     * @param businessSystemDesc 业务系统描述
     * @param businessSystemIds 有权限的业务系统ID列表
     * @return 业务系统列表
     */
    List<SystemListBean> selectSystemList(String businessSystemName, String businessSystemDesc, List<Long> businessSystemIds);

    /**
     * 根据设备ID集合和业务系统ID查询设备信息
     *
     * @param computerIds 设备ID集合
     * @param businessSystemId 业务系统ID
     * @return 设备ID和设备IP的映射关系，key为设备ID，value为设备IP
     */
    @MapKey("computerId")
    Map<Long, SystemComputerEntity> selectComputerIpMapByIdsAndSystemId(List<Long> computerIds, Long businessSystemId);

    /**
     * 根据业务系统ID、中心ID和排除设备ID集合查询设备列表（支持分页）
     *
     * @param businessSystemId 业务系统ID
     * @param centerId 中心ID
     * @param excludeComputerIds 排除设备ID集合
     * @return 设备列表
     */
    List<SystemComputerEntity> selectComputerListByCondition(Long businessSystemId, Long centerId, List<Long> excludeComputerIds);

    /**
     * 根据业务系统ID集合批量查询系统设备关系
     * @param businessSystemIdList 业务系统ID集合
     * @return 系统设备关系集合
     */
    List<SystemComputerEntity> selectSystemComputerByBusinessSystemIds(List<Long> businessSystemIdList);

    /**
     * 根据业务系统ID和设备ID批量查询系统设备关系
     * @param businessSystemId 业务系统ID
     * @param computerIds 设备ID集合
     * @return 系统设备关系集合
     */
    List<SystemComputerEntity> selectByBusinessSystemIdAndComputerIds(Long businessSystemId, List<Long> computerIds);

    /**
     * 根据ID集合批量查询系统计算机
     *
     * @param ids 系统计算机ID集合
     * @return 系统计算机集合
     */
    List<SystemComputerEntity> selectSystemComputerByIds(Long[] ids);

    /**
     * 根据业务系统ID查询已绑定的设备列表
     *
     * @param businessSystemId 业务系统ID
     * @return 已绑定的设备列表
     */
    List<SystemComputerEntity> selectByBusinessSystemId(Long businessSystemId);

    /**
     * 根据IP地址查询主机名
     *
     * @param computerIp 设备IP地址
     * @return 主机名，如果找不到则返回null
     */
    String selectComputerNameByIp(String computerIp);

    /**
     * 批量根据IP地址查询主机名
     *
     * @param computerIps IP地址列表
     * @return IP地址和主机名的映射关系，key为IP地址，value为主机名
     */
    @MapKey("computerIp")
    Map<String, SystemComputerEntity> selectComputerNameMapByIps(List<String> computerIps);
}
