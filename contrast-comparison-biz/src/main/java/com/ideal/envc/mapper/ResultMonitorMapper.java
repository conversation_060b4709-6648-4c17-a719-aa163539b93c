package com.ideal.envc.mapper;

import com.ideal.envc.model.bean.ResultMonitorBean;

import java.util.List;

/**
 * 比对结果监控Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface ResultMonitorMapper {

    /**
     * 查询比对结果列表
     *
     * @param businessSystemName 业务系统名称
     * @param model 对比类型
     * @param result 对比结果
     * @param from 触发来源
     * @return 比对结果列表
     */
    List<ResultMonitorBean> selectResultMonitorList(String businessSystemName, Integer model, Integer result, Integer from);
}
