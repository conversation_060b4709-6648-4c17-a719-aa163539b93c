package com.ideal.envc.mapper;

import java.util.List;
import com.ideal.envc.model.entity.DictionaryDetailEntity;

/**
 * 字典详情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface DictionaryDetailMapper {
    /**
     * 查询字典详情
     *
     * @param id 字典详情主键
     * @return 字典详情
     */
    DictionaryDetailEntity selectDictionaryDetailById(Long id);

    /**
     * 查询字典详情列表
     *
     * @param dictionaryDetail 字典详情
     * @return 字典详情集合
     */
    List<DictionaryDetailEntity> selectDictionaryDetailList(DictionaryDetailEntity dictionaryDetail);

    /**
     * 新增字典详情
     *
     * @param dictionaryDetail 字典详情
     * @return 结果
     */
    int insertDictionaryDetail(DictionaryDetailEntity dictionaryDetail);

    /**
     * 修改字典详情
     *
     * @param dictionaryDetail 字典详情
     * @return 结果
     */
    int updateDictionaryDetail(DictionaryDetailEntity dictionaryDetail);

    /**
     * 删除字典详情
     *
     * @param id 字典详情主键
     * @return 结果
     */
    int deleteDictionaryDetailById(Long id);

    /**
     * 批量删除字典详情
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteDictionaryDetailByIds(Long[] ids);

    /**
     * 根据字典码值获取字典详情信息列表
     *
     * @param code 字典码
     * @return 字典详情列表
     */
    List<DictionaryDetailEntity> selectDictionaryDetailListByCode(String code);
}
