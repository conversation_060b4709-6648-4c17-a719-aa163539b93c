package com.ideal.envc.service;

import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.model.dto.FlowOperateResultDto;
import com.ideal.envc.model.dto.UserDto;

/**
 * <AUTHOR>
 */
public interface IFlowOperateService {

    /**
     * 流程操作
     * @param flowIds 流程ID数组
     * @param userDto 操作用户
     */
    FlowOperateResultDto operateTerminatedFlow(Long[] flowIds, UserDto userDto) throws Exception;


    /**
     * 流程重试操作
     * @param flowId 流程ID
     * @param userDto 操作用户
     */
    FlowOperateResultDto operateRetryFlow(Long flowId, UserDto userDto) throws Exception;
}
