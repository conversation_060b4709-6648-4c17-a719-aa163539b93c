package com.ideal.envc.service.impl;

import com.ideal.envc.interaction.model.CenterDto;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.service.ICenterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 中心Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class CenterServiceImpl implements ICenterService {
    private final Logger logger = LoggerFactory.getLogger(CenterServiceImpl.class);

    private final SystemInteract systemInteract;

    public CenterServiceImpl(SystemInteract systemInteract) {
        this.systemInteract = systemInteract;
    }

    /**
     * 获取中心列表
     *
     * @return 中心列表
     */
    @Override
    public List<CenterDto> getCenterList(CenterQueryDto centerQueryDto) {
        logger.info("获取中心列表");
        return systemInteract.getCenterList( centerQueryDto);
    }
}
