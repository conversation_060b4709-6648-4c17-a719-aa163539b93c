package com.ideal.envc.service;

import com.ideal.envc.model.dto.NodeRelationDto;
import com.ideal.envc.model.dto.NodeRelationQueryDto;
import com.ideal.envc.model.dto.NodeRelationSaveDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.bean.NodeRelationListBean;
import com.ideal.envc.model.dto.NodeRelationListDto;

/**
 * 节点关系规则Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface INodeRelationService {
    /**
     * 查询节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 节点关系规则
     */
    NodeRelationDto selectNodeRelationById(Long id);

    /**
     * 查询节点关系规则列表
     *
     * @param queryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 节点关系规则列表
     */
    PageInfo<NodeRelationListDto> selectNodeRelationList(NodeRelationQueryDto queryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增节点关系规则
     *
     * @param nodeRelationSaveDto 节点关系保存DTO
     * @return 结果
     */
    int insertNodeRelation(NodeRelationSaveDto nodeRelationSaveDto, UserDto userDto);

    /**
     * 修改节点关系规则（含规则内容，content 为空时删除内容，有值时新增或更新内容）
     *
     * @param nodeRelationSaveDto 节点关系规则（含规则内容）
     * @param userDto 用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int updateNodeRelation(NodeRelationSaveDto nodeRelationSaveDto, UserDto userDto) throws ContrastBusinessException;

    /**
     * 批量删除节点关系规则
     *
     * @param ids 需要删除的节点关系规则主键集合
     * @return 结果
     */
    int deleteNodeRelationByIds(Long[] ids);

    /**
     * 批量启用或禁用节点关系规则
     *
     * @param ids 节点关系规则ID集合
     * @param enabled 启用状态（0：启用，1：禁用）
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int updateNodeRelationEnabledByIds(Long[] ids, Integer enabled, UserDto userDto) throws ContrastBusinessException;
}
