package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.PlanMapper;
import com.ideal.envc.model.entity.PlanEntity;
import com.ideal.envc.service.IPlanService;
import com.ideal.envc.model.dto.PlanDto;
import com.ideal.envc.model.dto.PlanQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import com.ideal.envc.exception.ContrastBusinessException;

import java.util.List;

/**
 * 方案信息Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class PlanServiceImpl implements IPlanService {
    private final Logger logger = LoggerFactory.getLogger(PlanServiceImpl.class);

    private final PlanMapper planMapper;

    public PlanServiceImpl(PlanMapper planMapper) {
        this.planMapper = planMapper;
    }

    /**
     * 查询方案信息
     *
     * @param id 方案信息主键
     * @return 方案信息
     */
    @Override
    public PlanDto selectPlanById(Long id) {
        PlanEntity plan = planMapper.selectPlanById(id);
        return BeanUtils.copy(plan, PlanDto.class);
    }

    /**
     * 查询方案信息列表
     *
     * @param planQueryDto 方案信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 方案信息
     */
    @Override
    public PageInfo<PlanDto> selectPlanList(PlanQueryDto planQueryDto, Integer pageNum, Integer pageSize) {
        PlanEntity query = BeanUtils.copy(planQueryDto, PlanEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<PlanEntity> planList = planMapper.selectPlanList(query);
        return PageDataUtil.toDtoPage(planList, PlanDto.class);
    }


    /**
     * 新增方案信息
     *
     * @param planDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertPlan(PlanDto planDto, UserDto userDto) throws ContrastBusinessException {
        if (planDto == null) {
            logger.error("新增方案失败：方案信息为空");
            throw new ContrastBusinessException("方案信息不能为空");
        }

        // 验证方案名称是否已存在
        if (planDto.getName() == null || planDto.getName().trim().isEmpty()) {
            logger.error("新增方案失败：方案名称为空");
            throw new ContrastBusinessException("方案名称不能为空");
        }

        int count = planMapper.checkPlanNameExists(planDto.getName().trim());
        if (count > 0) {
            logger.error("新增方案失败：方案名称已存在 {}", planDto.getName());
            throw new ContrastBusinessException("方案名称已存在，请重新输入");
        }

        try {
            // 设置创建人和更新人信息
            PlanEntity plan = BeanUtils.copy(planDto, PlanEntity.class);
            plan.setCreatorId(userDto.getId());
            plan.setCreatorName(userDto.getFullName());
            plan.setUpdatorId(userDto.getId());
            plan.setUpdatorName(userDto.getFullName());

            int result = planMapper.insertPlan(plan);
            logger.info("新增方案成功，名称：{}，创建人：{}", plan.getName(), userDto.getFullName());
            return result;
        } catch (Exception e) {
            logger.error("新增方案系统异常", e);
            throw new ContrastBusinessException("新增方案失败：" + e.getMessage());
        }
    }


    /**
     * 修改方案信息
     *
     * @param planDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updatePlan(PlanDto planDto, UserDto userDto) throws ContrastBusinessException {
        if (planDto == null) {
            logger.error("修改方案失败：方案信息为空");
            throw new ContrastBusinessException("方案信息不能为空");
        }
        if (planDto.getId() == null) {
            logger.error("修改方案失败：方案ID为空");
            throw new ContrastBusinessException("方案ID不能为空");
        }

        // 验证方案是否存在
        PlanEntity existingPlan = planMapper.selectPlanById(planDto.getId());
        if (existingPlan == null) {
            logger.error("修改方案失败：方案不存在，ID={}", planDto.getId());
            throw new ContrastBusinessException("方案不存在");
        }

        // 验证方案名称是否已存在（排除当前方案自身）
        if (planDto.getName() != null && !planDto.getName().trim().isEmpty()) {
            int count = planMapper.checkPlanNameExistsExcludeId(planDto.getName().trim(), planDto.getId());
            if (count > 0) {
                logger.error("修改方案失败：方案名称已存在，名称={}", planDto.getName());
                throw new ContrastBusinessException("方案名称已存在，请重新输入");
            }
        }

        try {
            // 设置更新人信息
            PlanEntity plan = BeanUtils.copy(planDto, PlanEntity.class);
            plan.setUpdatorId(userDto.getId());
            plan.setUpdatorName(userDto.getFullName());

            int result = planMapper.updatePlan(plan);
            logger.info("修改方案成功，ID={}，名称={}，操作人：{}", plan.getId(), plan.getName(), userDto.getFullName());
            return result;
        } catch (Exception e) {
            logger.error("修改方案系统异常，ID={}", planDto.getId(), e);
            throw new ContrastBusinessException("修改方案失败：" + e.getMessage());
        }
    }



    /**
     * 批量删除方案信息
     *
     * @param ids 需要删除的方案信息主键集合
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deletePlanByIds(Long[] ids, UserDto userDto) throws ContrastBusinessException {
        if (ids == null || ids.length == 0) {
            logger.error("删除方案失败：ID列表为空");
            throw new ContrastBusinessException("删除ID不能为空");
        }

        try {
            // 验证方案是否存在
            for (Long id : ids) {
                PlanEntity plan = planMapper.selectPlanById(id);
                if (plan == null) {
                    logger.error("删除方案失败：方案不存在，ID={}", id);
                    throw new ContrastBusinessException("ID为" + id + "的方案不存在");
                }
            }

            int result = planMapper.deletePlanByIds(ids);
            logger.info("删除方案成功，ID数量={}，操作人：{}", ids.length, userDto.getFullName());
            return result;
        } catch (ContrastBusinessException e) {
            throw e;
        } catch (Exception e) {
            logger.error("删除方案系统异常", e);
            throw new ContrastBusinessException("删除方案失败：" + e.getMessage());
        }
    }
}
