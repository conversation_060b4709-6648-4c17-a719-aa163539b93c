package com.ideal.envc.service.impl;

import com.ideal.common.dto.R;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.interaction.model.ComputerJo;
import com.ideal.envc.interaction.model.SystemComputerListQueryInnerIo;
import com.ideal.envc.interaction.model.SystemComputerListQueryIo;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.bean.SystemListBean;
import com.ideal.envc.model.dto.SystemListDto;
import com.ideal.envc.model.dto.SystemListQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import com.ideal.envc.service.ISystemComputerService;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import com.ideal.envc.exception.ContrastBusinessException;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class SystemComputerServiceImpl implements ISystemComputerService {
    private final Logger logger = LoggerFactory.getLogger(SystemComputerServiceImpl.class);

    private final SystemComputerMapper systemComputerMapper;
    private final SystemComputerNodeMapper systemComputerNodeMapper;
    private final NodeRelationMapper nodeRelationMapper;
    private final NodeRuleContentMapper nodeRuleContentMapper;
    private final SystemInteract systemInteract;
    private final BatchHandler batchHandler;

    public SystemComputerServiceImpl(SystemComputerMapper systemComputerMapper,
                                   SystemComputerNodeMapper systemComputerNodeMapper,
                                   NodeRelationMapper nodeRelationMapper,
                                   NodeRuleContentMapper nodeRuleContentMapper,
                                   SystemInteract systemInteract,
                                   BatchHandler batchHandler) {
        this.systemComputerMapper = systemComputerMapper;
        this.systemComputerNodeMapper = systemComputerNodeMapper;
        this.nodeRelationMapper = nodeRelationMapper;
        this.nodeRuleContentMapper = nodeRuleContentMapper;
        this.systemInteract = systemInteract;
        this.batchHandler = batchHandler;
    }

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SystemComputerDto selectSystemComputerById(Long id) {
        SystemComputerEntity systemComputer = systemComputerMapper.selectSystemComputerById(id);
        return BeanUtils.copy(systemComputer, SystemComputerDto.class);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param systemComputerQueryDto 【请填写功能名称】
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 【请填写功能名称】
     */
    @Override
    public PageInfo<SystemComputerDto> selectSystemComputerList(SystemComputerQueryDto systemComputerQueryDto, Integer pageNum, Integer pageSize) {
        SystemComputerEntity query = BeanUtils.copy(systemComputerQueryDto, SystemComputerEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<SystemComputerEntity> systemComputerList = systemComputerMapper.selectSystemComputerList(query);
        return PageDataUtil.toDtoPage(systemComputerList, SystemComputerDto.class);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param systemComputerQueryDto 【请填写功能名称】
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 【请填写功能名称】
     */
    @Override
    public R<PageInfo<ComputerJo>> pendingSystemComputerList(SystemComputerQueryDto systemComputerQueryDto, Integer pageNum, Integer pageSize) {
        if(systemComputerQueryDto==null || systemComputerQueryDto.getBusinessSystemId()==null){
            return R.fail("13000","传入业务系统ID为空！");
        }
        // 查询当前系统已绑定的设备ID列表
        List<SystemComputerEntity> existComputers = systemComputerMapper.selectByBusinessSystemId(systemComputerQueryDto.getBusinessSystemId());
        if (existComputers != null && !existComputers.isEmpty()) {
            List<Long> excludeComputerIds = existComputers.stream()
                    .map(SystemComputerEntity::getComputerId)
                    .collect(Collectors.toList());
            systemComputerQueryDto.setExcludeComputerIds(excludeComputerIds);
        }
        SystemComputerListQueryInnerIo bean = new SystemComputerListQueryInnerIo();
        if(StringUtils.isNotBlank(systemComputerQueryDto.getComputerName())){
            bean.setHostName(systemComputerQueryDto.getComputerName());
        }
        if(StringUtils.isNotBlank(systemComputerQueryDto.getComputerIp())){
            bean.setComputerIp(systemComputerQueryDto.getComputerIp());
        }
        if(systemComputerQueryDto.getCenterId()!=null){
            bean.setCentralId(systemComputerQueryDto.getCenterId());
        }
        if(systemComputerQueryDto.getExcludeComputerIds()!=null && !systemComputerQueryDto.getExcludeComputerIds().isEmpty()){
            bean.setExcludeComputerIds(systemComputerQueryDto.getExcludeComputerIds());
        }

        if(systemComputerQueryDto.getAppointComputerIds()!=null && !systemComputerQueryDto.getAppointComputerIds().isEmpty()){
            bean.setAppointComputerIds(systemComputerQueryDto.getAppointComputerIds());
        }
        bean.setBusinessSystemId(   systemComputerQueryDto.getBusinessSystemId());
        SystemComputerListQueryIo queryBean = new SystemComputerListQueryIo();
        queryBean.setBusinessSystemId(systemComputerQueryDto.getBusinessSystemId());
        queryBean.setQueryParam(bean);
        queryBean.setType(0);
        queryBean.setPageNum(pageNum);
        queryBean.setPageSize(pageSize);
        return systemInteract.getBusinessSystemComputerList(queryBean);

    }

    /**
     * 新增【请填写功能名称】
     *
     * @param systemComputerDto 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSystemComputer(SystemComputerDto systemComputerDto) {
        SystemComputerEntity systemComputer = BeanUtils.copy(systemComputerDto, SystemComputerEntity.class);
        return systemComputerMapper.insertSystemComputer(systemComputer);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param systemComputerDto 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSystemComputer(SystemComputerDto systemComputerDto) {
        SystemComputerEntity systemComputer = BeanUtils.copy(systemComputerDto, SystemComputerEntity.class);
        return systemComputerMapper.updateSystemComputer(systemComputer);
    }

    /**
     * 批量删除系统计算机
     *
     * @param ids 需要删除的系统计算机主键集合
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteSystemComputerByIds(Long[] ids) throws ContrastBusinessException {
        if (ids == null || ids.length == 0) {
            throw new ContrastBusinessException("删除失败，主键集合为空");
        }

        // 验证要删除的系统计算机是否存在
        List<SystemComputerEntity> systemComputerEntities = systemComputerMapper.selectSystemComputerByIds(ids);
        if (systemComputerEntities.isEmpty()) {
            throw new ContrastBusinessException("未找到对应的系统设备关系记录，删除失败");
        }

        // 按业务系统ID分组，获取每个业务系统下的设备ID列表
        Map<Long, List<Long>> businessSystemComputerMap = systemComputerEntities.stream()
                .collect(Collectors.groupingBy(
                        SystemComputerEntity::getBusinessSystemId,
                        Collectors.mapping(SystemComputerEntity::getComputerId, Collectors.toList())
                ));

        // 遍历每个业务系统，处理其下的设备节点关系
        for (Map.Entry<Long, List<Long>> entry : businessSystemComputerMap.entrySet()) {
            Long businessSystemId = entry.getKey();
            List<Long> computerIds = entry.getValue();

            // 直接查询需要删除的节点记录
            List<SystemComputerNodeEntity> nodesToDelete = systemComputerNodeMapper
                    .selectSystemComputerNodeByBusinessSystemIdAndComputerIds(businessSystemId, computerIds);

            if (!nodesToDelete.isEmpty()) {
                // 获取节点ID列表
                List<Long> nodeIds = nodesToDelete.stream()
                        .map(SystemComputerNodeEntity::getId)
                        .collect(Collectors.toList());

                // 查询节点关系记录
                List<NodeRelationEntity> nodeRelationEntities = nodeRelationMapper.selectNodeRelationBySystemComputerNodeIds(nodeIds);
                if (!nodeRelationEntities.isEmpty()) {
                    // 获取关系ID列表
                    List<Long> relationIds = nodeRelationEntities.stream()
                            .map(NodeRelationEntity::getId)
                            .collect(Collectors.toList());

                    // 删除节点规则内容
                    nodeRuleContentMapper.deleteNodeRuleContentByNodeRelationIds(relationIds.toArray(new Long[0]));
                    // 删除节点关系
                    nodeRelationMapper.deleteNodeRelationByIds(relationIds.toArray(new Long[0]));
                }

                // 删除系统计算机节点
                systemComputerNodeMapper.deleteSystemComputerNodeByIds(nodeIds.toArray(new Long[0]));
            }
        }

        // 删除系统计算机
        return systemComputerMapper.deleteSystemComputerByIds(ids);
    }

    /**
     * 新增业务系统下设备
     *
     * @param systemComputerDtoList 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int addSystemComputer(List<SystemComputerDto> systemComputerDtoList, UserDto userDto) throws ContrastBusinessException {
        if(systemComputerDtoList == null || systemComputerDtoList.isEmpty()){
            logger.warn("新增业务系统下设备为空");
            throw new ContrastBusinessException("新增设备列表不能为空");
        }
        // 校验是否有重复设备
        Long businessSystemId = systemComputerDtoList.get(0).getBusinessSystemId();
        List<Long> computerIds = new ArrayList<>();
        for(SystemComputerDto dto : systemComputerDtoList){
            computerIds.add(dto.getComputerId());
        }
        List<SystemComputerEntity> existList = systemComputerMapper.selectByBusinessSystemIdAndComputerIds(businessSystemId, computerIds);
        if(existList != null && !existList.isEmpty()){
            throw new ContrastBusinessException("部分设备已存在，禁止重复添加！");
        }
        List<SystemComputerEntity> systemComputerEntityList = new ArrayList<>();
        for(SystemComputerDto systemComputerDto : systemComputerDtoList){
            SystemComputerEntity systemComputerEntity = BeanUtils.copy(systemComputerDto, SystemComputerEntity.class);
            systemComputerEntity.setCreatorId(userDto.getId());
            systemComputerEntity.setCreatorName(userDto.getFullName());
            systemComputerEntityList.add(systemComputerEntity);
        }
        batchHandler.batchData(systemComputerEntityList,systemComputerMapper::insertSystemComputer,500);
        return systemComputerEntityList.size();
    }

    /**
     * 查询已绑定设备的业务系统列表
     *
     * @param systemListQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @param userId 用户ID
     * @return 业务系统列表
     */
    @Override
    public PageInfo<SystemListDto> selectSystemList(SystemListQueryDto systemListQueryDto, Integer pageNum, Integer pageSize, Long userId) {
        logger.info("查询已绑定设备的业务系统列表，查询条件：{}，用户ID：{}", systemListQueryDto, userId);

        // 根据用户ID查询平台管理具备权限的业务系统ID列表
        List<Long> businessSystemIdList = systemInteract.getBusinessSystemIdList(userId);
        if (businessSystemIdList == null || businessSystemIdList.isEmpty()) {
            logger.warn("用户无业务系统权限，用户ID：{}", userId);
            return new PageInfo<>(Collections.emptyList());
        }

        // 查询条件中如果存在排除业务系统Id集合，需要再查询权限系统Id中将排除部分的系统ID滤除掉，以保证业务系统列表中不包含排除部分
        if (systemListQueryDto != null && systemListQueryDto.getExcludeBusinessSystemIds() != null && !systemListQueryDto.getExcludeBusinessSystemIds().isEmpty()) {
            businessSystemIdList.removeAll(systemListQueryDto.getExcludeBusinessSystemIds());
        }
        if (businessSystemIdList.isEmpty()) {
            logger.warn("用户无要求的有业务系统权限的系统，用户ID：{}", userId);
            return new PageInfo<>(Collections.emptyList());
        }

        // 查询条件
        String businessSystemName = systemListQueryDto != null ? systemListQueryDto.getBusinessSystemName() : null;
        String businessSystemDesc = systemListQueryDto != null ? systemListQueryDto.getBusinessSystemDesc() : null;

        // 分页查询
        PageMethod.startPage(pageNum, pageSize);
        List<SystemListBean> systemListBeans = systemComputerMapper.selectSystemList(businessSystemName, businessSystemDesc, businessSystemIdList);

        // 转换为DTO对象
        List<SystemListDto> systemListDtos = BeanUtils.copy(systemListBeans, SystemListDto.class);

        // 构建分页结果
        PageInfo<SystemListBean> pageInfo = new PageInfo<>(systemListBeans);
        PageInfo<SystemListDto> result = new PageInfo<>(systemListDtos);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());

        return result;
    }
}

