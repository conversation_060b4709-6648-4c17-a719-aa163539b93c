package com.ideal.envc.service;

import com.ideal.common.dto.R;
import com.ideal.envc.interaction.model.ComputerJo;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryDto;
import com.ideal.envc.model.dto.SystemListDto;
import com.ideal.envc.model.dto.SystemListQueryDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.exception.ContrastBusinessException;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface ISystemComputerService {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    SystemComputerDto selectSystemComputerById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param systemComputerQueryDto 【请填写功能名称】
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 【请填写功能名称】集合
     */
    PageInfo<SystemComputerDto> selectSystemComputerList(SystemComputerQueryDto systemComputerQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param systemComputerQueryDto 【请填写功能名称】
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 【请填写功能名称】集合
     */
    R<PageInfo<ComputerJo>> pendingSystemComputerList(SystemComputerQueryDto systemComputerQueryDto, Integer pageNum, Integer pageSize);



    /**
     * 新增【请填写功能名称】
     *
     * @param systemComputerDto 【请填写功能名称】
     * @return 结果
     */
    int insertSystemComputer(SystemComputerDto systemComputerDto);

    /**
     * 修改【请填写功能名称】
     *
     * @param systemComputerDto 【请填写功能名称】
     * @return 结果
     */
    int updateSystemComputer(SystemComputerDto systemComputerDto);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int deleteSystemComputerByIds(Long[] ids) throws ContrastBusinessException;


    /**
     * 新增【请填写功能名称】
     *
     * @param systemComputerDto 【请填写功能名称】
     * @param  userDto 【请填写功能名称】
     * @return 结果
     */
    int addSystemComputer(List<SystemComputerDto> systemComputerDto, UserDto userDto) throws com.ideal.envc.exception.ContrastBusinessException;

    /**
     * 查询已绑定设备的业务系统列表
     *
     * @param systemListQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @param userId 用户ID
     * @return 业务系统列表
     */
    PageInfo<SystemListDto> selectSystemList(SystemListQueryDto systemListQueryDto, Integer pageNum, Integer pageSize, Long userId);

}
