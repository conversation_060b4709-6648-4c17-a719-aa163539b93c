package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.service.IRunFlowResultService;
import com.ideal.envc.model.dto.RunFlowResultDto;
import com.ideal.envc.model.dto.RunFlowResultQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.exception.ContrastBusinessException;

import java.util.List;

/**
 * 流程输出结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class RunFlowResultServiceImpl implements IRunFlowResultService {
    private final Logger logger = LoggerFactory.getLogger(RunFlowResultServiceImpl.class);

    private final RunFlowResultMapper runFlowResultMapper;

    public RunFlowResultServiceImpl(RunFlowResultMapper runFlowResultMapper) {
        this.runFlowResultMapper = runFlowResultMapper;
    }

    /**
     * 查询流程输出结果
     *
     * @param id 流程输出结果主键
     * @return 流程输出结果
     */
    @Override
    public RunFlowResultDto selectRunFlowResultById(Long id) throws ContrastBusinessException {
        try {
            RunFlowResultEntity runFlowResult = runFlowResultMapper.selectRunFlowResultById(id);
            return BeanUtils.copy(runFlowResult, RunFlowResultDto.class);
        } catch (Exception e) {
            logger.error("查询流程输出结果失败", e);
            throw new ContrastBusinessException("查询流程输出结果失败：" + e.getMessage());
        }
    }

    /**
     * 查询流程输出结果列表
     *
     * @param runFlowResultQueryDto 流程输出结果
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 流程输出结果
     */
    @Override
    public PageInfo<RunFlowResultDto> selectRunFlowResultList(RunFlowResultQueryDto runFlowResultQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            RunFlowResultEntity query = BeanUtils.copy(runFlowResultQueryDto, RunFlowResultEntity.class);
            PageMethod.startPage(pageNum, pageSize);
            List<RunFlowResultEntity> runFlowResultList = runFlowResultMapper.selectRunFlowResultList(query);
            return PageDataUtil.toDtoPage(runFlowResultList, RunFlowResultDto.class);
        } catch (Exception e) {
            logger.error("查询流程输出结果列表失败", e);
            throw new ContrastBusinessException("查询流程输出结果列表失败：" + e.getMessage());
        }
    }

    /**
     * 新增流程输出结果
     *
     * @param runFlowResultDto 流程输出结果
     * @return 结果
     */
    @Override
    public int insertRunFlowResult(RunFlowResultDto runFlowResultDto) throws ContrastBusinessException {
        try {
            RunFlowResultEntity runFlowResult = BeanUtils.copy(runFlowResultDto, RunFlowResultEntity.class);
            return runFlowResultMapper.insertRunFlowResult(runFlowResult);
        } catch (Exception e) {
            logger.error("新增流程输出结果失败", e);
            throw new ContrastBusinessException("新增流程输出结果失败：" + e.getMessage());
        }
    }

    /**
     * 修改流程输出结果
     *
     * @param runFlowResultDto 流程输出结果
     * @return 结果
     */
    @Override
    public int updateRunFlowResult(RunFlowResultDto runFlowResultDto) throws ContrastBusinessException {
        try {
            RunFlowResultEntity runFlowResult = BeanUtils.copy(runFlowResultDto, RunFlowResultEntity.class);
            return runFlowResultMapper.updateRunFlowResult(runFlowResult);
        } catch (Exception e) {
            logger.error("修改流程输出结果失败", e);
            throw new ContrastBusinessException("修改流程输出结果失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除流程输出结果
     *
     * @param ids 需要删除的流程输出结果主键
     * @return 结果
     */
    @Override
    public int deleteRunFlowResultByIds(Long[] ids) {
        return runFlowResultMapper.deleteRunFlowResultByIds(ids);
    }

    @Override
    public int deleteRunFlowResultById(Long id) throws ContrastBusinessException {
        try {
            return runFlowResultMapper.deleteRunFlowResultById(id);
        } catch (Exception e) {
            logger.error("删除流程输出结果失败", e);
            throw new ContrastBusinessException("删除流程输出结果失败：" + e.getMessage());
        }
    }
}
