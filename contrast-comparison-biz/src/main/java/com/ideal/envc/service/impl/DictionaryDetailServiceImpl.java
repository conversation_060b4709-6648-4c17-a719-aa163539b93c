package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.DictionaryDetailMapper;
import com.ideal.envc.model.entity.DictionaryDetailEntity;
import com.ideal.envc.service.IDictionaryDetailService;
import com.ideal.envc.model.dto.DictionaryDetailDto;
import com.ideal.envc.model.dto.DictionaryDetailQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * 字典详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class DictionaryDetailServiceImpl implements IDictionaryDetailService {
    private final Logger logger = LoggerFactory.getLogger(DictionaryDetailServiceImpl.class);

    private final DictionaryDetailMapper dictionaryDetailMapper;

    public DictionaryDetailServiceImpl(DictionaryDetailMapper dictionaryDetailMapper) {
        this.dictionaryDetailMapper = dictionaryDetailMapper;
    }

    /**
     * 查询字典详情
     *
     * @param id 字典详情主键
     * @return 字典详情
     */
    @Override
    public DictionaryDetailDto selectDictionaryDetailById(Long id) {
        DictionaryDetailEntity dictionaryDetail = dictionaryDetailMapper.selectDictionaryDetailById(id);
        return BeanUtils.copy(dictionaryDetail, DictionaryDetailDto.class);
    }

    /**
     * 查询字典详情列表
     *
     * @param dictionaryDetailQueryDto 字典详情
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 字典详情
     */
    @Override
    public PageInfo<DictionaryDetailDto> selectDictionaryDetailList(DictionaryDetailQueryDto dictionaryDetailQueryDto, Integer pageNum, Integer pageSize) {
        DictionaryDetailEntity query = BeanUtils.copy(dictionaryDetailQueryDto, DictionaryDetailEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<DictionaryDetailEntity> dictionaryDetailList = dictionaryDetailMapper.selectDictionaryDetailList(query);
        return PageDataUtil.toDtoPage(dictionaryDetailList, DictionaryDetailDto.class);
    }

    /**
     * 新增字典详情
     *
     * @param dictionaryDetailDto 字典详情
     * @return 结果
     */
    @Override
    public int insertDictionaryDetail(DictionaryDetailDto dictionaryDetailDto) {
        DictionaryDetailEntity dictionaryDetail = BeanUtils.copy(dictionaryDetailDto, DictionaryDetailEntity.class);
        return dictionaryDetailMapper.insertDictionaryDetail(dictionaryDetail);
    }

    /**
     * 修改字典详情
     *
     * @param dictionaryDetailDto 字典详情
     * @return 结果
     */
    @Override
    public int updateDictionaryDetail(DictionaryDetailDto dictionaryDetailDto) {
        DictionaryDetailEntity dictionaryDetail = BeanUtils.copy(dictionaryDetailDto, DictionaryDetailEntity.class);
        return dictionaryDetailMapper.updateDictionaryDetail(dictionaryDetail);
    }

    /**
     * 批量删除字典详情
     *
     * @param ids 需要删除的字典详情主键
     * @return 结果
     */
    @Override
    public int deleteDictionaryDetailByIds(Long[] ids) {
        return dictionaryDetailMapper.deleteDictionaryDetailByIds(ids);
    }

    /**
     * 根据字典码值获取字典详情信息列表
     *
     * @param code 字典码
     * @return 字典详情列表
     */
    @Override
    public List<DictionaryDetailDto> findDictionaryDetailListByCode(String code) {
        logger.info("根据字典码获取字典详情列表: {}", code);
        if (code == null || code.trim().isEmpty()) {
            return java.util.Collections.emptyList();
        }

        // 调用Mapper方法查询数据
        List<DictionaryDetailEntity> entityList = dictionaryDetailMapper.selectDictionaryDetailListByCode(code);

        // 将Entity列表转换为DTO列表
        return BeanUtils.copy(entityList, DictionaryDetailDto.class);
    }
}
