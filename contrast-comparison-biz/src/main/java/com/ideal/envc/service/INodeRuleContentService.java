package com.ideal.envc.service;

import com.ideal.envc.model.dto.NodeRuleContentDto;
import com.ideal.envc.model.dto.NodeRuleContentQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 节点关系规则Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface INodeRuleContentService {
    /**
     * 查询节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 节点关系规则
     */
    NodeRuleContentDto selectNodeRuleContentById(Long id);

    /**
     * 查询节点关系规则列表
     *
     * @param nodeRuleContentQueryDto 节点关系规则
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点关系规则集合
     */
    PageInfo<NodeRuleContentDto> selectNodeRuleContentList(NodeRuleContentQueryDto nodeRuleContentQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增节点关系规则
     *
     * @param nodeRuleContentDto 节点关系规则
     * @return 结果
     */
    int insertNodeRuleContent(NodeRuleContentDto nodeRuleContentDto);

    /**
     * 修改节点关系规则
     *
     * @param nodeRuleContentDto 节点关系规则
     * @return 结果
     */
    int updateNodeRuleContent(NodeRuleContentDto nodeRuleContentDto);

    /**
     * 批量删除节点关系规则
     *
     * @param ids 需要删除的节点关系规则主键集合
     * @return 结果
     */
    int deleteNodeRuleContentByIds(Long[] ids);
}
