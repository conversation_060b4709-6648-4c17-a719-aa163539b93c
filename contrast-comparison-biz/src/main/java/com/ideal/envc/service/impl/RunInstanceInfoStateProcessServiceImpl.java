package com.ideal.envc.service.impl;

import com.ideal.envc.component.TaskCounterComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.dto.RunInstanceStateMessage;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.producer.RunInstanceStateProducer;
import com.ideal.envc.service.IRunInstanceInfoStateProcessService;
import com.ideal.envc.service.IRunInstanceStateProcessService;
import org.redisson.api.RLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 运行实例信息状态处理服务实现
 * 
 * 主要功能：
 * 1. 处理规则层状态变更后的实例详情状态计算和变更
 * 2. 管理实例详情计数器，实现规则计数器归0时的状态更新
 * 3. 确保在实例详情状态更新成功后才清理计数器
 * 
 * <AUTHOR>
 */
@Service
public class RunInstanceInfoStateProcessServiceImpl implements IRunInstanceInfoStateProcessService {
    private static final Logger logger = LoggerFactory.getLogger(RunInstanceInfoStateProcessServiceImpl.class);
    
    /** 消息过期时间（毫秒） */
    private static final long MESSAGE_EXPIRE_TIME = 30000L;
    
    private final RunInstanceInfoMapper runInstanceInfoMapper;
    private final RunRuleMapper runRuleMapper;
    private final TaskCounterComponent taskCounterComponent;
    private final IRunInstanceStateProcessService runInstanceStateProcessService;

    public RunInstanceInfoStateProcessServiceImpl(RunInstanceInfoMapper runInstanceInfoMapper,
                                                  RunRuleMapper runRuleMapper,
                                                  TaskCounterComponent taskCounterComponent, IRunInstanceStateProcessService runInstanceStateProcessService) {
        this.runInstanceInfoMapper = runInstanceInfoMapper;
        this.runRuleMapper = runRuleMapper;
        this.taskCounterComponent = taskCounterComponent;
        this.runInstanceStateProcessService = runInstanceStateProcessService;
    }

    /**
     * 处理实例详情状态更新
     * 
     * @param instanceInfoId 实例详情ID
     * @param ruleId 规则ID
     * @param messageTimestamp 消息时间戳
     * @return 是否处理成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processInstanceInfoStateUpdate(Long instanceInfoId, Long ruleId, Long messageTimestamp) {
        try {
            // 参数校验
            if (instanceInfoId == null || ruleId == null) {
                logger.warn("参数不能为空，instanceInfoId: {}, ruleId: {}", instanceInfoId, ruleId);
                return false;
            }

            // 1. 根据instanceInfoId查询对应的运行实例信息
            RunInstanceInfoEntity instanceInfo = runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId);
            if (instanceInfo == null) {
                logger.warn("未找到对应的运行实例信息，instanceInfoId: {}", instanceInfoId);
                // 记录不存在，认为处理成功，避免重试
                return true;
            }

            // 2. 检查消息是否过期
            Long currentTimestamp = getCurrentTimestamp(instanceInfo);
            if (isMessageExpired(messageTimestamp, currentTimestamp)) {
                return true;
            }

            // 3. 检查和减少实例详情计数器
            int counterValue = decrementInstanceInfoCounter(instanceInfoId, ruleId);
            
            // 4. 处理计数器结果
            return handleCounterValue(instanceInfoId, ruleId, messageTimestamp, instanceInfo, currentTimestamp, counterValue);
            
        } catch (Exception e) {
            logger.error("处理实例详情状态更新异常，instanceInfoId: {}, ruleId: {}", instanceInfoId, ruleId, e);
            // 需要重试
            return false;
        }
    }
    
    /**
     * 获取当前时间戳
     * @param instanceInfo 实例详情实体
     * @return 当前时间戳
     */
    Long getCurrentTimestamp(RunInstanceInfoEntity instanceInfo) {
        return instanceInfo.getUpdateTime() != null ? 
            instanceInfo.getUpdateTime().getTime() : System.currentTimeMillis();
    }
    
    /**
     * 检查消息是否过期
     * @param messageTimestamp 消息时间戳
     * @param currentTimestamp 当前时间戳
     * @return 是否过期
     */
    boolean isMessageExpired(Long messageTimestamp, Long currentTimestamp) {
        if (messageTimestamp != null && currentTimestamp > messageTimestamp + MESSAGE_EXPIRE_TIME) {
            logger.warn("消息已过期，跳过处理，messageTimestamp: {}, currentTimestamp: {}", 
                messageTimestamp, currentTimestamp);
            // 过期消息，认为处理成功，避免重试
            return true;
        }
        return false;
    }
    
    /**
     * 处理计数器值
     * @param instanceInfoId 实例详情ID
     * @param ruleId 规则ID
     * @param messageTimestamp 消息时间戳
     * @param instanceInfo 实例详情实体
     * @param currentTimestamp 当前时间戳
     * @param counterValue 计数器值
     * @return 处理结果
     */
    private boolean handleCounterValue(Long instanceInfoId, Long ruleId, Long messageTimestamp, 
                                      RunInstanceInfoEntity instanceInfo, Long currentTimestamp, int counterValue) {
        if (counterValue == 0) {
            logger.info("实例详情计数器已归0，开始进行状态计算和更新，instanceInfoId: {}", instanceInfoId);
            // 再次查询实例信息，确保获取最新状态
            RunInstanceInfoEntity latestInstanceInfo = runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId);
            if (latestInstanceInfo != null) {
                // 使用最新的实例信息
                instanceInfo = latestInstanceInfo;
            }
            return processZeroCounter(instanceInfoId, instanceInfo, currentTimestamp);
        } else if (counterValue > 0) {
            logger.info("实例详情计数器未归0，暂不进行状态更新，instanceInfoId: {}, counterValue: {}", 
                instanceInfoId, counterValue);
            // 计数器未归0，认为处理成功
            return true;
        } else {
            logger.error("实例详情计数器减值失败，instanceInfoId: {}, ruleId: {}", instanceInfoId, ruleId);
            // 计数器减值失败，需要重试
            return false;
        }
    }
    
    /**
     * 处理计数器为0的情况
     * @param instanceInfoId 实例详情ID
     * @param instanceInfo 实例详情实体
     * @param currentTimestamp 当前时间戳
     * @return 处理结果
     */
    private boolean processZeroCounter(Long instanceInfoId, RunInstanceInfoEntity instanceInfo, Long currentTimestamp) {
        String businessLockKey = "instance_info_state_process:" + instanceInfoId;
        boolean processSuccess = false;
        RLock lock = null;
        boolean isLocked = false;
        try {
            // 尝试获取业务处理锁
            lock = taskCounterComponent.getBusinessLock(businessLockKey, 30);
            if(lock==null){
                logger.info("获取业务处理锁失败，可能其他线程正在处理，instanceInfoId: {}", instanceInfoId);
                return processSuccess;
            }
            isLocked = lock.tryLock(30, TaskCounterComponent.LOCK_TIMEOUT, TimeUnit.SECONDS);
            if(!isLocked){
                logger.info("获取业务处理锁失败，可能其他线程正在处理，instanceInfoId: {}", instanceInfoId);
                return processSuccess;
            }
            processSuccess= processWithLock(instanceInfoId, instanceInfo, currentTimestamp);
        } catch (Exception e) {
            logger.error("业务处理过程异常，instanceInfoId: {}", instanceInfoId, e);
            // 如果处理成功了但是在finally中出现异常，不要返回失败
            return processSuccess;
        }finally {
            if (lock != null && isLocked  ) {
                lock.unlock();
            }
        }
        return processSuccess;
    }
    
    /**
     * 在获取锁的情况下处理
     * @param instanceInfoId 实例详情ID
     * @param instanceInfo 实例详情实体
     * @param currentTimestamp 当前时间戳
     * @return 处理结果
     */
    private boolean processWithLock(Long instanceInfoId, RunInstanceInfoEntity instanceInfo, Long currentTimestamp) throws ContrastBusinessException {
        // 重新检查计数器，确保没有被其他线程处理
        long recheck = taskCounterComponent.getInstanceInfoCounterValue(instanceInfoId);
        if (recheck != 0) {
            logger.info("实例详情计数器值已变化，跳过处理，instanceInfoId: {}, counterValue: {}", 
                instanceInfoId, recheck);
            return true;
        }
        
        // 计算汇总状态
        int[] statusAndResult = calculateInstanceInfoStateAndResult(instanceInfoId);
        int status = statusAndResult[0];
        int resultVal = statusAndResult[1];

        // 检查状态是否需要更新
        if (isStateUnchanged(instanceInfo, status, resultVal)) {
            return handleUnchangedState(instanceInfoId, instanceInfo, currentTimestamp);
        }

        // 更新状态
        return updateState(instanceInfoId, instanceInfo, currentTimestamp, status, resultVal);
    }
    
    /**
     * 检查状态是否未变化
     * @param instanceInfo 实例详情实体
     * @param status 计算的状态
     * @param resultVal 计算的结果
     * @return 是否未变化
     */
    boolean isStateUnchanged(RunInstanceInfoEntity instanceInfo, int status, int resultVal) {
        return instanceInfo.getState() != null && instanceInfo.getResult() != null &&
               instanceInfo.getState() == status && instanceInfo.getResult() == resultVal;
    }
    
    /**
     * 处理状态未变化的情况
     * @param instanceInfoId 实例详情ID
     * @param instanceInfo 实例详情实体
     * @param currentTimestamp 当前时间戳
     * @return 处理结果
     */
    private boolean handleUnchangedState(Long instanceInfoId, RunInstanceInfoEntity instanceInfo, Long currentTimestamp) {
        logger.info("运行实例信息状态未发生变化，无需更新，instanceInfoId: {}", instanceInfoId);
        // 状态未变化，但处理已完成，清理计数器并触发上层处理
        clearInstanceInfoCounter(instanceInfoId);
        runInstanceStateProcessService.processInstanceStateUpdate(
            instanceInfo.getEnvcRunInstanceId(), instanceInfoId, currentTimestamp);
        return true;
    }
    
    /**
     * 更新状态
     * @param instanceInfoId 实例详情ID
     * @param instanceInfo 实例详情实体
     * @param currentTimestamp 当前时间戳
     * @param status 状态
     * @param resultVal 结果
     * @return 更新结果
     */
    private boolean updateState(Long instanceInfoId, RunInstanceInfoEntity instanceInfo, 
                              Long currentTimestamp, int status, int resultVal) {
        boolean updateSuccess = updateInstanceInfoStateAndResult(instanceInfoId, status, resultVal, currentTimestamp);
        
        if (updateSuccess) {
            logger.info("更新运行实例信息状态成功，instanceInfoId: {}, status: {}, resultVal: {}", 
                instanceInfoId, status, resultVal);
            
            // 只有状态更新成功后才清理计数器
            clearInstanceInfoCounter(instanceInfoId);
            
            // 调用实例状态处理服务，触发上层实例状态更新
            runInstanceStateProcessService.processInstanceStateUpdate(
                instanceInfo.getEnvcRunInstanceId(), instanceInfoId, currentTimestamp);
            return true;
        } else {
            logger.warn("更新运行实例信息状态失败，保留计数器以便重试，instanceInfoId: {}", instanceInfoId);
            // 状态更新失败，不清理计数器，保持数据一致性
            return false;
        }
    }

    /**
     * 检查并减少实例详情计数器
     * 
     * @param instanceInfoId 实例详情ID
     * @param ruleId 规则ID
     * @return 减少后的计数器值，-1表示减少失败
     */
    @Override
    public int decrementInstanceInfoCounter(Long instanceInfoId, Long ruleId) {
        try {
            // 不再重复查询实例信息，避免多次调用selectRunInstanceInfoById
            long counterValue = taskCounterComponent.decrementInstanceInfoCounter(instanceInfoId);
            logger.info("实例详情计数器减值操作完成，instanceInfoId: {}, ruleId: {}, counterValue: {}", 
                instanceInfoId, ruleId, counterValue);
            return (int) counterValue;
        } catch (ContrastBusinessException e) {
            logger.error("实例详情计数器减值失败，instanceInfoId: {}, ruleId: {}", instanceInfoId, ruleId, e);
            return -1;
        }
    }

    /**
     * 计算实例详情状态和结果
     * 
     * @param instanceInfoId 实例详情ID
     * @return [状态, 结果] 数组
     */
    @Override
    public int[] calculateInstanceInfoStateAndResult(Long instanceInfoId) {
        // 根据instanceInfoId查询对应的运行规则对象集合
        List<RunRuleEntity> rules = runRuleMapper.selectRulesByInstanceInfoId(instanceInfoId);
        if (rules.isEmpty()) {
            logger.warn("未找到关联的运行规则，instanceInfoId: {}", instanceInfoId);
            // 默认返回运行中状态
            return new int[]{0, -1};
        }

        int status = 0;
        int resultVal = -1;
        
        /*
         * 计算status状态
         * 状态优先级：终止(2) > 运行中(0) > 已完成(1)
         * 只要有一个终止，整体就是终止状态
         */
        // 1表示已完成
        boolean allCompleted = rules.stream().allMatch(rule -> rule.getState() != null && rule.getState() == 1);
        // 2表示终止
        boolean hasTerminated = rules.stream().anyMatch(rule -> rule.getState() != null && rule.getState() == 2);
        // 0表示运行中（只有在没有终止状态的情况下，才考虑运行中状态）
        boolean hasRunning = !hasTerminated && rules.stream().anyMatch(rule -> rule.getState() != null && rule.getState() == 0);

        // 按照规则计算status
        if (allCompleted) {
            // 已完成
            status = 1;
        } else if (hasTerminated) {
            // 终止
            status = 2;
        } else if (hasRunning) {
            // 运行中
            status = 0;
        }

        /*
         * 计算resultVal结果
         * 结果优先级：失败(1) > 运行中(-1) > 成功(0)
         * 只要有一个失败，整体就是失败结果
         */
        // 1表示不一致/失败
        boolean hasFailed = rules.stream().anyMatch(rule -> rule.getResult() != null && rule.getResult() == 1);
        // -1表示运行中（只有在没有失败状态的情况下，才考虑运行中状态）
        boolean hasRunningResult = !hasFailed && rules.stream().anyMatch(rule -> rule.getResult() != null && rule.getResult() == -1);
        // 0表示一致/成功
        boolean allSuccess = rules.stream().allMatch(rule -> rule.getResult() != null && rule.getResult() == 0);

        // 按照规则计算resultVal
        if (hasFailed) {
            // 不一致/失败
            resultVal = 1;
        } else if (hasRunningResult) {
            // 运行中
            resultVal = -1;
        } else if (allSuccess) {
            // 一致/成功
            resultVal = 0;
        }
        
        return new int[]{status, resultVal};
    }

    /**
     * 更新实例详情状态和结果
     * 
     * @param instanceInfoId 实例详情ID
     * @param state 状态
     * @param result 结果
     * @param currentTimestamp 当前时间戳
     * @return 是否更新成功
     */
    @Override
    public boolean updateInstanceInfoStateAndResult(Long instanceInfoId, int state, int result, Long currentTimestamp) {
        try {
            /*
             * 使用当前数据库时间戳作为乐观锁条件更新数据
             * 防止并发更新导致的数据不一致问题
             */
            int updatedRows = runInstanceInfoMapper.updateStateAndResultByIdAndTimestamp(
                instanceInfoId, state, result);
            
            return updatedRows > 0;
        } catch (Exception e) {
            logger.error("更新实例详情状态和结果失败，instanceInfoId: {}", instanceInfoId, e);
            return false;
        }
    }

    /**
     * 清理实例详情计数器
     * 
     * @param instanceInfoId 实例详情ID
     * @return 是否清理成功
     */
    @Override
    public boolean clearInstanceInfoCounter(Long instanceInfoId) {
        try {
            taskCounterComponent.clearInstanceInfoCounter(instanceInfoId);
            logger.info("成功清理实例详情计数器，instanceInfoId: {}", instanceInfoId);
            return true;
        } catch (ContrastBusinessException e) {
            logger.error("清理实例详情计数器失败，instanceInfoId: {}", instanceInfoId, e);
            return false;
        }
    }

}