package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.mapper.TaskMapper;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.TaskEntity;
import com.ideal.envc.service.IJobOperateService;
import com.ideal.jobapi.core.apiclient.IdealXxlJobApiUtil;
import com.ideal.jobapi.core.enums.IdealStateType;
import com.ideal.jobapi.core.model.IdealXxlJobInfoDto;
import com.xxl.job.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 定时任务处理工具类
 * <AUTHOR>
 */
@Service
public class JobOperateServiceImpl implements IJobOperateService {

    private static final Logger logger = LoggerFactory.getLogger(JobOperateServiceImpl.class);
    private final TaskMapper taskMapper;

    public JobOperateServiceImpl(TaskMapper taskMapper) {
        this.taskMapper = taskMapper;
    }

    @Override
    public Integer createAndStartJob(ContrastScheduleJobTaskDto contrastScheduleJobTaskDto) throws ScheduleJobOperateException {
        logger.info("start call schedule job service, taskId is {}, taskName is {}", contrastScheduleJobTaskDto.getTaskId(), contrastScheduleJobTaskDto.getTaskName());
        
        Integer xxJobId;
        IdealXxlJobInfoDto idealJobInfoModel = IdealXxlJobInfoDto.getSimpleCronInstance(
                contrastScheduleJobTaskDto.getTaskName(),
                contrastScheduleJobTaskDto.getJobHandlerName(),
                contrastScheduleJobTaskDto.getCreateName(),
                contrastScheduleJobTaskDto.getCron()
        );
        idealJobInfoModel.setIdealStateType(IdealStateType.RUNNING);
        idealJobInfoModel.setIdealExecutorParam(JSON.toJSONString(contrastScheduleJobTaskDto));
        ReturnT<String> sd = IdealXxlJobApiUtil.addJob(idealJobInfoModel);
        logger.info("start end schedule job service, taskId is {}, taskName is {}", contrastScheduleJobTaskDto.getTaskId(), contrastScheduleJobTaskDto.getTaskName());

        if(sd == null) {
            throw new ScheduleJobOperateException("TaskEntity ReturnT is null!");
        }
        if(sd.getCode() != 200) {
            logger.info("create task name is: {}, result is fail, return code:{}, error message:{}", contrastScheduleJobTaskDto.getTaskName(), sd.getCode(), sd.getMsg());
            throw new ScheduleJobOperateException(String.valueOf(sd.getCode()), sd.getMsg());
        }

        String res = sd.toString();
        logger.info("job returns parameters: {}", res);
        if(sd.getContent() == null) {
            throw new ScheduleJobOperateException("TaskEntity xxJob id is null!");
        }
        xxJobId = Integer.valueOf(sd.getContent());
        logger.info("create task name is [{}] success, schedule task id: {}", contrastScheduleJobTaskDto.getTaskName(), xxJobId);
        return xxJobId;
    }

    @Override
    public Boolean startJob(Integer scheduleJobId) {
        if(scheduleJobId == null || scheduleJobId <= 0) {
            logger.error("start schedule task id is null!");
            return false;
        }
        ReturnT<String> startResult = IdealXxlJobApiUtil.startJob(scheduleJobId);
        if(startResult.getCode() != 200) {
            logger.error("start schedule task id {} is fail! cause is {}", scheduleJobId, startResult.getMsg());
            return false;
        }
        return true;
    }

    @Override
    public boolean modifyJob(ContrastScheduleJobTaskDto contrastScheduleJobTaskDto) {
        IdealXxlJobInfoDto idealJobInfoModel = IdealXxlJobInfoDto.getSimpleCronInstance(
                contrastScheduleJobTaskDto.getTaskName(),
                contrastScheduleJobTaskDto.getJobHandlerName(),
                contrastScheduleJobTaskDto.getCreateName(),
                contrastScheduleJobTaskDto.getCron()
        );
        idealJobInfoModel.setId(Math.toIntExact(contrastScheduleJobTaskDto.getScheduleJobId()));
        idealJobInfoModel.setIdealExecutorParam(JSON.toJSONString(contrastScheduleJobTaskDto));
        ReturnT<String> sd = IdealXxlJobApiUtil.updateJob(idealJobInfoModel);
        logger.info("modify schedule task id {} ! response message is {}", contrastScheduleJobTaskDto.getScheduleJobId(), sd.getMsg());
        return sd.getCode() == 200;
    }

    @Override
    public boolean stopJob(Integer scheduleJobId) {
        ReturnT<String> sd = IdealXxlJobApiUtil.stopJob(scheduleJobId);
        logger.info("stop schedule task id {} ! response message is {}", scheduleJobId, sd.getMsg());
        return sd.getCode() == 200;
    }

    @Override
    public boolean removeJob(Integer scheduleJobId) {
        if(scheduleJobId == null || scheduleJobId <= 0) {
            logger.error("remove schedule task id is null or invalid!");
            return false;
        }

        try {
            // 先停止任务，再删除任务
            ReturnT<String> stopResult = IdealXxlJobApiUtil.stopJob(scheduleJobId);
            if(stopResult.getCode() != 200) {
                logger.warn("stop schedule task id {} before removing failed! cause is {}", scheduleJobId, stopResult.getMsg());
                // 即使停止失败，仍然尝试删除
            }

            ReturnT<String> removeResult = IdealXxlJobApiUtil.removeJob(scheduleJobId);
            if(removeResult.getCode() != 200) {
                logger.error("remove schedule task id {} failed! cause is {}", scheduleJobId, removeResult.getMsg());
                return false;
            }

            logger.info("remove schedule task id {} successfully!", scheduleJobId);
            return true;
        } catch (Exception e) {
            logger.error("remove schedule task id {} exception: {}", scheduleJobId, e.getMessage(), e);
            return false;
        }
    }
}
