package com.ideal.envc.service;

import com.ideal.envc.model.dto.RunFlowResultDto;
import com.ideal.envc.model.dto.RunFlowResultQueryDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.exception.ContrastBusinessException;

/**
 * 流程输出结果Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IRunFlowResultService {
    /**
     * 查询流程输出结果
     *
     * @param id 流程输出结果主键
     * @return 流程输出结果
     */
    RunFlowResultDto selectRunFlowResultById(Long id) throws ContrastBusinessException;

    /**
     * 查询流程输出结果列表
     *
     * @param runFlowResultQueryDto 流程输出结果
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 流程输出结果集合
     */
    PageInfo<RunFlowResultDto> selectRunFlowResultList(RunFlowResultQueryDto runFlowResultQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;

    /**
     * 新增流程输出结果
     *
     * @param runFlowResultDto 流程输出结果
     * @return 结果
     */
    int insertRunFlowResult(RunFlowResultDto runFlowResultDto) throws ContrastBusinessException;

    /**
     * 修改流程输出结果
     *
     * @param runFlowResultDto 流程输出结果
     * @return 结果
     */
    int updateRunFlowResult(RunFlowResultDto runFlowResultDto) throws ContrastBusinessException;

    /**
     * 批量删除流程输出结果
     *
     * @param ids 需要删除的流程输出结果主键集合
     * @return 结果
     */
    int deleteRunFlowResultByIds(Long[] ids) throws ContrastBusinessException;

    /**
     * 删除流程输出结果
     *
     * @param id 流程输出结果主键
     * @return 结果
     */
    int deleteRunFlowResultById(Long id) throws ContrastBusinessException;
}
