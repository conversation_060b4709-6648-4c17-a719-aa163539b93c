package com.ideal.envc.service;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.exception.ScheduleJobOperateException;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.TaskCronUpdateDto;
import com.ideal.envc.model.dto.TaskDto;
import com.ideal.envc.model.dto.TaskOperateResultDto;
import com.ideal.envc.model.dto.TaskPlanListDto;
import com.ideal.envc.model.dto.TaskQueryDto;
import com.ideal.envc.model.dto.TaskStartOrStopDto;
import com.ideal.envc.model.dto.UserDto;

import java.util.List;

/**
 * 任务Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface ITaskService {
    /**
     * 查询任务
     *
     * @param id 任务主键
     * @return 任务
     */
    TaskDto selectTaskById(Long id);

    /**
     * 查询任务列表
     *
     * @param taskQueryDto 任务
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 任务集合
     * @throws ContrastBusinessException 业务异常
     */
    PageInfo<TaskDto> selectTaskList(TaskQueryDto taskQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;

    /**
     * 新增任务
     *
     * @param taskDto 任务
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int insertTask(TaskDto taskDto) throws ContrastBusinessException;


    /**
     * 新增任务
     *
     * @param taskDto 任务
     * @return 结果
     */
    Long createTask(TaskDto taskDto, UserDto userDto) throws ScheduleJobOperateException, ContrastBusinessException;

    /**
     * 任务更新
     * @param taskDto 任务DTO
     * @param userDto 用户DTO
     * @return 更新结果
     * @throws ContrastBusinessException 业务异常
     */
    int modifyTask(TaskDto taskDto,UserDto userDto) throws ContrastBusinessException;

    /**
     * 操作任务（启动或停止）
     *
     * @param taskStartOrStopDto 任务启停参数
     * @param userDto 用户信息
     * @throws ContrastBusinessException 业务异常
     */
    void operateTasks(TaskStartOrStopDto taskStartOrStopDto, UserDto userDto) throws ContrastBusinessException;

    /**
     * 修改任务
     *
     * @param taskDto 任务
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int updateTask(TaskDto taskDto) throws ContrastBusinessException;

    /**
     * 批量删除任务
     *
     * @param ids 需要删除的任务主键集合
     * @return 结果
     */
    int deleteTaskByIds(Long[] ids);

    /**
     * 检查同一方案ID和同一cron表达式的任务是否已存在
     *
     * @param envcPlanId 方案ID
     * @param cron cron表达式
     * @return 是否存在
     */
    boolean checkTaskExists(Long envcPlanId, String cron);

    /**
     * 检查除指定ID外的同一方案ID和同一cron表达式的任务是否已存在
     *
     * @param id 任务ID
     * @param envcPlanId 方案ID
     * @param cron cron表达式
     * @return 是否存在
     */
    boolean checkTaskExistsExcludeId(Long id, Long envcPlanId, String cron);

    /**
     * 更新任务周期表达式
     *
     * @param taskCronUpdateDto 任务周期表达式更新数据
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int updateTaskCron(TaskCronUpdateDto taskCronUpdateDto, UserDto userDto) throws ContrastBusinessException;

    /**
     * 查询任务方案列表
     *
     * @param taskQueryDto 任务查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 任务方案列表
     */
    PageInfo<TaskPlanListDto> selectTaskPlanList(TaskQueryDto taskQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 根据任务ID集合查询定时ID集合
     *
     * @param taskIds 任务ID集合
     * @return 定时ID集合
     */
    List<Long> selectScheduledIdsByTaskIds(List<Long> taskIds);

    /**
     * 根据任务ID更新定时ID和状态
     *
     * @param taskId 任务ID
     * @param scheduledId 定时ID
     * @param state 任务状态
     * @return 更新的行数
     */
    int updateTaskScheduledIdAndState(Long taskId, Long scheduledId, Integer state);

    /**
     * 删除定时任务并删除任务
     *
     * @param taskIdList 任务ID列表
     * @param userDto 用户信息
     * @return 操作结果，包含成功和失败的任务ID列表
     */
    TaskOperateResultDto removeScheduleJob(List<Long> taskIdList, UserDto userDto);

    /**
     * 查询任务详情（包含方案名称和中心名称）
     *
     * @param id 任务主键
     * @return 任务详情
     * @throws ContrastBusinessException 业务异常
     */
    TaskDto selectTaskDetailById(Long id) throws ContrastBusinessException;
}
