package com.ideal.envc.service;

import com.ideal.envc.exception.ScheduleJobOperateException;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.UserDto;

/**
 * xxJob对接处理类
 * <AUTHOR>
 */
public interface IJobOperateService {

    /**
     * 创建任务
     * @param contrastScheduleJobTaskDto  任务创建实体
     * @return xxJob服务对应任务的Id
     */
    Integer createAndStartJob(ContrastScheduleJobTaskDto contrastScheduleJobTaskDto)  throws ScheduleJobOperateException;

    /**
     * 修改任务
     * @param contrastScheduleJobTaskDto  任务修改实体
     * @return 操作结果
     */
    boolean modifyJob(ContrastScheduleJobTaskDto contrastScheduleJobTaskDto);

    /**
     * 停止任务
     * @param scheduleJobId xxjob服务对应任务的Id
     * @return 操作结果
     */
    boolean stopJob(Integer scheduleJobId);

    /**
     * 启动任务
     * @param scheduleJobId xxjob服务对应任务的Id
     * @return 操作结果
     */
    Boolean startJob(Integer scheduleJobId);

    /**
     * 删除定时任务
     * @param scheduleJobId xxjob服务对应任务的Id
     * @return 操作结果
     */
    boolean removeJob(Integer scheduleJobId);
}
