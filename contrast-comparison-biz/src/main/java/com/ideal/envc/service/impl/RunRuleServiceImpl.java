package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.service.IRunRuleService;
import com.ideal.envc.model.dto.RunRuleDto;
import com.ideal.envc.model.dto.RunRuleQueryDto;

import java.util.List;

/**
 * 节点规则结果Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class RunRuleServiceImpl implements IRunRuleService {
    private final RunRuleMapper runRuleMapper;

    public RunRuleServiceImpl(RunRuleMapper runRuleMapper) {
        this.runRuleMapper = runRuleMapper;
    }

    /**
     * 查询节点规则结果
     *
     * @param id 节点规则结果主键
     * @return 节点规则结果
     */
    @Override
    public RunRuleDto selectRunRuleById(Long id) {
        RunRuleEntity runRule = runRuleMapper.selectRunRuleById(id);
        return BeanUtils.copy(runRule, RunRuleDto.class);
    }

    /**
     * 查询节点规则结果列表
     *
     * @param runRuleQueryDto 节点规则结果
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点规则结果
     */
    @Override
    public PageInfo<RunRuleDto> selectRunRuleList(RunRuleQueryDto runRuleQueryDto, Integer pageNum, Integer pageSize) {
        RunRuleEntity query = BeanUtils.copy(runRuleQueryDto, RunRuleEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<RunRuleEntity> runRuleList = runRuleMapper.selectRunRuleList(query);
        return PageDataUtil.toDtoPage(runRuleList, RunRuleDto.class);
    }

    /**
     * 新增节点规则结果
     *
     * @param runRuleDto 节点规则结果
     * @return 结果
     */
    @Override
    public int insertRunRule(RunRuleDto runRuleDto) {
        RunRuleEntity runRule = BeanUtils.copy(runRuleDto, RunRuleEntity.class);
        return runRuleMapper.insertRunRule(runRule);
    }

    /**
     * 修改节点规则结果
     *
     * @param runRuleDto 节点规则结果
     * @return 结果
     */
    @Override
    public int updateRunRule(RunRuleDto runRuleDto) {
        RunRuleEntity runRule = BeanUtils.copy(runRuleDto, RunRuleEntity.class);
        return runRuleMapper.updateRunRule(runRule);
    }

    /**
     * 批量删除节点规则结果
     *
     * @param ids 需要删除的节点规则结果主键
     * @return 结果
     */
    @Override
    public int deleteRunRuleByIds(Long[] ids) {
        return runRuleMapper.deleteRunRuleByIds(ids);
    }
}
