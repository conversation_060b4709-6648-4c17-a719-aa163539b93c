package com.ideal.envc.service;

import com.ideal.envc.model.dto.DictionaryDto;
import com.ideal.envc.model.dto.DictionaryQueryDto;
import com.github.pagehelper.PageInfo;

/**
 * 字典码Service接口
 *
 * <AUTHOR>
 */
public interface IDictionaryService {
    /**
     * 查询字典码
     *
     * @param id 字典码主键
     * @return 字典码
     */
    DictionaryDto selectDictionaryById(Long id);

    /**
     * 查询字典码列表
     *
     * @param dictionaryQueryDto 字典码
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 字典码集合
     */
    PageInfo<DictionaryDto> selectDictionaryList(DictionaryQueryDto dictionaryQueryDto, Integer pageNum, Integer pageSize);

    /**
     * 新增字典码
     *
     * @param dictionaryDto 字典码
     * @return 结果
     */
    int insertDictionary(DictionaryDto dictionaryDto);

    /**
     * 修改字典码
     *
     * @param dictionaryDto 字典码
     * @return 结果
     */
    int updateDictionary(DictionaryDto dictionaryDto);

    /**
     * 批量删除字典码
     *
     * @param ids 需要删除的字典码主键集合
     * @return 结果
     */
    int deleteDictionaryByIds(Long[] ids);

    /**
     * 验证字典码是否存在
     *
     * @param code 字典码
     * @return 验证结果，true表示存在，false表示不存在
     */
    Boolean validateDictionaryCode(String code);
}
