package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.NodeRuleContentEntity;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.service.INodeRelationService;
import com.ideal.envc.model.dto.NodeRelationDto;
import com.ideal.envc.model.dto.NodeRelationQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;
import com.ideal.envc.model.dto.NodeRelationSaveDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.common.util.batch.BatchHandler;
import com.github.pagehelper.PageHelper;
import com.ideal.envc.model.bean.NodeRelationListBean;
import com.ideal.envc.model.dto.NodeRelationListDto;

import java.util.ArrayList;
import java.util.List;

/**
 * 节点关系规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class NodeRelationServiceImpl implements INodeRelationService {
    private final Logger logger = LoggerFactory.getLogger(NodeRelationServiceImpl.class);

    private final NodeRelationMapper nodeRelationMapper;
    private final NodeRuleContentMapper nodeRuleContentMapper;
    private final BatchHandler batchHandler;

    public NodeRelationServiceImpl(NodeRelationMapper nodeRelationMapper, NodeRuleContentMapper nodeRuleContentMapper, BatchHandler batchHandler) {
        this.nodeRelationMapper = nodeRelationMapper;
        this.nodeRuleContentMapper = nodeRuleContentMapper;
        this.batchHandler = batchHandler;
    }

    /**
     * 查询节点关系规则
     *
     * @param id 节点关系规则主键
     * @return 节点关系规则
     */
    @Override
    public NodeRelationDto selectNodeRelationById(Long id) {
        NodeRelationEntity nodeRelation = nodeRelationMapper.selectNodeRelationById(id);
        return BeanUtils.copy(nodeRelation, NodeRelationDto.class);
    }

    /**
     * 查询节点关系规则列表
     *
     * @param queryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点关系规则列表
     */
    @Override
    public PageInfo<NodeRelationListDto> selectNodeRelationList(NodeRelationQueryDto queryDto, Integer pageNum, Integer pageSize) {
        logger.info("查询节点关系规则列表，查询条件：{}", queryDto);
        
        // 转换查询条件
        NodeRelationEntity queryEntity = BeanUtils.copy(queryDto, NodeRelationEntity.class);
        
        // 设置分页
        PageHelper.startPage(pageNum, pageSize);
        
        // 查询数据
        List<NodeRelationListBean> beanList = nodeRelationMapper.selectNodeRelationList(queryEntity);
        
        // 转换为DTO
        List<NodeRelationListDto> dtoList = BeanUtils.copy(beanList, NodeRelationListDto.class);
        
        return new PageInfo<>(dtoList);
    }

    /**
     * 新增节点关系规则
     *
     * @param nodeRelationSaveDto 节点关系规则
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertNodeRelation(NodeRelationSaveDto nodeRelationSaveDto,UserDto userDto) {
        if (nodeRelationSaveDto == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        if (nodeRelationSaveDto.getEnvcSystemComputerNodeId() == null) {
            throw new IllegalArgumentException("节点关系ID不能为空");
        }
        if (nodeRelationSaveDto.getModel() == null) {
            throw new IllegalArgumentException("模式不能为空");
        }

        NodeRelationEntity nodeRelationEntity = BeanUtils.copy(nodeRelationSaveDto, NodeRelationEntity.class);
        nodeRelationEntity.setCreatorId(userDto.getId());
        nodeRelationEntity.setCreatorName(userDto.getFullName());
        nodeRelationEntity.setUpdatorId(userDto.getId());
        nodeRelationEntity.setUpdatorName(userDto.getFullName());
        //增加后端处理逻辑，当选择方式是全部时，规则类型默认为匹配。
        if(nodeRelationEntity.getWay() == 0){
            //匹配
            nodeRelationEntity.setRuleType(0);
            //非子集
            nodeRelationEntity.setChildLevel(1);
        }
        int result = nodeRelationMapper.insertNodeRelation(nodeRelationEntity);

        if (result > 0 && nodeRelationSaveDto.getContent() != null && !nodeRelationSaveDto.getContent().isEmpty()) {
            NodeRuleContentEntity nodeRuleContentEntity = new NodeRuleContentEntity();
            nodeRuleContentEntity.setEnvcNodeRelationId(nodeRelationEntity.getId());
            nodeRuleContentEntity.setRuleContent(nodeRelationSaveDto.getContent());
            nodeRuleContentMapper.insertNodeRuleContent(nodeRuleContentEntity);
        }

        return result;
    }

    /**
     * 修改节点关系规则
     *
     * @param nodeRelationSaveDto 节点关系规则（含规则内容）
     * @param userDto 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = {ContrastBusinessException.class, RuntimeException.class})
    public int updateNodeRelation(NodeRelationSaveDto nodeRelationSaveDto, UserDto userDto) throws ContrastBusinessException {
        if (nodeRelationSaveDto == null) {
            logger.error("修改节点关系规则失败，参数 nodeRelationSaveDto 为空");
            throw new ContrastBusinessException("参数不能为空");
        }
        if (nodeRelationSaveDto.getId() == null) {
            logger.error("修改节点关系规则失败，ID 为空");
            throw new ContrastBusinessException("ID不能为空");
        }
        if (nodeRelationSaveDto.getEnvcSystemComputerNodeId() == null) {
            logger.error("修改节点关系规则失败，节点关系ID为空");
            throw new ContrastBusinessException("节点关系ID不能为空");
        }
        if (nodeRelationSaveDto.getModel() == null) {
            logger.error("修改节点关系规则失败，模式为空");
            throw new ContrastBusinessException("模式不能为空");
        }
        try {
            // 1. 查询现有记录
            NodeRelationEntity existingNodeRelation = nodeRelationMapper.selectNodeRelationById(nodeRelationSaveDto.getId());
            if (existingNodeRelation == null) {
                logger.error("修改节点关系规则失败，未找到ID为{}的记录", nodeRelationSaveDto.getId());
                throw new ContrastBusinessException("未找到要修改的记录");
            }

            // 2. 在现有记录基础上更新需要变更的字段
            existingNodeRelation.setEnvcSystemComputerNodeId(nodeRelationSaveDto.getEnvcSystemComputerNodeId());
            existingNodeRelation.setModel(nodeRelationSaveDto.getModel());
            existingNodeRelation.setType(nodeRelationSaveDto.getType());
            existingNodeRelation.setPath(nodeRelationSaveDto.getPath());
            existingNodeRelation.setSourcePath(nodeRelationSaveDto.getSourcePath());
            existingNodeRelation.setEncode(nodeRelationSaveDto.getEncode());
            existingNodeRelation.setWay(nodeRelationSaveDto.getWay());
            existingNodeRelation.setRuleType(nodeRelationSaveDto.getRuleType());
            existingNodeRelation.setEnabled(nodeRelationSaveDto.getEnabled());
            existingNodeRelation.setChildLevel(nodeRelationSaveDto.getChildLevel());
            // 设置更新人信息
            existingNodeRelation.setUpdatorId(userDto.getId());
            existingNodeRelation.setUpdatorName(userDto.getFullName());
            //增加后端处理逻辑，当选择方式是全部时，规则类型默认为匹配。
            if(existingNodeRelation.getWay() == 0){
                //匹配
                existingNodeRelation.setRuleType(0);
                //非子集
                existingNodeRelation.setChildLevel(1);
            }
            int result = nodeRelationMapper.updateNodeRelation(existingNodeRelation);

            // 3. 处理规则内容表
            String content = nodeRelationSaveDto.getContent();
            Long relationId = nodeRelationSaveDto.getId();
            NodeRuleContentEntity oldContent = nodeRuleContentMapper.selectNodeRuleContentList(
                new NodeRuleContentEntity() {{ setEnvcNodeRelationId(relationId); }}
            ).stream().findFirst().orElse(null);

            if (content == null || content.trim().isEmpty()) {
                // content 为空，且原有有内容，需删除
                if (oldContent != null) {
                    nodeRuleContentMapper.deleteNodeRuleContentById(oldContent.getId());
                    logger.info("已删除节点规则内容，relationId={}", relationId);
                }
            } else {
                if (oldContent == null) {
                    // 原无内容，需新增
                    NodeRuleContentEntity newContent = new NodeRuleContentEntity();
                    newContent.setEnvcNodeRelationId(relationId);
                    newContent.setRuleContent(content);
                    nodeRuleContentMapper.insertNodeRuleContent(newContent);
                    logger.info("已新增节点规则内容，relationId={}", relationId);
                } else {
                    // 原有内容，需更新
                    oldContent.setRuleContent(content);
                    nodeRuleContentMapper.updateNodeRuleContent(oldContent);
                    logger.info("已更新节点规则内容，relationId={}", relationId);
                }
            }
            logger.info("修改节点关系规则成功，ID={}，操作人={}", existingNodeRelation.getId(), userDto.getFullName());
            return result;
        } catch (ContrastBusinessException e) {
            logger.error("修改节点关系规则业务异常，ID={}，异常信息：{}", nodeRelationSaveDto.getId(), e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("修改节点关系规则系统异常，ID={}，异常信息：{}", nodeRelationSaveDto.getId(), e.getMessage(), e);
            throw new ContrastBusinessException("修改节点关系规则失败：" + e.getMessage(), e);
        }
    }

    /**
     * 批量删除节点关系规则
     *
     * @param ids 需要删除的节点关系规则主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteNodeRelationByIds(Long[] ids) {
        nodeRuleContentMapper.deleteNodeRuleContentByNodeRelationIds(ids);
        return nodeRelationMapper.deleteNodeRelationByIds(ids);
    }

    /**
     * 批量启用或禁用节点关系规则
     *
     * @param ids 节点关系规则ID集合
     * @param enabled 启用状态（0：启用，1：禁用）
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    @Transactional(rollbackFor = {ContrastBusinessException.class, RuntimeException.class})
    public int updateNodeRelationEnabledByIds(Long[] ids, Integer enabled, UserDto userDto) throws ContrastBusinessException {
        if (ids == null || ids.length == 0) {
            logger.error("批量启用或禁用节点关系规则失败：参数ids为空");
            throw new ContrastBusinessException("参数不能为空");
        }
        if (enabled == null || (enabled != 0 && enabled != 1)) {
            logger.error("批量启用或禁用节点关系规则失败：参数enabled无效");
            throw new ContrastBusinessException("启用状态参数无效");
        }
        if (userDto == null) {
            logger.error("批量启用或禁用节点关系规则失败：用户信息为空");
            throw new ContrastBusinessException("用户信息不能为空");
        }

        // 批量查询规则
        List<NodeRelationEntity> nodeRelationList = nodeRelationMapper.selectNodeRelationByIds(ids);
        
        // 验证规则状态
        for (NodeRelationEntity nodeRelation : nodeRelationList) {
            if (nodeRelation.getEnabled() != null && nodeRelation.getEnabled().equals(enabled)) {
                logger.error("批量启用或禁用节点关系规则失败：ID为{}的规则已经是{}状态", 
                    nodeRelation.getId(), enabled == 0 ? "启用" : "禁用");
                throw new ContrastBusinessException("ID为" + nodeRelation.getId() + "的规则已经是" + 
                    (enabled == 0 ? "启用" : "禁用") + "状态");
            }
        }

        // 检查是否有规则不存在
        if (nodeRelationList.size() != ids.length) {
            logger.error("批量启用或禁用节点关系规则失败：部分规则不存在");
            throw new ContrastBusinessException("部分规则不存在");
        }

        // 更新规则状态
        for (NodeRelationEntity nodeRelation : nodeRelationList) {
            nodeRelation.setEnabled(enabled);
            nodeRelation.setUpdatorId(userDto.getId());
            nodeRelation.setUpdatorName(userDto.getFullName());
        }

        // 使用 BatchHandler 进行批量更新
        batchHandler.batchData(nodeRelationList, nodeRelationMapper::updateNodeRelation, 1000);
        
        logger.info("批量{}节点关系规则成功，操作人：{}，影响记录数：{}", 
            enabled == 0 ? "启用" : "禁用", userDto.getFullName(), nodeRelationList.size());
        return nodeRelationList.size();
    }
}

