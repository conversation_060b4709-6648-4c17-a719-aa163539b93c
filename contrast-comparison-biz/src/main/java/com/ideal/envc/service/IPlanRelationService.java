package com.ideal.envc.service;

import com.ideal.envc.model.dto.PlanRelationDto;
import com.ideal.envc.model.dto.PlanRelationQueryDto;
import com.ideal.envc.model.dto.PlanSystemListDto;
import com.ideal.envc.model.dto.PlanSystemQueryDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.PlanRelationBatchDto;

/**
 * 方案信息Service接口
 *
 * <AUTHOR>
 */
public interface IPlanRelationService {
    /**
     * 查询方案信息
     *
     * @param id 方案信息主键
     * @return 方案信息
     */
    PlanRelationDto selectPlanRelationById(Long id) throws ContrastBusinessException;

    /**
     * 查询方案信息列表
     *
     * @param planRelationQueryDto 方案信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 方案信息集合
     */
    PageInfo<PlanRelationDto> selectPlanRelationList(PlanRelationQueryDto planRelationQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;


    /**
     * 新增方案信息
     *
     * @param planRelationDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int insertPlanRelation(PlanRelationDto planRelationDto, UserDto userDto) throws ContrastBusinessException;


    /**
     * 修改方案信息
     *
     * @param planRelationDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int updatePlanRelation(PlanRelationDto planRelationDto, UserDto userDto) throws ContrastBusinessException;


    /**
     * 批量删除方案信息
     *
     * @param ids 需要删除的方案信息主键集合
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int deletePlanRelationByIds(Long[] ids, UserDto userDto) throws ContrastBusinessException;

    /**
     * 查询方案绑定系统列表
     *
     * @param planSystemQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 方案绑定系统列表
     * @throws ContrastBusinessException 业务异常
     */
    PageInfo<PlanSystemListDto> selectPlanSystemList(PlanSystemQueryDto planSystemQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;

    /**
     * 批量保存方案与系统关系
     *
     * @param planRelationBatchDto 批量保存参数
     * @param userDto 当前用户信息
     * @return 保存结果
     * @throws ContrastBusinessException 业务异常
     */
    int batchInsertPlanRelation(PlanRelationBatchDto planRelationBatchDto, UserDto userDto) throws ContrastBusinessException;

    /**
     * 查询方案可绑定系统列表
     *
     * @param planSystemQueryDto 查询条件
     * @param pageNum 页码
     * @param pageSize 每页记录数
     * @return 方案可绑定系统列表
     */
    PageInfo<PlanSystemListDto> selectAvailablePlanSystemList(PlanSystemQueryDto planSystemQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException;
}
