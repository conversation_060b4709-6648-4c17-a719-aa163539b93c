package com.ideal.envc.service;

import com.ideal.envc.model.dto.PlanDto;
import com.ideal.envc.model.dto.PlanQueryDto;
import com.ideal.envc.model.dto.UserDto;
import com.github.pagehelper.PageInfo;
import com.ideal.envc.exception.ContrastBusinessException;

/**
 * 方案信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IPlanService {
    /**
     * 查询方案信息
     *
     * @param id 方案信息主键
     * @return 方案信息
     */
    PlanDto selectPlanById(Long id);

    /**
     * 查询方案信息列表
     *
     * @param planQueryDto 方案信息
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 方案信息集合
     */
    PageInfo<PlanDto> selectPlanList(PlanQueryDto planQueryDto, Integer pageNum, Integer pageSize);


    /**
     * 新增方案信息
     *
     * @param planDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int insertPlan(PlanDto planDto, UserDto userDto) throws ContrastBusinessException;


    /**
     * 修改方案信息
     *
     * @param planDto 方案信息
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int updatePlan(PlanDto planDto, UserDto userDto) throws ContrastBusinessException;


    /**
     * 批量删除方案信息
     *
     * @param ids 需要删除的方案信息主键集合
     * @param userDto 当前操作用户信息
     * @return 结果
     * @throws ContrastBusinessException 业务异常
     */
    int deletePlanByIds(Long[] ids, UserDto userDto) throws ContrastBusinessException;
}
