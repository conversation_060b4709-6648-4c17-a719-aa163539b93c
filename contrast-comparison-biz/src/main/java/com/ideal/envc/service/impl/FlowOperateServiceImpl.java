package com.ideal.envc.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.common.util.BeanUtils;
import com.ideal.envc.common.ContrastToolUtils;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.interaction.engine.EngineInteract;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.mapper.RunRuleContentMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.HierarchicalRunFlowBean;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.HierarchicalRunInstanceInfoBean;
import com.ideal.envc.model.bean.HierarchicalRunRuleBean;
import com.ideal.envc.model.bean.HierarchicalRunRuleContentBean;
import com.ideal.envc.model.bean.RetryRuleBean;
import com.ideal.envc.model.dto.FlowOperateResultDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleContentEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.StartFromEnums;
import com.ideal.envc.model.enums.StateEnum;
import com.ideal.envc.model.enums.TaskFlowOperationTypeEnum;
import com.ideal.envc.service.IFlowOperateService;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.ideal.envc.service.IStartContrastCommonBaseService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class FlowOperateServiceImpl implements IFlowOperateService {
    private final Logger logger = LoggerFactory.getLogger(FlowOperateServiceImpl.class);
    private final EngineInteract engineInteract;
    private final RunFlowMapper runFlowMapper;
    private final RunRuleMapper runRuleMapper;
    private final RunInstanceMapper runInstanceMapper;
    private final RunInstanceInfoMapper runInstanceInfoMapper;
    private final RunRuleContentMapper runRuleContentMapper;
    private final IStartContrastCommonBaseService startContrastCommonBaseService;
    public FlowOperateServiceImpl(EngineInteract engineInteract, RunFlowMapper runFlowMapper, RunRuleMapper runRuleMapper, RunInstanceMapper runInstanceMapper, RunInstanceInfoMapper runInstanceInfoMapper, RunRuleContentMapper runRuleContentMapper, IStartContrastCommonBaseService startContrastCommonBaseService) {
        this.engineInteract = engineInteract;
        this.runFlowMapper = runFlowMapper;
        this.runRuleMapper = runRuleMapper;
        this.runInstanceMapper = runInstanceMapper;
        this.runInstanceInfoMapper = runInstanceInfoMapper;
        this.runRuleContentMapper = runRuleContentMapper;
        this.startContrastCommonBaseService = startContrastCommonBaseService;
    }

    @Override
    @Transactional(rollbackFor = {Exception.class,EngineServiceException.class})
    public FlowOperateResultDto operateTerminatedFlow(Long[] flowIds, UserDto userDto) throws Exception {
        String operateTypeName = "终止操作";
        logger.info("operateFlow flowIds:{} operation:{} userDto:{}", flowIds, operateTypeName, JSON.toJSONString(userDto));
        if(flowIds==null || flowIds.length == 0){
            logger.error("operateFlow flowIds is empty");
            throw new Exception( "flowIds is empty");
        }
        String flowStr =  engineInteract.engineKillPauseResumeFlow(flowIds,TaskFlowOperationTypeEnum.TASK_FLOW_DEFAULT.getCode());
        Long[] operateFailFlowIdArr = ContrastToolUtils.convertStringToLongArray(flowStr);
        Long[] successFlowIdArr = ContrastToolUtils.subtract(flowIds,operateFailFlowIdArr);
        logger.info("operateFlow fail  flowStr:{}", flowStr);
        if(successFlowIdArr.length>0){
            logger.info("operateFlow operate：{} success", operateTypeName);
            // 使用批量操作优化性能
            batchUpdateFlowAndRuleState(Arrays.asList(successFlowIdArr), StateEnum.TERMINATED.getCode());
        }

        FlowOperateResultDto flowOperateResultDto = new FlowOperateResultDto();
        flowOperateResultDto.setFlowIds(flowIds);
        flowOperateResultDto.setFailFlowIds(flowStr);
        return flowOperateResultDto;
    }

    /**
     * 批量更新流程和规则状态
     *
     * @param flowIds 流程ID列表
     * @param state 状态
     */
    private void batchUpdateFlowAndRuleState(List<Long> flowIds, Integer state) {
        if (flowIds == null || flowIds.isEmpty()) {
            logger.warn("批量更新流程和规则状态：流程ID列表为空");
            return;
        }

        try {
            // 1. 批量查询流程实体，获取runBizId列表
            List<RunFlowEntity> runFlowEntities = runFlowMapper.selectRunFlowByFlowIds(flowIds);
            if (runFlowEntities.isEmpty()) {
                logger.warn("批量更新流程和规则状态：未找到对应的流程实体，flowIds: {}", flowIds);
                return;
            }

            // 2. 提取runBizId列表
            List<Long> runBizIds = runFlowEntities.stream()
                    .map(RunFlowEntity::getRunBizId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (runBizIds.isEmpty()) {
                logger.warn("批量更新流程和规则状态：未找到有效的runBizId，flowIds: {}", flowIds);
                return;
            }

            // 3. 批量更新流程状态
            long updateTime = System.currentTimeMillis();
            int flowUpdateCount = runFlowMapper.batchUpdateRunFlowState(flowIds, state, updateTime);
            logger.info("批量更新流程状态完成，更新数量: {}, flowIds: {}", flowUpdateCount, flowIds);

            // 4. 批量更新规则状态
            int ruleUpdateCount = runRuleMapper.batchUpdateRunRuleStateAndResult(runBizIds, state, 1);
            logger.info("批量更新规则状态完成，更新数量: {}, runBizIds: {}", ruleUpdateCount, runBizIds);

        } catch (Exception e) {
            logger.error("批量更新流程和规则状态失败，flowIds: {}, state: {}", flowIds, state, e);
            // 如果批量操作失败，回退到逐个操作
            fallbackToIndividualUpdate(flowIds, state);
        }
    }

    /**
     * 回退到逐个更新操作
     *
     * @param flowIds 流程ID列表
     * @param state 状态
     */
    private void fallbackToIndividualUpdate(List<Long> flowIds, Integer state) {
        logger.info("开始回退到逐个更新操作，flowIds: {}", flowIds);

        for (Long flowId : flowIds) {
            try {
                RunFlowEntity runFlowEntity = runFlowMapper.selectRunFlowByFlowId(flowId);
                if (runFlowEntity != null) {
                    runFlowEntity.setState(state);
                    runFlowEntity.setUpdateOrderTime(System.currentTimeMillis());
                    runFlowMapper.updateRunFlow(runFlowEntity);

                    RunRuleEntity runRuleEntity = runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId());
                    if (runRuleEntity != null) {
                        runRuleEntity.setState(state);
                        runRuleEntity.setResult(1);
                        runRuleMapper.updateRunRule(runRuleEntity);
                    }

                    logger.debug("逐个更新成功，flowId: {}", flowId);
                } else {
                    logger.warn("逐个更新失败：未找到流程实体，flowId: {}", flowId);
                }
            } catch (Exception e) {
                logger.error("逐个更新失败，flowId: {}", flowId, e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = {Exception.class, EngineServiceException.class})
    public FlowOperateResultDto operateRetryFlow(Long flowId, UserDto userDto) throws Exception {
        String operateTypeName = "重试操作";
        logger.info("operateFlow flowId:{} operation:{} userDto:{}", flowId, operateTypeName, JSON.toJSONString(userDto));
        
        // 1. 参数验证
        validateRetryFlowParams(flowId);
        
        // 2. 获取重试规则信息并验证
        RetryRuleBean retryRuleBean = getAndValidateRetryRuleBean(flowId);
        
        // 3. 创建新的运行实例
        RunInstanceEntity newRunInstanceEntity = createNewRunInstance(retryRuleBean, userDto);
        
        // 4. 创建新的运行实例详情
        RunInstanceInfoEntity newRunInstanceInfoEntity = createNewRunInstanceInfo(retryRuleBean, newRunInstanceEntity);
        
        // 5. 创建新的运行规则
        RunRuleEntity newRunRuleEntity = createNewRunRule(retryRuleBean, newRunInstanceInfoEntity, userDto);
        
        // 6. 创建新的运行规则内容
        RunRuleContentEntity newRunRuleContentEntity = null;
        if(StringUtils.isNotEmpty(retryRuleBean.getRuleContent())){
             newRunRuleContentEntity = createNewRunRuleContent(newRunRuleEntity, retryRuleBean);

        }
        // 7. 创建新的运行流程
        RunFlowEntity newRunFlowEntity = createNewRunFlow(flowId, newRunRuleEntity, userDto);
        
        // 8. 构建层次化数据并发送MQ
        boolean sendFlag = buildHierarchicalDataAndSendMQ(newRunInstanceEntity, newRunInstanceInfoEntity, 
                newRunRuleEntity, newRunRuleContentEntity, newRunFlowEntity, userDto);
        
        // 9. 构建返回结果
        return buildRetryFlowResult(flowId, sendFlag);
    }

    /**
     * 验证重试流程参数
     * @param flowId 流程ID
     * @throws Exception 参数验证失败时抛出异常
     */
    private void validateRetryFlowParams(Long flowId) throws Exception {
        if(flowId == null){
            logger.error("operateFlow flowId is empty");
            throw new Exception("flowId is empty");
        }
    }

    /**
     * 获取并验证重试规则Bean
     * @param flowId 流程ID
     * @return 重试规则Bean
     * @throws Exception 验证失败时抛出异常
     */
    private RetryRuleBean getAndValidateRetryRuleBean(Long flowId) throws Exception {
        RetryRuleBean retryRuleBean = runInstanceMapper.selectRetryRuleByFlowId(flowId);
        if(retryRuleBean == null){
            logger.error("operateFlow retryRuleBean is empty,operate flowId:{}",flowId);
            throw new Exception("retryRuleBean is empty");
        }
        if (retryRuleBean.getRunRuleId() == null || retryRuleBean.getRunRuleId() <= 0) {
            logger.error("operateFlow retryRuleBean runRuleId is empty,operate flowId:{}",flowId);
            throw new Exception("retryRuleBean runRuleId is empty");
        }
        if (retryRuleBean.getRunInstanceId() == null || retryRuleBean.getRunInstanceId() <= 0) {
            logger.error("operateFlow retryRuleBean runInstanceId is empty,operate flowId:{}",flowId);
            throw new Exception("retryRuleBean runInstanceId is empty");
        }
        if (retryRuleBean.getRunInstanceInfoId() == null || retryRuleBean.getRunInstanceInfoId() <= 0) {
            logger.error("operateFlow retryRuleBean runInstanceInfoId is empty,operate flowId:{}",flowId);
            throw new Exception("retryRuleBean runInstanceInfoId is empty");
        }
        return retryRuleBean;
    }

    /**
     * 创建新的运行实例
     * @param retryRuleBean 重试规则Bean
     * @param userDto 用户信息
     * @return 新的运行实例
     * @throws Exception 创建失败时抛出异常
     */
    private RunInstanceEntity createNewRunInstance(RetryRuleBean retryRuleBean, UserDto userDto) throws Exception {
        RunInstanceEntity runInstanceEntity = runInstanceMapper.selectRunInstanceById(retryRuleBean.getRunInstanceId());
        if(runInstanceEntity == null){
            logger.error("operateFlow runInstanceEntity is empty,operate runInstanceId:{}",retryRuleBean.getRunInstanceId());
            throw new Exception("runInstanceEntity is empty");
        }
        
        runInstanceEntity.setId(null);
        runInstanceEntity.setResult(-1);
        runInstanceEntity.setState(0);
        runInstanceEntity.setFrom(StartFromEnums.RETRY.getCode());
        runInstanceEntity.setStarterId(userDto.getId());
        runInstanceEntity.setStarterName(userDto.getFullName());
        runInstanceEntity.setStartTime(new java.sql.Timestamp(System.currentTimeMillis()));
        runInstanceEntity.setElapsedTime(null);
        runInstanceMapper.insertRunInstance(runInstanceEntity);
        return runInstanceEntity;
    }

    /**
     * 创建新的运行实例详情
     * @param retryRuleBean 重试规则Bean
     * @param newRunInstanceEntity 新的运行实例
     * @return 新的运行实例详情
     * @throws Exception 创建失败时抛出异常
     */
    private RunInstanceInfoEntity createNewRunInstanceInfo(RetryRuleBean retryRuleBean, RunInstanceEntity newRunInstanceEntity) throws Exception {
        RunInstanceInfoEntity runInstanceInfoEntity = runInstanceInfoMapper.selectRunInstanceInfoById(retryRuleBean.getRunInstanceInfoId());
        if(runInstanceInfoEntity == null){
            logger.error("operateFlow runInstanceInfoEntity is empty,operate runInstanceInfoId:{}",retryRuleBean.getRunInstanceInfoId());
            throw new Exception("runInstanceInfoEntity is empty");
        }
        
        runInstanceInfoEntity.setId(null);
        runInstanceInfoEntity.setEnvcRunInstanceId(newRunInstanceEntity.getId());
        runInstanceInfoEntity.setResult(-1);
        runInstanceInfoEntity.setState(0);
        runInstanceInfoMapper.insertRunInstanceInfo(runInstanceInfoEntity);
        return runInstanceInfoEntity;
    }

    /**
     * 创建新的运行规则
     * @param retryRuleBean 重试规则Bean
     * @param newRunInstanceInfoEntity 新的运行实例详情
     * @param userDto 用户信息
     * @return 新的运行规则
     * @throws Exception 创建失败时抛出异常
     */
    private RunRuleEntity createNewRunRule(RetryRuleBean retryRuleBean, RunInstanceInfoEntity newRunInstanceInfoEntity, UserDto userDto) throws Exception {
        RunRuleEntity runRuleEntity = runRuleMapper.selectRunRuleById(retryRuleBean.getRunRuleId());
        if(runRuleEntity == null){
            logger.error("operateFlow runRuleEntity is empty,operate runRuleId:{}",retryRuleBean.getRunRuleId());
            throw new Exception("runRuleEntity is empty");
        }
        
        runRuleEntity.setId(null);
        runRuleEntity.setEnvcRunInstanceInfoId(newRunInstanceInfoEntity.getId());
        runRuleEntity.setResult(-1);
        runRuleEntity.setState(0);
        runRuleEntity.setElapsedTime(null);
        runRuleEntity.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        runRuleEntity.setCreatorId(userDto.getId());
        runRuleEntity.setCreatorName(userDto.getFullName());
        runRuleMapper.insertRunRule(runRuleEntity);
        return runRuleEntity;
    }

    /**
     * 创建新的运行规则内容
     * @param newRunRuleEntity 新的运行规则
     * @param retryRuleBean 重试规则Bean
     * @return 新的运行规则内容
     */
    private RunRuleContentEntity createNewRunRuleContent(RunRuleEntity newRunRuleEntity, RetryRuleBean retryRuleBean) {
        RunRuleContentEntity runRuleContentEntity = new RunRuleContentEntity();
        runRuleContentEntity.setEnvcRunRuleId(newRunRuleEntity.getId());
        runRuleContentEntity.setContent(retryRuleBean.getRuleContent());
        runRuleContentMapper.insertRunRuleContent(runRuleContentEntity);
        return runRuleContentEntity;
    }

    /**
     * 创建新的运行流程
     * @param flowId 原流程ID
     * @param runRuleEntity 新的运行规则内容
     * @param userDto 用户信息
     * @return 新的运行流程
     */
    private RunFlowEntity createNewRunFlow(Long flowId, RunRuleEntity runRuleEntity, UserDto userDto) {
        RunFlowEntity runFlowEntity = runFlowMapper.selectRunFlowByFlowId(flowId);
        if(runFlowEntity != null){
            runFlowEntity.setId(null);
            runFlowEntity.setState(StateEnum.RUNNING.getCode());
            runFlowEntity.setUpdateOrderTime(0L);
            runFlowEntity.setCreatorId(userDto.getId());
            runFlowEntity.setCreatorName(userDto.getFullName());
            runFlowEntity.setElapsedTime(null);
            runFlowEntity.setRet(null);
            runFlowEntity.setFlowid(null);
            runFlowEntity.setRunBizId(runRuleEntity.getId());
            runFlowMapper.insertRunFlow(runFlowEntity);
        }
        return runFlowEntity;
    }

    /**
     * 构建层次化数据并发送MQ
     * @param newRunInstanceEntity 新的运行实例
     * @param newRunInstanceInfoEntity 新的运行实例详情
     * @param newRunRuleEntity 新的运行规则
     * @param newRunRuleContentEntity 新的运行规则内容
     * @param newRunFlowEntity 新的运行流程
     * @param userDto 用户信息
     * @return 发送是否成功
     * @throws EngineServiceException 引擎服务异常时抛出
     */
    private boolean buildHierarchicalDataAndSendMQ(RunInstanceEntity newRunInstanceEntity, 
                                                   RunInstanceInfoEntity newRunInstanceInfoEntity,
                                                   RunRuleEntity newRunRuleEntity, 
                                                   RunRuleContentEntity newRunRuleContentEntity,
                                                   RunFlowEntity newRunFlowEntity, 
                                                   UserDto userDto) throws EngineServiceException {
        List<HierarchicalRunInstanceBean> hierarchicalInstanceList = new ArrayList<>();
        HierarchicalRunRuleContentBean hierarchicalRunRuleContentBean = BeanUtils.copy(newRunRuleContentEntity, HierarchicalRunRuleContentBean.class);
        HierarchicalRunFlowBean hierarchicalRunFlowBean = BeanUtils.copy(newRunFlowEntity, HierarchicalRunFlowBean.class);
        HierarchicalRunRuleBean hierarchicalRunRuleBean = BeanUtils.copy(newRunRuleEntity, HierarchicalRunRuleBean.class);
        hierarchicalRunRuleBean.setRuleContent(hierarchicalRunRuleContentBean);
        hierarchicalRunRuleBean.setRuleFlow(hierarchicalRunFlowBean);

        HierarchicalRunInstanceInfoBean hierarchicalRunInstanceInfoBean = BeanUtils.copy(newRunInstanceInfoEntity, HierarchicalRunInstanceInfoBean.class);
        List<HierarchicalRunRuleBean> hierarchicalRunRuleBeanList = new ArrayList<>();
        hierarchicalRunRuleBeanList.add(hierarchicalRunRuleBean);
        hierarchicalRunInstanceInfoBean.setRuleList(hierarchicalRunRuleBeanList);

        List<HierarchicalRunInstanceInfoBean> hierarchicalRunInstanceInfoBeanList = new ArrayList<>();
        hierarchicalRunInstanceInfoBeanList.add(hierarchicalRunInstanceInfoBean);
        HierarchicalRunInstanceBean hierarchicalRunInstanceBean = BeanUtils.copy(newRunInstanceEntity, HierarchicalRunInstanceBean.class);
        hierarchicalRunInstanceBean.setInstanceInfoList(hierarchicalRunInstanceInfoBeanList);
        hierarchicalInstanceList.add(hierarchicalRunInstanceBean);
        
        // 封装推送消息Mq数据
        List<StartTaskFlowDto> startTaskFlowDtoList = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

        // 推送消息到MQ
        String startType = "规则级重试";
        boolean sendFlag = startContrastCommonBaseService.startContrastSendAndUpdateState(startTaskFlowDtoList, startType);
        logger.info("{} 启动发送Mq，操作是否成功：{}", startType, sendFlag);
        return sendFlag;
    }

    /**
     * 构建重试流程结果
     * @param flowId 流程ID
     * @param sendFlag 发送是否成功
     * @return 流程操作结果
     */
    private FlowOperateResultDto buildRetryFlowResult(Long flowId, boolean sendFlag) {
        FlowOperateResultDto flowOperateResultDto = new FlowOperateResultDto();
        Long[] flowIds = new Long[]{flowId};
        flowOperateResultDto.setFlowIds(flowIds);
        if(sendFlag){
            flowOperateResultDto.setFailFlowIds("");
        }else{
            flowOperateResultDto.setFailFlowIds(flowId.toString());
        }
        return flowOperateResultDto;
    }

}
