package com.ideal.envc.service.impl;

import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.exception.EngineServiceException;
import com.ideal.envc.interaction.model.CenterQueryDto;
import com.ideal.envc.interaction.model.SystemComputerListQueryInnerIo;
import com.ideal.envc.interaction.model.SystemComputerListQueryIo;
import com.ideal.envc.interaction.sysm.SystemInteract;
import com.ideal.envc.model.bean.HierarchicalRunInstanceBean;
import com.ideal.envc.model.bean.HierarchicalRunInstanceInfoBean;
import com.ideal.envc.model.bean.HierarchicalRunRuleBean;
import com.ideal.envc.model.bean.StartComputerNodeBean;
import com.ideal.envc.model.bean.StartPlanBean;
import com.ideal.envc.model.bean.StartSystemBean;
import com.ideal.envc.model.dto.ComputerInfoDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.dto.start.StartTaskFlowDto;
import com.ideal.envc.producer.SendDataToEngineMqProducer;
import com.ideal.envc.service.IStartContrastBaseService;
import com.ideal.envc.service.IStartContrastCommonBaseService;
import com.ideal.envc.service.IStartContrastCommonService;
import com.ideal.envc.component.TaskCounterComponent;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.RunInstanceMapper;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.model.entity.RunInstanceEntity;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.entity.RunFlowEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 一致性比对启动公共服务实现类
 *
 * <AUTHOR>
 */
@Service
public class StartContrastCommonServiceImpl implements IStartContrastCommonService {
    private static final Logger logger = LoggerFactory.getLogger(StartContrastCommonServiceImpl.class);

    private final IStartContrastBaseService startContrastBaseService;
    private final SystemInteract systemInteract;
    private final IStartContrastCommonBaseService startContrastCommonBaseService;
    private final TaskCounterComponent taskCounterComponent;
    private final RunInstanceMapper runInstanceMapper;
    private final RunInstanceInfoMapper runInstanceInfoMapper;
    private final RunRuleMapper runRuleMapper;
    private final RunFlowMapper runFlowMapper;

    public StartContrastCommonServiceImpl(IStartContrastBaseService startContrastBaseService,  SystemInteract systemInteract, IStartContrastCommonBaseService startContrastCommonBaseService,  TaskCounterComponent taskCounterComponent, RunInstanceMapper runInstanceMapper, RunInstanceInfoMapper runInstanceInfoMapper, RunRuleMapper runRuleMapper, RunFlowMapper runFlowMapper) {
        this.startContrastBaseService = startContrastBaseService;
        this.systemInteract = systemInteract;
        this.startContrastCommonBaseService = startContrastCommonBaseService;
        this.taskCounterComponent = taskCounterComponent;
        this.runInstanceMapper = runInstanceMapper;
        this.runInstanceInfoMapper = runInstanceInfoMapper;
        this.runRuleMapper = runRuleMapper;
        this.runFlowMapper = runFlowMapper;
    }

    /**
     * 通用启动处理方法
     *
     * @param startPlanList 方案信息列表
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源
     * @param startType 启动类型名称（用于日志）
     * @param userDto 用户信息
     * @return 启动结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public StartResult processStart(List<StartPlanBean> startPlanList, Long userId, String userName, Integer from, String startType, UserDto userDto) throws EngineServiceException {
        StartResult result = new StartResult();
        
        if (CollectionUtils.isEmpty(startPlanList)) {
            result.setSuccess(false);
            result.setMessage("未找到启动信息");
            logger.error("{}启动失败，未找到启动信息", startType);
            return result;
        }
        
        try {
            // 1. 基础数据获取
            StartBaseDataCollection dataCollection = collectBaseData(startPlanList);
            
            // 2. 验证基础数据
            StartResult validationResult = validateBaseData(dataCollection, startType);
            if (!validationResult.isSuccess()) {
                return validationResult;
            }
            
            // 3. 获取外部数据
            ExternalDataCollection externalData = fetchExternalData(dataCollection);
            
            // 4. 验证外部数据
            StartResult externalValidationResult = validateExternalData(externalData, startType);
            if (!externalValidationResult.isSuccess()) {
                return externalValidationResult;
            }

            // 5. 验证计算机信息完整性
            StartResult computerValidationResult = validateComputerInfoCompleteness(startPlanList, externalData, startType);
            if (!computerValidationResult.isSuccess()) {
                return computerValidationResult;
            }

            // 6. 执行启动流程
            executeStartProcess(startPlanList, userId, userName, from, startType, userDto, 
                               externalData.getCenterMap(), externalData.getComputerInfoDtoMap());
            
            return result;
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(startType + "启动异常：" + e.getMessage());
            logger.error("{}启动异常", startType, e);
            // 重新抛出异常，确保事务回滚
            throw e;
        }
    }

    /**
     * 收集基础数据（中心ID、设备ID等）
     *
     * @param startPlanList 方案信息列表
     * @return 基础数据集合
     */
    private StartBaseDataCollection collectBaseData(List<StartPlanBean> startPlanList) {
        List<Long> centerIdList = new ArrayList<>();
        List<Long> computerIdList = new ArrayList<>();
        List<Long> souceComputerIdList = new ArrayList<>();

        for (StartPlanBean startPlanBean : startPlanList) {
            if (startPlanBean.getSystems() != null && !startPlanBean.getSystems().isEmpty()) {
                for (StartSystemBean startSystemBean : startPlanBean.getSystems()) {
                    if (startSystemBean.getComputerNodeBeans() != null && !startSystemBean.getComputerNodeBeans().isEmpty()) {
                        processComputerNodes(startSystemBean.getComputerNodeBeans(), centerIdList, computerIdList, souceComputerIdList);
                    }
                }
            }
        }

        return new StartBaseDataCollection(centerIdList, computerIdList, souceComputerIdList);
    }

    /**
     * 处理设备节点，提取相关ID
     *
     * @param computerNodeBeans 设备节点列表
     * @param centerIdList 中心ID列表
     * @param computerIdList 设备ID列表
     * @param souceComputerIdList 源设备ID列表
     */
    private void processComputerNodes(List<StartComputerNodeBean> computerNodeBeans,
                                    List<Long> centerIdList,
                                    List<Long> computerIdList,
                                    List<Long> souceComputerIdList) {
        for (StartComputerNodeBean startComputerNodeBean : computerNodeBeans) {
            // 中心封装
            addIfNotExistsAndPositive(centerIdList, startComputerNodeBean.getSourceCenterId());
            addIfNotExistsAndPositive(centerIdList, startComputerNodeBean.getTargetCenterId());
            
            // 全量设备封装
            addIfNotExistsAndPositive(computerIdList, startComputerNodeBean.getSourceComputerId());
            addIfNotExistsAndPositive(computerIdList, startComputerNodeBean.getTargetComputerId());
            
            // 源设备封装
            if (!souceComputerIdList.contains(startComputerNodeBean.getSourceComputerId())) {
                souceComputerIdList.add(startComputerNodeBean.getSourceComputerId());
            }
        }
    }

    /**
     * 如果ID不存在且为正数则添加到列表
     *
     * @param idList ID列表
     * @param id 要添加的ID
     */
    private void addIfNotExistsAndPositive(List<Long> idList, Long id) {
        if (id != null && idList!=null && !idList.contains(id) && id > 0) {
            idList.add(id);
        }
    }

    /**
     * 验证基础数据
     *
     * @param dataCollection 基础数据集合
     * @param startType 启动类型
     * @return 验证结果
     */
    private StartResult validateBaseData(StartBaseDataCollection dataCollection, String startType) {
        StartResult result = new StartResult();
        
        if (dataCollection.getCenterIdList().isEmpty()) {
            result.setSuccess(false);
            result.setMessage("未找到中心信息");
            logger.error("{}启动失败，未找到中心信息", startType);
            return result;
        }
        
        if (dataCollection.getComputerIdList().isEmpty()) {
            result.setSuccess(false);
            result.setMessage("未找到设备信息");
            logger.error("{}启动失败，未找到设备信息", startType);
            return result;
        }
        
        result.setSuccess(true);
        return result;
    }

    /**
     * 获取外部数据（中心信息和设备信息）
     *
     * @param dataCollection 基础数据集合
     * @return 外部数据集合
     */
    private ExternalDataCollection fetchExternalData(StartBaseDataCollection dataCollection) {
        CenterQueryDto centerQueryDto = new CenterQueryDto();
        centerQueryDto.setAppointIds(dataCollection.getCenterIdList());
        Map<Long, String> centerMap = systemInteract.getCenterMap(centerQueryDto);

        SystemComputerListQueryIo systemComputerListQueryIo = new SystemComputerListQueryIo();
        SystemComputerListQueryInnerIo systemComputerListQueryInnerIo = new SystemComputerListQueryInnerIo();
        systemComputerListQueryInnerIo.setAppointComputerIds(dataCollection.getComputerIdList());
        systemComputerListQueryIo.setType(0);
        systemComputerListQueryIo.setQueryParam(systemComputerListQueryInnerIo);
        Map<Long, ComputerInfoDto> computerInfoDtoMap = systemInteract.getComputerInfoMap(systemComputerListQueryIo);

        return new ExternalDataCollection(centerMap, computerInfoDtoMap);
    }

    /**
     * 验证外部数据
     *
     * @param externalData 外部数据集合
     * @param startType 启动类型
     * @return 验证结果
     */
    private StartResult validateExternalData(ExternalDataCollection externalData, String startType) {
        StartResult result = new StartResult();

        if (externalData.getCenterMap().isEmpty()) {
            result.setSuccess(false);
            result.setMessage("平台管理服务未返回中心信息");
            logger.error("{}启动失败，平台管理服务未返回中心信息", startType);
            return result;
        }

        if (externalData.getComputerInfoDtoMap().isEmpty()) {
            result.setSuccess(false);
            result.setMessage("平台管理服务未返回设备信息");
            logger.error("{}启动失败，平台管理服务未返回设备信息", startType);
            return result;
        }

        result.setSuccess(true);
        return result;
    }

    /**
     * 验证计算机信息完整性
     * 在数据处理前验证所有需要的计算机信息是否存在，避免处理过程中发现数据缺失
     *
     * @param startPlanList 方案信息列表
     * @param externalData 外部数据集合
     * @param startType 启动类型
     * @return 验证结果
     */
    private StartResult validateComputerInfoCompleteness(List<StartPlanBean> startPlanList, ExternalDataCollection externalData, String startType) {
        StartResult result = new StartResult();
        Map<Long, ComputerInfoDto> computerInfoDtoMap = externalData.getComputerInfoDtoMap();

        // 收集所有需要的计算机ID
        Set<Long> requiredComputerIds = new HashSet<Long>();
        List<String> missingComputerIds = new ArrayList<String>();

        for (StartPlanBean startPlanBean : startPlanList) {
            if (startPlanBean.getSystems() != null && !startPlanBean.getSystems().isEmpty()) {
                for (StartSystemBean startSystemBean : startPlanBean.getSystems()) {
                    if (startSystemBean.getComputerNodeBeans() != null && !startSystemBean.getComputerNodeBeans().isEmpty()) {
                        for (StartComputerNodeBean nodeBean : startSystemBean.getComputerNodeBeans()) {
                            if (nodeBean != null) {
                                // 收集源计算机ID
                                if (nodeBean.getSourceComputerId() != null) {
                                    requiredComputerIds.add(nodeBean.getSourceComputerId());
                                }
                                // 收集目标计算机ID
                                if (nodeBean.getTargetComputerId() != null) {
                                    requiredComputerIds.add(nodeBean.getTargetComputerId());
                                }
                            }
                        }
                    }
                }
            }
        }

        // 验证所有需要的计算机信息是否存在
        for (Long computerId : requiredComputerIds) {
            if (!computerInfoDtoMap.containsKey(computerId)) {
                missingComputerIds.add(String.valueOf(computerId));
                logger.error("{}启动失败，未找到计算机信息，计算机ID：{}", startType, computerId);
            }
        }

        if (!missingComputerIds.isEmpty()) {
            result.setSuccess(false);
            result.setMessage("未找到计算机信息，计算机ID：" + String.join(", ", missingComputerIds));
            logger.error("{}启动失败，缺失计算机信息，计算机ID列表：{}", startType, missingComputerIds);
            return result;
        }

        logger.info("{}计算机信息完整性验证通过，共验证{}台计算机", startType, requiredComputerIds.size());
        result.setSuccess(true);
        return result;
    }

    /**
     * 执行启动流程
     *
     * @param startPlanList 方案信息列表
     * @param userId 用户ID
     * @param userName 用户名称
     * @param from 触发来源
     * @param startType 启动类型
     * @param userDto 用户信息
     * @param centerMap 中心映射
     * @param computerInfoDtoMap 设备信息映射
     * @throws EngineServiceException 引擎服务异常
     */
    private void executeStartProcess(List<StartPlanBean> startPlanList, Long userId, String userName,
                                   Integer from, String startType, UserDto userDto,
                                   Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap) throws EngineServiceException {
        // 1. 存储运行实例相关表数据
         List<HierarchicalRunInstanceBean> hierarchicalInstanceList = startContrastBaseService.saveRunInstanceData(
                 startPlanList, userId, userName, from, -1L, centerMap, computerInfoDtoMap);
        
        // 2. 封装推送消息Mq数据
        List<StartTaskFlowDto> startTaskFlowDtoList = startContrastCommonBaseService.buildTaskFlowDtoList(hierarchicalInstanceList, userDto);

        //2.5 通过hierarchicalInstanceList进行封装出instanceid及其下包含的instanceinfo个数，及instanceinfoid下包含的rule的个数。
        Map<Long,List<Map<Long,Integer>>> instanceIdAndInstanceInfoCountMap = new HashMap<>();
        for(HierarchicalRunInstanceBean hierarchicalRunInstanceBean : hierarchicalInstanceList){
            List<Map<Long,Integer>> instanceInfoCountList = new ArrayList<>();
            for(HierarchicalRunInstanceInfoBean hierarchicalRunInstanceInfoBean : hierarchicalRunInstanceBean.getInstanceInfoList()){
                Map<Long,Integer> instanceInfoCountMap = new HashMap<>();
                int  ruleCount = 0;
                List<HierarchicalRunRuleBean> ruleList = hierarchicalRunInstanceInfoBean.getRuleList();
                if (ruleList != null){
                    ruleCount = ruleList.size();
                }
                instanceInfoCountMap.put(hierarchicalRunInstanceInfoBean.getId(),ruleCount);
                instanceInfoCountList.add(instanceInfoCountMap);
            }

            instanceIdAndInstanceInfoCountMap.put(hierarchicalRunInstanceBean.getId(),instanceInfoCountList);
        }


        // 3. 推送消息到MQ
        for(StartTaskFlowDto startTaskFlowDto : startTaskFlowDtoList){
            List<StartTaskFlowDto> startTaskFlowDtoListOfOne = new ArrayList<>();
            startTaskFlowDtoListOfOne.add(startTaskFlowDto);
            
            // 初始化实例（instance）计数器和实例详情(instanceinfo)计数器初始化
            Long instanceId = startTaskFlowDto.getUniqueTaskId();
            List<Map<Long,Integer>> instanceInfoCountList = instanceIdAndInstanceInfoCountMap.get(instanceId);
            
            try {
                // 初始化实例计数器，初始值为实例详情的数量
                Integer instanceCounterInitValue = instanceInfoCountList != null ? instanceInfoCountList.size() : 0;
                taskCounterComponent.initInstanceCounter(instanceId, instanceCounterInitValue);
                logger.info("成功初始化实例计数器，实例ID：{}，初始值：{}", instanceId, instanceCounterInitValue);
                
                // 初始化每个实例详情计数器，初始值为对应的规则数量
                if (instanceInfoCountList != null) {
                    for (Map<Long, Integer> instanceInfoCountMap : instanceInfoCountList) {
                        for (Map.Entry<Long, Integer> entry : instanceInfoCountMap.entrySet()) {
                            Long instanceInfoId = entry.getKey();
                            Integer ruleCount = entry.getValue();
                            taskCounterComponent.initInstanceInfoCounter(instanceInfoId, ruleCount);
                            logger.info("成功初始化实例详情计数器，实例详情ID：{}，初始值：{}", instanceInfoId, ruleCount);
                        }
                    }
                }
                
                // 发送MQ消息
                boolean sendFlag = startContrastCommonBaseService.startContrastSendAndUpdateState(startTaskFlowDtoListOfOne, startType);
                logger.info("{} 启动发送Mq，操作是否成功：{}", startType, sendFlag);
                
                // 如果启动失败，则清理计数器，防止不一致，并且更新实例和实例详情以及实例详情下规则及对应流程状态为失败，比对结果为比对不一致
                if (!sendFlag) {
                    logger.error("{}启动失败，开始清理计数器并更新状态，实例ID：{}", startType, instanceId);
                    handleStartFailure(instanceId, instanceInfoCountList, startType);
                }
                
            } catch (ContrastBusinessException e) {
                logger.error("初始化计数器失败，实例ID：{}，异常信息：{}", instanceId, e.getMessage(), e);
                // 计数器初始化失败，也需要进行失败处理
                handleStartFailure(instanceId, instanceInfoCountList, startType);
                // 重新抛出异常，确保事务回滚
                throw new EngineServiceException("初始化计数器失败：" + e.getMessage(), e);
            }
        }
        logger.info("{} 启动发送Mq，操作是否成功", startType);
    }

    /**
     * 处理启动失败的情况
     * 清理计数器，并更新实例和实例详情以及实例详情下规则及对应流程状态为失败，比对结果为比对不一致
     *
     * @param instanceId 实例ID
     * @param instanceInfoCountList 实例详情计数信息列表
     * @param startType 启动类型
     */
    private void handleStartFailure(Long instanceId, List<Map<Long, Integer>> instanceInfoCountList, String startType) {
        try {
            // 1. 清理计数器
            cleanupCounters(instanceId, instanceInfoCountList);
            
            // 2. 更新实例状态为失败
            updateInstanceStatusToFailed(instanceId);
            
            // 3. 更新实例详情状态为失败
            if (instanceInfoCountList != null) {
                for (Map<Long, Integer> instanceInfoCountMap : instanceInfoCountList) {
                    for (Long instanceInfoId : instanceInfoCountMap.keySet()) {
                        updateInstanceInfoStatusToFailed(instanceInfoId);
                        // 4. 更新该实例详情下的所有规则状态为失败，比对结果为比对不一致
                        updateRulesStatusToFailed(instanceInfoId);
                    }
                }
            }
            
            logger.info("{}启动失败处理完成，实例ID：{}", startType, instanceId);
            
        } catch (Exception e) {
            logger.error("处理{}启动失败时发生异常，实例ID：{}，异常信息：{}", startType, instanceId, e.getMessage(), e);
        }
    }

    /**
     * 清理计数器
     *
     * @param instanceId 实例ID
     * @param instanceInfoCountList 实例详情计数信息列表
     */
    private void cleanupCounters(Long instanceId, List<Map<Long, Integer>> instanceInfoCountList) {
        try {
            // 清理实例计数器
            if (taskCounterComponent.isInstanceCounterExists(instanceId)) {
                taskCounterComponent.clearInstanceCounter(instanceId);
                logger.info("成功清理实例计数器，实例ID：{}", instanceId);
            }
            
            // 清理实例详情计数器
            if (instanceInfoCountList != null) {
                for (Map<Long, Integer> instanceInfoCountMap : instanceInfoCountList) {
                    for (Long instanceInfoId : instanceInfoCountMap.keySet()) {
                        if (taskCounterComponent.isInstanceInfoCounterExists(instanceInfoId)) {
                            taskCounterComponent.clearInstanceInfoCounter(instanceInfoId);
                            logger.info("成功清理实例详情计数器，实例详情ID：{}", instanceInfoId);
                        }
                    }
                }
            }
            
        } catch (ContrastBusinessException e) {
            logger.error("清理计数器时发生异常，实例ID：{}，异常信息：{}", instanceId, e.getMessage(), e);
        }
    }

    /**
     * 更新实例状态为失败
     *
     * @param instanceId 实例ID
     */
    private void updateInstanceStatusToFailed(Long instanceId) {
        try {
            // 查询实例信息
            RunInstanceEntity instance = runInstanceMapper.selectRunInstanceById(instanceId);
            if (instance != null) {
                // 更新实例状态为失败，结果为不一致
                instance.setState(2); // istate = 2 (终止)
                instance.setResult(1); // iresult = 1 (不一致/失败)
                int updateCount = runInstanceMapper.updateRunInstance(instance);
                if (updateCount > 0) {
                    logger.info("成功更新实例状态为失败，实例ID：{}", instanceId);
                } else {
                    logger.warn("更新实例状态失败，可能实例不存在，实例ID：{}", instanceId);
                }
            } else {
                logger.warn("实例不存在，无法更新状态，实例ID：{}", instanceId);
            }
        } catch (Exception e) {
            logger.error("更新实例状态失败，实例ID：{}，异常信息：{}", instanceId, e.getMessage(), e);
        }
    }

    /**
     * 更新实例详情状态为失败
     *
     * @param instanceInfoId 实例详情ID
     */
    private void updateInstanceInfoStatusToFailed(Long instanceInfoId) {
        try {
            // 查询实例详情信息
            RunInstanceInfoEntity instanceInfo = runInstanceInfoMapper.selectRunInstanceInfoById(instanceInfoId);
            if (instanceInfo != null) {
                // 更新实例详情状态为失败，结果为不一致
                instanceInfo.setState(2); // istate = 2 (终止)
                instanceInfo.setResult(1); // iresult = 1 (不一致/失败)
                int updateCount = runInstanceInfoMapper.updateRunInstanceInfo(instanceInfo);
                if (updateCount > 0) {
                    logger.info("成功更新实例详情状态为失败，实例详情ID：{}", instanceInfoId);
                } else {
                    logger.warn("更新实例详情状态失败，可能实例详情不存在，实例详情ID：{}", instanceInfoId);
                }
            } else {
                logger.warn("实例详情不存在，无法更新状态，实例详情ID：{}", instanceInfoId);
            }
        } catch (Exception e) {
            logger.error("更新实例详情状态失败，实例详情ID：{}，异常信息：{}", instanceInfoId, e.getMessage(), e);
        }
    }

    /**
     * 更新规则状态为失败，比对结果为比对不一致
     *
     * @param instanceInfoId 实例详情ID
     */
    private void updateRulesStatusToFailed(Long instanceInfoId) {
        try {
            // 查询该实例详情下的所有规则
            List<RunRuleEntity> rules = runRuleMapper.selectRulesByInstanceInfoId(instanceInfoId);
            if (rules != null && !rules.isEmpty()) {
                for (RunRuleEntity rule : rules) {
                    // 更新规则状态为失败，结果为不一致
                    rule.setState(2); // istate = 2 (终止)
                    rule.setResult(1); // iresult = 1 (不一致/失败)
                    int updateCount = runRuleMapper.updateRunRule(rule);
                    if (updateCount > 0) {
                        logger.info("成功更新规则状态为失败，规则ID：{}", rule.getId());
                    } else {
                        logger.warn("更新规则状态失败，规则ID：{}", rule.getId());
                    }
                    
                    // 更新该规则对应的流程状态为失败
                    updateFlowStatusToFailedByRuleId(rule.getId());
                }
                logger.info("成功更新实例详情下所有规则状态为失败，实例详情ID：{}，规则数量：{}", instanceInfoId, rules.size());
            } else {
                logger.warn("实例详情下没有找到规则，实例详情ID：{}", instanceInfoId);
            }
        } catch (Exception e) {
            logger.error("更新规则状态失败，实例详情ID：{}，异常信息：{}", instanceInfoId, e.getMessage(), e);
        }
    }

    /**
     * 根据规则ID更新对应流程状态为失败
     *
     * @param ruleId 规则ID
     */
    private void updateFlowStatusToFailedByRuleId(Long ruleId) {
        try {
            // 查询与该规则关联的所有流程（比对模式: imodel = 0, irun_biz_id = ruleId）
            RunFlowEntity flowQuery = new RunFlowEntity();
            flowQuery.setRunBizId(ruleId);
            flowQuery.setModel(0); // 0：比对模式
            List<RunFlowEntity> flows = runFlowMapper.selectRunFlowList(flowQuery);
            
            if (flows != null && !flows.isEmpty()) {
                for (RunFlowEntity flow : flows) {
                    // 更新流程状态为失败
                    flow.setState(2); // istate = 2 (终止)
                    int updateCount = runFlowMapper.updateRunFlow(flow);
                    if (updateCount > 0) {
                        logger.info("成功更新流程状态为失败，流程ID：{}，规则ID：{}", flow.getId(), ruleId);
                    } else {
                        logger.warn("更新流程状态失败，流程ID：{}，规则ID：{}", flow.getId(), ruleId);
                    }
                }
                logger.info("成功更新规则关联的所有流程状态为失败，规则ID：{}，流程数量：{}", ruleId, flows.size());
            } else {
                logger.debug("规则没有关联的流程，规则ID：{}", ruleId);
            }
        } catch (Exception e) {
            logger.error("更新流程状态失败，规则ID：{}，异常信息：{}", ruleId, e.getMessage(), e);
        }
    }

    /**
     * 基础数据集合内部类
     */
    private static class StartBaseDataCollection {
        private final List<Long> centerIdList;
        private final List<Long> computerIdList;
        private final List<Long> souceComputerIdList;

        public StartBaseDataCollection(List<Long> centerIdList, List<Long> computerIdList, List<Long> souceComputerIdList) {
            this.centerIdList = centerIdList;
            this.computerIdList = computerIdList;
            this.souceComputerIdList = souceComputerIdList;
        }

        public List<Long> getCenterIdList() {
            return centerIdList;
        }

        public List<Long> getComputerIdList() {
            return computerIdList;
        }

        public List<Long> getSouceComputerIdList() {
            return souceComputerIdList;
        }
    }

    /**
     * 外部数据集合内部类
     */
    private static class ExternalDataCollection {
        private final Map<Long, String> centerMap;
        private final Map<Long, ComputerInfoDto> computerInfoDtoMap;

        public ExternalDataCollection(Map<Long, String> centerMap, Map<Long, ComputerInfoDto> computerInfoDtoMap) {
            this.centerMap = centerMap;
            this.computerInfoDtoMap = computerInfoDtoMap;
        }

        public Map<Long, String> getCenterMap() {
            return centerMap;
        }

        public Map<Long, ComputerInfoDto> getComputerInfoDtoMap() {
            return computerInfoDtoMap;
        }
    }
}
