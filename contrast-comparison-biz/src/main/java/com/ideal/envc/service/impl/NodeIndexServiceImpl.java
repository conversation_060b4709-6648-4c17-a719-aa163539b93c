package com.ideal.envc.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ideal.common.dto.TableQueryDto;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.NodeRelationMapper;
import com.ideal.envc.mapper.NodeRuleContentMapper;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.mapper.SystemComputerNodeMapper;
import com.ideal.envc.model.dto.NodeBatchSaveRequestDto;
import com.ideal.envc.model.dto.NodeRelationContentDto;
import com.ideal.envc.model.dto.SystemComputerDto;
import com.ideal.envc.model.dto.SystemComputerQueryPageDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.entity.NodeRelationEntity;
import com.ideal.envc.model.entity.NodeRuleContentEntity;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.model.entity.SystemComputerNodeEntity;
import com.ideal.envc.model.enums.ResponseCodeEnum;
import com.ideal.envc.service.INodeIndexService;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class NodeIndexServiceImpl implements INodeIndexService {
    private final Logger logger = LoggerFactory.getLogger(NodeIndexServiceImpl.class);
    //节点关系表接口服务
    private final SystemComputerNodeMapper systemComputerNodeMapper;
    //节点关系配置
    private final NodeRelationMapper nodeRelationMapper;
    //规则内容表接口服务
    private final NodeRuleContentMapper nodeRuleContentMapper;
    //系统设备关系表接口服务
    private final SystemComputerMapper systemComputerMapper;
    //节点关系配置表接口服务
    private final BatchHandler batchHandler;
    public NodeIndexServiceImpl(SystemComputerNodeMapper systemComputerNodeMapper, NodeRelationMapper nodeRelationMapper, NodeRuleContentMapper nodeRuleContentMapper, SystemComputerMapper systemComputerMapper, BatchHandler batchHandler) {
        this.systemComputerNodeMapper = systemComputerNodeMapper;
        this.nodeRelationMapper = nodeRelationMapper;
        this.nodeRuleContentMapper = nodeRuleContentMapper;
        this.systemComputerMapper = systemComputerMapper;
        this.batchHandler = batchHandler;
    }


    /**
     * 批量保存节点规则信息关系
     *
     * @param nodeBatchSaveRequestDto 节点规则关系
     * @param userDto 用户信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean batchSaveNodeRelation(NodeBatchSaveRequestDto nodeBatchSaveRequestDto, UserDto userDto) {
        // 参数校验
        if (!validateBatchSaveRequest(nodeBatchSaveRequestDto)) {
            return false;
        }

        // 构建节点关系实体列表
        List<SystemComputerNodeEntity> systemComputerNodeEntityList = buildSystemComputerNodeEntities(nodeBatchSaveRequestDto, userDto);

        // 构建节点关系规则和内容实体列表
        List<NodeRelationEntity> nodeRelationEntityList = new ArrayList<>();
        List<NodeRuleContentEntity> nodeRuleContentEntityList = new ArrayList<>();
        buildNodeRelationAndContentEntities(nodeBatchSaveRequestDto, userDto, systemComputerNodeEntityList, 
                nodeRelationEntityList, nodeRuleContentEntityList);

        // 批量保存数据
        saveBatchData(systemComputerNodeEntityList, nodeRelationEntityList, nodeRuleContentEntityList);
        
        return true;
    }

    /**
     * 验证批量保存请求参数
     *
     * @param nodeBatchSaveRequestDto 请求参数
     * @return 验证结果
     */
    private boolean validateBatchSaveRequest(NodeBatchSaveRequestDto nodeBatchSaveRequestDto) {
        if (nodeBatchSaveRequestDto == null) {
            logger.error("增加节点及规则信息入参对象为空");
            return false;
        }
        if (nodeBatchSaveRequestDto.getSourceComputerId() == null) {
            logger.error("增加节点及规则信息源设备ID为空");
            return false;
        }
        if (nodeBatchSaveRequestDto.getTargetComputerIdList() == null || nodeBatchSaveRequestDto.getTargetComputerIdList().isEmpty()) {
            logger.error("增加节点及规则信息目标设备为空");
            return false;
        }
        if (nodeBatchSaveRequestDto.getSourceCenterId() == null) {
            logger.error("增加节点及规则信息源中心ID为空");
            return false;
        }
        if (nodeBatchSaveRequestDto.getTargetCenterId() == null) {
            logger.error("增加节点及规则信息目标中心ID为空");
            return false;
        }
        if (nodeBatchSaveRequestDto.getBusinessSystemId() == null) {
            logger.error("增加节点及规则信息业务系统ID为空");
            return false;
        }
        return true;
    }

    /**
     * 构建系统计算机节点实体列表
     *
     * @param nodeBatchSaveRequestDto 请求参数
     * @param userDto 用户信息
     * @return 系统计算机节点实体列表
     */
    private List<SystemComputerNodeEntity> buildSystemComputerNodeEntities(NodeBatchSaveRequestDto nodeBatchSaveRequestDto, UserDto userDto) {
        List<SystemComputerNodeEntity> systemComputerNodeEntityList = new ArrayList<>();
        List<Long> targetComputerIdList = nodeBatchSaveRequestDto.getTargetComputerIdList();
        
        // 获取目标计算机IP映射
        Map<Long, SystemComputerEntity> targetComputerMap = systemComputerMapper.selectComputerIpMapByIdsAndSystemId(
                targetComputerIdList, nodeBatchSaveRequestDto.getBusinessSystemId());
        Map<Long, String> ipMap = new HashMap<>();
        for (Map.Entry<Long, SystemComputerEntity> entry : targetComputerMap.entrySet()) {
            ipMap.put(entry.getKey(), entry.getValue().getComputerIp());
        }

        // 构建节点关系实体
        for (Long targetComputerId : targetComputerIdList) {
            SystemComputerNodeEntity systemComputerNodeEntity = createSystemComputerNodeEntity(
                    nodeBatchSaveRequestDto, userDto, targetComputerId, ipMap.get(targetComputerId));
            systemComputerNodeEntityList.add(systemComputerNodeEntity);
        }
        
        return systemComputerNodeEntityList;
    }

    /**
     * 创建系统计算机节点实体
     *
     * @param nodeBatchSaveRequestDto 请求参数
     * @param userDto 用户信息
     * @param targetComputerId 目标计算机ID
     * @param targetComputerIp 目标计算机IP
     * @return 系统计算机节点实体
     */
    private SystemComputerNodeEntity createSystemComputerNodeEntity(NodeBatchSaveRequestDto nodeBatchSaveRequestDto, 
            UserDto userDto, Long targetComputerId, String targetComputerIp) {
        SystemComputerNodeEntity systemComputerNodeEntity = new SystemComputerNodeEntity();
        systemComputerNodeEntity.setId(SnowflakeIdWorker.generateId());
        systemComputerNodeEntity.setSourceComputerId(nodeBatchSaveRequestDto.getSourceComputerId());
        systemComputerNodeEntity.setSourceCenterId(nodeBatchSaveRequestDto.getSourceCenterId());
        systemComputerNodeEntity.setSourceComputerIp(nodeBatchSaveRequestDto.getSourceIp());
        systemComputerNodeEntity.setTargetComputerId(targetComputerId);
        systemComputerNodeEntity.setTargetComputerIp(targetComputerIp);
        systemComputerNodeEntity.setTargetCenterId(nodeBatchSaveRequestDto.getTargetCenterId());
        systemComputerNodeEntity.setCreatorId(userDto.getId());
        systemComputerNodeEntity.setCreatorName(userDto.getFullName());
        return systemComputerNodeEntity;
    }

    /**
     * 构建节点关系规则和内容实体列表
     *
     * @param nodeBatchSaveRequestDto 请求参数
     * @param userDto 用户信息
     * @param systemComputerNodeEntityList 系统计算机节点实体列表
     * @param nodeRelationEntityList 节点关系实体列表（输出参数）
     * @param nodeRuleContentEntityList 节点规则内容实体列表（输出参数）
     */
    private void buildNodeRelationAndContentEntities(NodeBatchSaveRequestDto nodeBatchSaveRequestDto, UserDto userDto,
            List<SystemComputerNodeEntity> systemComputerNodeEntityList, List<NodeRelationEntity> nodeRelationEntityList,
            List<NodeRuleContentEntity> nodeRuleContentEntityList) {
        
        if (nodeBatchSaveRequestDto.getNodeRelationContentDtoList() == null || 
                nodeBatchSaveRequestDto.getNodeRelationContentDtoList().isEmpty()) {
            return;
        }

        List<NodeRelationContentDto> nodeRelationContentDtoList = nodeBatchSaveRequestDto.getNodeRelationContentDtoList();
        for (NodeRelationContentDto nodeRelationContentDto : nodeRelationContentDtoList) {
            for (SystemComputerNodeEntity systemComputerNodeEntity : systemComputerNodeEntityList) {
                // 创建节点关系实体
                NodeRelationEntity nodeRelationEntity = createNodeRelationEntity(
                        nodeRelationContentDto, userDto, systemComputerNodeEntity.getId());
                nodeRelationEntityList.add(nodeRelationEntity);

                // 创建节点规则内容实体
                NodeRuleContentEntity nodeRuleContentEntity = createNodeRuleContentEntity(
                        nodeRelationContentDto, nodeRelationEntity.getId());
                nodeRuleContentEntityList.add(nodeRuleContentEntity);
            }
        }
    }

    /**
     * 创建节点关系实体
     *
     * @param nodeRelationContentDto 节点关系内容DTO
     * @param userDto 用户信息
     * @param systemComputerNodeId 系统计算机节点ID
     * @return 节点关系实体
     */
    private NodeRelationEntity createNodeRelationEntity(NodeRelationContentDto nodeRelationContentDto, 
            UserDto userDto, Long systemComputerNodeId) {
        NodeRelationEntity nodeRelationEntity = new NodeRelationEntity();
        nodeRelationEntity.setId(SnowflakeIdWorker.generateId());
        nodeRelationEntity.setEnvcSystemComputerNodeId(systemComputerNodeId);
        nodeRelationEntity.setModel(nodeRelationContentDto.getModel());
        nodeRelationEntity.setType(nodeRelationContentDto.getType());
        nodeRelationEntity.setPath(nodeRelationContentDto.getPath());
        nodeRelationEntity.setEncode(nodeRelationContentDto.getEncode());
        nodeRelationEntity.setWay(nodeRelationContentDto.getWay());
        nodeRelationEntity.setRuleType(nodeRelationContentDto.getRuleType());
        nodeRelationEntity.setEnabled(nodeRelationContentDto.getEnabled());
        nodeRelationEntity.setChildLevel(nodeRelationContentDto.getChildLevel());
        nodeRelationEntity.setCreatorId(userDto.getId());
        nodeRelationEntity.setCreatorName(userDto.getFullName());
        return nodeRelationEntity;
    }

    /**
     * 创建节点规则内容实体
     *
     * @param nodeRelationContentDto 节点关系内容DTO
     * @param nodeRelationId 节点关系ID
     * @return 节点规则内容实体
     */
    private NodeRuleContentEntity createNodeRuleContentEntity(NodeRelationContentDto nodeRelationContentDto, Long nodeRelationId) {
        NodeRuleContentEntity nodeRuleContentEntity = new NodeRuleContentEntity();
        nodeRuleContentEntity.setId(SnowflakeIdWorker.generateId());
        nodeRuleContentEntity.setEnvcNodeRelationId(nodeRelationId);
        nodeRuleContentEntity.setRuleContent(nodeRelationContentDto.getContent());
        return nodeRuleContentEntity;
    }

    /**
     * 批量保存数据
     *
     * @param systemComputerNodeEntityList 系统计算机节点实体列表
     * @param nodeRelationEntityList 节点关系实体列表
     * @param nodeRuleContentEntityList 节点规则内容实体列表
     */
    private void saveBatchData(List<SystemComputerNodeEntity> systemComputerNodeEntityList,
            List<NodeRelationEntity> nodeRelationEntityList, List<NodeRuleContentEntity> nodeRuleContentEntityList) {
        // 批量插入节点关系表保存节点关系
        batchHandler.batchData(systemComputerNodeEntityList, systemComputerNodeMapper::insertSystemComputerNode, 1000);
        // 批量插入节点关系规则表保存规则
        batchHandler.batchData(nodeRelationEntityList, nodeRelationMapper::insertNodeRelation, 1000);
        // 批量插入节点规则内容表保存规则内容
        batchHandler.batchData(nodeRuleContentEntityList, nodeRuleContentMapper::insertNodeRuleContent, 1000);
    }

    /**
     * 系统设备查询分页
     *
     * @param tableQueryDto 查询条件
     * @return 设备列表分页信息
     * @throws ContrastBusinessException 业务异常
     */
    @Override
    public PageInfo<SystemComputerDto> selectSystemComputerListPage(TableQueryDto<SystemComputerQueryPageDto> tableQueryDto) throws ContrastBusinessException {
        if (tableQueryDto == null || tableQueryDto.getQueryParam() == null) {
            logger.error("查询系统设备列表参数为空!");
            throw new ContrastBusinessException(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode() + "：查询参数不能为空");
        }

        SystemComputerQueryPageDto systemComputerQueryPageDto = tableQueryDto.getQueryParam();
        // 参数校验
        if (systemComputerQueryPageDto.getBusinessSystemId() == null 
                || systemComputerQueryPageDto.getSourceCenterId() == null 
                || systemComputerQueryPageDto.getTargetCenterId() == null 
                || systemComputerQueryPageDto.getSourceComputerId() == null) {
            logger.warn("查询系统设备列表参数不完整，业务系统ID、源中心ID、目标中心ID和源设备ID都不能为空");
            throw new ContrastBusinessException(ResponseCodeEnum.QUERY_PARAM_ERROR.getCode() + "：业务系统ID、源中心ID、目标中心ID和源设备ID不能为空");
        }

        // 获取需要排除的设备ID列表
        List<Long> excludeComputerIds = getExcludeComputerIds(systemComputerQueryPageDto);
        
        // 使用 PageHelper 进行分页查询
        PageHelper.startPage(tableQueryDto.getPageNum(), tableQueryDto.getPageSize());
        List<SystemComputerEntity> systemComputerList = systemComputerMapper.selectComputerListByCondition(
                systemComputerQueryPageDto.getBusinessSystemId(), 
                systemComputerQueryPageDto.getTargetCenterId(), 
                excludeComputerIds
        );

        // 将实体对象转换为 DTO 对象
        List<SystemComputerDto> systemComputerDtoList = BeanUtils.copy(systemComputerList, SystemComputerDto.class);

        // 构建分页结果
        PageInfo<SystemComputerEntity> pageInfo = new PageInfo<>(systemComputerList);
        PageInfo<SystemComputerDto> result = new PageInfo<>(systemComputerDtoList);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());

        return result;
    }

    /**
     * 获取需要排除的设备ID列表
     *
     * @param queryDto 查询条件
     * @return 需要排除的设备ID列表
     */
    private List<Long> getExcludeComputerIds(SystemComputerQueryPageDto queryDto) {
        SystemComputerNodeEntity queryEntity = new SystemComputerNodeEntity();
        queryEntity.setBusinessSystemId(queryDto.getBusinessSystemId());
        queryEntity.setSourceCenterId(queryDto.getSourceCenterId());
        queryEntity.setTargetCenterId(queryDto.getTargetCenterId());
        queryEntity.setSourceComputerId(queryDto.getSourceComputerId());
        
        List<SystemComputerNodeEntity> existingNodes = systemComputerNodeMapper.selectSystemComputerNodeList(queryEntity);
        if (existingNodes != null && !existingNodes.isEmpty()) {
            return existingNodes.stream()
                    .map(SystemComputerNodeEntity::getTargetComputerId)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 系统设备查询列表（不分页）
     *
     * @param businessSystemId 业务系统ID
     * @param centerId 中心ID
     * @param excludeComputerIds 排除设备ID集合
     * @return 设备列表
     */
    @Override
    public List<SystemComputerDto> selectSystemComputerList(Long businessSystemId, Long centerId, List<Long> excludeComputerIds) {
        logger.info("查询系统设备列表（不分页），业务系统ID：{}，中心ID：{}，排除设备ID集合：{}", businessSystemId, centerId, excludeComputerIds);

        // 参数校验
        if (businessSystemId == null) {
            logger.warn("查询系统设备列表参数不完整，业务系统ID不能为空");
            return new ArrayList<>();
        }

        // 调用不分页的查询方法
        List<SystemComputerEntity> systemComputerList = systemComputerMapper.selectComputerListByCondition(businessSystemId, centerId, excludeComputerIds);

        // 将实体对象转换为 DTO 对象
        return BeanUtils.copy(systemComputerList, SystemComputerDto.class);
    }
}
