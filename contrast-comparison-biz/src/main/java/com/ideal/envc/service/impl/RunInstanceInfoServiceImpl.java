package com.ideal.envc.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.RunInstanceInfoMapper;
import com.ideal.envc.model.entity.RunInstanceInfoEntity;
import com.ideal.envc.service.IRunInstanceInfoService;
import com.ideal.envc.model.dto.RunInstanceInfoDto;
import com.ideal.envc.model.dto.RunInstanceInfoQueryDto;

import java.util.List;

/**
 * 实例详情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Service
public class RunInstanceInfoServiceImpl implements IRunInstanceInfoService {
    private final RunInstanceInfoMapper runInstanceInfoMapper;

    public RunInstanceInfoServiceImpl(RunInstanceInfoMapper runInstanceInfoMapper) {
        this.runInstanceInfoMapper = runInstanceInfoMapper;
    }

    /**
     * 查询实例详情
     *
     * @param id 实例详情主键
     * @return 实例详情
     */
    @Override
    public RunInstanceInfoDto selectRunInstanceInfoById(Long id) {
        RunInstanceInfoEntity runInstanceInfo = runInstanceInfoMapper.selectRunInstanceInfoById(id);
        return BeanUtils.copy(runInstanceInfo, RunInstanceInfoDto.class);
    }

    /**
     * 查询实例详情列表
     *
     * @param runInstanceInfoQueryDto 实例详情
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 实例详情
     */
    @Override
    public PageInfo<RunInstanceInfoDto> selectRunInstanceInfoList(RunInstanceInfoQueryDto runInstanceInfoQueryDto, Integer pageNum, Integer pageSize) {
        RunInstanceInfoEntity query = BeanUtils.copy(runInstanceInfoQueryDto, RunInstanceInfoEntity.class);
        PageMethod.startPage(pageNum, pageSize);
        List<RunInstanceInfoEntity> runInstanceInfoList = runInstanceInfoMapper.selectRunInstanceInfoList(query);
        return PageDataUtil.toDtoPage(runInstanceInfoList, RunInstanceInfoDto.class);
    }

    /**
     * 新增实例详情
     *
     * @param runInstanceInfoDto 实例详情
     * @return 结果
     */
    @Override
    public int insertRunInstanceInfo(RunInstanceInfoDto runInstanceInfoDto) {
        RunInstanceInfoEntity runInstanceInfo = BeanUtils.copy(runInstanceInfoDto, RunInstanceInfoEntity.class);
        return runInstanceInfoMapper.insertRunInstanceInfo(runInstanceInfo);
    }

    /**
     * 修改实例详情
     *
     * @param runInstanceInfoDto 实例详情
     * @return 结果
     */
    @Override
    public int updateRunInstanceInfo(RunInstanceInfoDto runInstanceInfoDto) {
        RunInstanceInfoEntity runInstanceInfo = BeanUtils.copy(runInstanceInfoDto, RunInstanceInfoEntity.class);
        return runInstanceInfoMapper.updateRunInstanceInfo(runInstanceInfo);
    }

    /**
     * 批量删除实例详情
     *
     * @param ids 需要删除的实例详情主键
     * @return 结果
     */
    @Override
    public int deleteRunInstanceInfoByIds(Long[] ids) {
        return runInstanceInfoMapper.deleteRunInstanceInfoByIds(ids);
    }
}
