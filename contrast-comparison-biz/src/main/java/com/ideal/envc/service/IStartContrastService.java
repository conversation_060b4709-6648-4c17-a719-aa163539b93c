package com.ideal.envc.service;


import com.ideal.envc.model.dto.NodeLevelStartDto;
import com.ideal.envc.model.dto.PlanLevelStartDto;
import com.ideal.envc.model.dto.RuleLevelStartDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.SystemLevelStartDto;
import com.ideal.envc.model.dto.TaskLevelStartDto;
import com.ideal.envc.model.dto.UserDto;

/**
 * 一致性比对启动Service接口
 *
 * <AUTHOR>
 */
public interface IStartContrastService {

    /**
     * 方案级启动
     *
     * @param startDto 方案级启动DTO
     * @param userDto 操作用户
     * @return 启动结果
     */
    StartResult startByPlans(PlanLevelStartDto startDto, UserDto userDto) ;

    /**
     * 系统级启动
     *
     * @param startDto 系统级启动DTO
     * @return 启动结果
     */
    StartResult startBySystems(SystemLevelStartDto startDto, UserDto userDto);

    /**
     * 节点级启动
     *
     * @param startDto 节点级启动DTO
     * @return 启动结果
     */
    StartResult startByNodes(NodeLevelStartDto startDto, UserDto userDto);

    /**
     * 规则级启动
     *
     * @param startDto 规则级启动DTO
     * @return 启动结果
     */
    StartResult startByRules(RuleLevelStartDto startDto, UserDto userDto);

    /**
     * 任务级启动
     *
     * @param startDto 任务级启动DTO
     * @return 启动结果
     */
    StartResult startByTasks(TaskLevelStartDto startDto, UserDto userDto);


}
