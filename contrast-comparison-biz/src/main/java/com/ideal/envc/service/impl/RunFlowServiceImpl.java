package com.ideal.envc.service.impl;

import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.PageDataUtil;
import org.springframework.stereotype.Service;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.service.IRunFlowService;
import com.ideal.envc.model.dto.RunFlowDto;
import com.ideal.envc.model.dto.RunFlowQueryDto;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ideal.envc.exception.ContrastBusinessException;

import java.util.List;

/**
 * 节点规则流程Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class RunFlowServiceImpl implements IRunFlowService {
    private final Logger logger = LoggerFactory.getLogger(RunFlowServiceImpl.class);

    private final RunFlowMapper runFlowMapper;

    public RunFlowServiceImpl(RunFlowMapper runFlowMapper) {
        this.runFlowMapper = runFlowMapper;
    }

    /**
     * 查询节点规则流程
     *
     * @param id 节点规则流程主键
     * @return 节点规则流程
     */
    @Override
    public RunFlowDto selectRunFlowById(Long id) throws ContrastBusinessException {
        try {
            if (id == null) {
                logger.warn("查询流程详情失败，ID为空");
                return null;
            }
            
            RunFlowEntity runFlow = runFlowMapper.selectRunFlowById(id);
            if (runFlow == null) {
                logger.warn("查询流程详情失败，未找到ID为{}的记录", id);
                return null;
            }
            
            return BeanUtils.copy(runFlow, RunFlowDto.class);
        } catch (Exception e) {
            logger.error("查询流程详情失败", e);
            throw new ContrastBusinessException("查询流程详情失败：" + e.getMessage());
        }
    }

    /**
     * 查询节点规则流程列表
     *
     * @param runFlowQueryDto 节点规则流程
     * @param pageNum 页码
     * @param pageSize 单页长度
     * @return 节点规则流程
     */
    @Override
    public PageInfo<RunFlowDto> selectRunFlowList(RunFlowQueryDto runFlowQueryDto, Integer pageNum, Integer pageSize) throws ContrastBusinessException {
        try {
            RunFlowEntity query = BeanUtils.copy(runFlowQueryDto, RunFlowEntity.class);
            PageMethod.startPage(pageNum, pageSize);
            List<RunFlowEntity> runFlowList = runFlowMapper.selectRunFlowList(query);
            return PageDataUtil.toDtoPage(runFlowList, RunFlowDto.class);
        } catch (Exception e) {
            logger.error("查询流程列表失败", e);
            throw new ContrastBusinessException("查询流程列表失败：" + e.getMessage());
        }
    }

    /**
     * 新增节点规则流程
     *
     * @param runFlowDto 节点规则流程
     * @return 结果
     */
    @Override
    public int insertRunFlow(RunFlowDto runFlowDto) throws ContrastBusinessException {
        try {
            RunFlowEntity runFlow = BeanUtils.copy(runFlowDto, RunFlowEntity.class);
            return runFlowMapper.insertRunFlow(runFlow);
        } catch (Exception e) {
            logger.error("新增流程失败", e);
            throw new ContrastBusinessException("新增流程失败：" + e.getMessage());
        }
    }

    /**
     * 修改节点规则流程
     *
     * @param runFlowDto 节点规则流程
     * @return 结果
     */
    @Override
    public int updateRunFlow(RunFlowDto runFlowDto) throws ContrastBusinessException {
        try {
            RunFlowEntity runFlow = BeanUtils.copy(runFlowDto, RunFlowEntity.class);
            return runFlowMapper.updateRunFlow(runFlow);
        } catch (Exception e) {
            logger.error("修改流程失败", e);
            throw new ContrastBusinessException("修改流程失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除节点规则流程
     *
     * @param ids 需要删除的节点规则流程主键
     * @return 结果
     */
    @Override
    public int deleteRunFlowByIds(Long[] ids) throws ContrastBusinessException {
        try {
            return runFlowMapper.deleteRunFlowByIds(ids);
        } catch (Exception e) {
            logger.error("批量删除流程失败", e);
            throw new ContrastBusinessException("批量删除流程失败：" + e.getMessage());
        }
    }
}
