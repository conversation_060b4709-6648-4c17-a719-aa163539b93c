package com.ideal.envc.component;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.mapper.EngineActOutputMapper;
import com.ideal.envc.mapper.RunFlowMapper;
import com.ideal.envc.mapper.RunFlowResultMapper;
import com.ideal.envc.mapper.RunRuleMapper;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.OutputParseResult;
import com.ideal.envc.model.entity.RunFlowEntity;
import com.ideal.envc.model.entity.RunFlowResultEntity;
import com.ideal.envc.model.entity.RunRuleEntity;
import com.ideal.envc.model.enums.ActivityStateEnum;
import com.ideal.envc.model.enums.ActivityTypeEnum;
import com.ideal.envc.model.enums.FlowStateEnum;
import com.ideal.envc.model.enums.RuleITypeEnums;
import com.ideal.envc.model.enums.RuleModelEnum;
import com.ideal.envc.model.enums.ScriptActivityNameEnum;
import com.ideal.envc.service.IRunInstanceInfoStateProcessService;
import com.ideal.envc.strategy.OutputParseStrategy;
import com.ideal.envc.strategy.OutputParseStrategyFactory;
import com.ideal.monitor.mq.ActOutputNotification;
import com.ideal.monitor.mq.MonitorFlowDto;
import com.ideal.monitor.mq.MonitorFowActiveNodeDto;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 监控数据回调接口
 * <AUTHOR>
 */
@Component
public class EngineDataExecuteComponent {

    private static final Logger logger = LoggerFactory.getLogger(EngineDataExecuteComponent.class);
    private final RunFlowResultMapper runFlowResultEntityMapper;
    private final RunRuleMapper runRuleMapper;
    private final RunFlowMapper runFlowMapper;
    private final IRunInstanceInfoStateProcessService runInstanceInfoStateProcessService;
    private final EngineActOutputMapper engineActOutputMapper;
    private final OutputParseStrategyFactory outputParseStrategyFactory;

    public EngineDataExecuteComponent(RunFlowResultMapper runFlowResultEntityMapper, RunRuleMapper runRuleMapper, RunFlowMapper runFlowMapper, IRunInstanceInfoStateProcessService runInstanceInfoStateProcessService, EngineActOutputMapper engineActOutputMapper, OutputParseStrategyFactory outputParseStrategyFactory) {
        this.runFlowResultEntityMapper = runFlowResultEntityMapper;
        this.runRuleMapper = runRuleMapper;
        this.runFlowMapper = runFlowMapper;
        this.runInstanceInfoStateProcessService = runInstanceInfoStateProcessService;
        this.engineActOutputMapper = engineActOutputMapper;
        this.outputParseStrategyFactory = outputParseStrategyFactory;
    }

    /**
     * 日常操作 监控回调处理方法
     * @param monitorFlowDto 监控回调报文
     */
    @Transactional
    public void executeMonitorData(MonitorFlowDto monitorFlowDto) {
        logger.info("receive flow monitor callback monitorFlowDto message:{}", JSON.toJSONString(monitorFlowDto));
        if (!checkMonitorMessage(monitorFlowDto)) {
            logger.info("receive flow monitor callback message check not passed !");
            return;
        }

        MonitorFowActiveNodeDto activeNodeDto = monitorFlowDto.getMonitorFowActiveNodeDto();
        
        // 处理流程信息或活动信息
        if (activeNodeDto == null) {
            // 回更的是流程信息
            processFlowInformation(monitorFlowDto);
        } else {
            // 回更的是活动信息
            processActivityInformation(monitorFlowDto, activeNodeDto);
        }
    }
    
    /**
     * 处理流程信息
     * @param monitorFlowDto 监控回调报文
     */
    private void processFlowInformation(MonitorFlowDto monitorFlowDto) {
        RunFlowEntity runFlowEntity = runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId());
        if((runFlowEntity.getFlowid()==null ||  runFlowEntity.getFlowid()==0) && Objects.equals(monitorFlowDto.getFlowStatus(), FlowStateEnum.RUNNING.getCode())){
            runFlowEntity.setFlowid(monitorFlowDto.getFlowId());
            runFlowEntity.setState(FlowStateEnum.RUNNING.getCode());
            runFlowEntity.setUpdateOrderTime(monitorFlowDto.getUpdateOrderTime());
            runFlowMapper.updateRunFlowOfFirst(runFlowEntity);
        }
        // 同步引擎工作流数据到，更新业务数据
        else if(FlowStateEnum.isCompletedState(monitorFlowDto.getFlowStatus()) || monitorFlowDto.getFlowStatus()==70){

            updateRunFlowEntity(runFlowEntity, monitorFlowDto);
            //中间状态为了回更流程id
            if(monitorFlowDto.getFlowStatus()==70){
                runFlowMapper.updateRunFlowOfFirst(runFlowEntity);
                return;
            }else{
                runFlowMapper.updateRunFlow(runFlowEntity);
            }
            // 更新规则运行表
            updateRunRuleEntity(runFlowEntity, monitorFlowDto);
        }
    }
    
    /**
     * 更新运行流程实体
     * @param runFlowEntity 运行流程实体
     * @param monitorFlowDto 监控回调报文
     */
    private void updateRunFlowEntity(RunFlowEntity runFlowEntity, MonitorFlowDto monitorFlowDto) {
        runFlowEntity.setFlowid(monitorFlowDto.getFlowId());
        runFlowEntity.setCreateTime(new java.sql.Date(monitorFlowDto.getFlowStartTime()));
        runFlowEntity.setEndTime(new java.sql.Date(monitorFlowDto.getFlowEndTime()));
        runFlowEntity.setState(monitorFlowDto.getFlowStatus());
        runFlowEntity.setUpdateOrderTime(monitorFlowDto.getUpdateOrderTime());
        if(monitorFlowDto.getFlowEndTime()!=null && monitorFlowDto.getFlowEndTime()>0){
            runFlowEntity.setElapsedTime(monitorFlowDto.getFlowEndTime() - monitorFlowDto.getFlowStartTime());
        }
    }
    
    /**
     * 更新规则运行实体
     * @param runFlowEntity 运行流程实体
     * @param monitorFlowDto 监控回调报文
     */
    private void updateRunRuleEntity(RunFlowEntity runFlowEntity, MonitorFlowDto monitorFlowDto) {
        RunRuleEntity runRuleEntity = runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId());
        runRuleEntity.setState(monitorFlowDto.getFlowStatus().equals(FlowStateEnum.FINISHED.getCode())?1:2);
        runRuleEntity.setEndTime(new java.sql.Date(monitorFlowDto.getFlowEndTime()));
        runRuleEntity.setElapsedTime(runFlowEntity.getElapsedTime());

        // 判断规则result字段是否仍为-1，如果是则设置为1（比对失败）
        if (runRuleEntity.getResult() != null && runRuleEntity.getResult() == -1) {
            logger.info("流程已完成但规则result仍为-1，未获取到预期的活动类型活动结果，设置result为1（比对失败），规则ID：{}", runRuleEntity.getId());
            runRuleEntity.setResult(1);
        }

        runRuleMapper.updateRunRule(runRuleEntity);

        // 规则状态变更，触发实例详情计数器减一，并判断是否计数器是否规0，如果0，则调用实例详情状态变更。
        runInstanceInfoStateProcessService.processInstanceInfoStateUpdate(
                runRuleEntity.getEnvcRunInstanceInfoId(), 
                runRuleEntity.getId(), 
                monitorFlowDto.getUpdateOrderTime());
    }
    
    /**
     * 处理活动信息
     * @param monitorFlowDto 监控回调报文
     * @param activeNodeDto 活动节点信息
     */
    private void processActivityInformation(MonitorFlowDto monitorFlowDto, MonitorFowActiveNodeDto activeNodeDto) {
        ActOutputNotification actOutputNotification = monitorFlowDto.getMonitorActOutput();
        if (!canFlowContentModify(monitorFlowDto) || activeNodeDto == null) {
            return;
        }

        String reqId = activeNodeDto.getReqId();
        RunFlowResultEntity existRunFlowResultEntity = runFlowResultEntityMapper.selectRunFlowResultByEnvcRunFlowId(monitorFlowDto.getBizUniqueId());
        RunFlowEntity runFlowEntity = runFlowMapper.selectRunFlowById(monitorFlowDto.getBizUniqueId());
        RunRuleEntity runRuleEntity = runRuleMapper.selectRunRuleById(runFlowEntity.getRunBizId());
        
        // 解析输出结果
        OutputParseResult parseResult = parseActivityOutput(runRuleEntity, reqId, activeNodeDto.getActDefName());
        
        // 更新流程结果
        updateFlowResult(monitorFlowDto, existRunFlowResultEntity, parseResult.getContent());
        
        // 更新规则结果
        runRuleEntity.setResult(parseResult.isRet() ? 0 : 1);
        runRuleMapper.updateRunRuleOfResult(runRuleEntity.getResult(), runRuleEntity.getId());
    }
    
    /**
     * 解析活动输出
     * @param runRuleEntity 规则实体
     * @param reqId 请求ID
     * @param actDefName 活动定义名称
     * @return 解析结果
     */
    private OutputParseResult parseActivityOutput(RunRuleEntity runRuleEntity, String reqId, String actDefName) {
        List<EngineActOutputBean> actOutputs = engineActOutputMapper.queryOutputByReqId(reqId);
        Integer model = runRuleEntity.getModel();
        Long type = runRuleEntity.getType();
        if(RuleModelEnum.COMPARE.getCode()==runRuleEntity.getModel()){
            type = RuleITypeEnums.FILE.getCode();
        }
        OutputParseStrategy strategy = outputParseStrategyFactory.getStrategy(model, type);
        
        if (strategy != null) {
            return strategy.parse(actOutputs, actDefName);
        }
        
        return new OutputParseResult(false, "");
    }
    
    /**
     * 更新流程结果
     * @param monitorFlowDto 监控回调报文
     * @param existRunFlowResultEntity 已存在的流程结果实体
     * @param content 内容
     */
    private void updateFlowResult(MonitorFlowDto monitorFlowDto, RunFlowResultEntity existRunFlowResultEntity, String content) {
        if (existRunFlowResultEntity == null) {
            // 同步引擎工作流数据到，更新业务数据
            RunFlowResultEntity runFlowResultEntity = new RunFlowResultEntity();
            runFlowResultEntity.setFlowid(monitorFlowDto.getFlowId());
            runFlowResultEntity.setEnvcRunFlowId(monitorFlowDto.getBizUniqueId());
            runFlowResultEntity.setContent(content);
            runFlowResultEntity.setUpdateOrderTime(monitorFlowDto.getUpdateOrderTime());
            runFlowResultEntityMapper.insertRunFlowResult(runFlowResultEntity);
        } else {
            existRunFlowResultEntity.setUpdateOrderTime(monitorFlowDto.getUpdateOrderTime());
            existRunFlowResultEntity.setContent(content);
            runFlowResultEntityMapper.updateRunFlowResult(existRunFlowResultEntity);
        }
    }

    private boolean canFlowContentModify(MonitorFlowDto monitorFlowDto){
        ActOutputNotification actOutputNotification = monitorFlowDto.getMonitorActOutput();
        MonitorFowActiveNodeDto monitorFowActiveNodeDto = monitorFlowDto.getMonitorFowActiveNodeDto();
        boolean activityState = ActivityStateEnum.ACT_STATE_FINISH.getCode().equals(monitorFowActiveNodeDto.getActStatus())
                || ActivityStateEnum.ACT_STATE_FAIL.getCode().equals(monitorFowActiveNodeDto.getActStatus())
                || ActivityStateEnum.ACT_STATE_FAIL_BUSINESS.getCode().equals(monitorFowActiveNodeDto.getActStatus())
                || ActivityStateEnum.ACT_STATE_FAIL_SKIPPED.getCode().equals(monitorFowActiveNodeDto.getActStatus())
                || ActivityStateEnum.ACT_STATE_SKIPPED.getCode().equals(monitorFowActiveNodeDto.getActStatus())
                ;
        if(activityState){
            //文件比对活动
            if(ActivityTypeEnum.COMPARE_CONTENT.getCode().equals(monitorFowActiveNodeDto.getActDefName()) ){
                return true;
            }
            //文件同步活动
            if(ActivityTypeEnum.SYNC_CONTENT.getCode().equals(monitorFowActiveNodeDto.getActDefName())){
                return true;
            }
            return ActivityTypeEnum.SHELL_CMD.getCode().equals(monitorFowActiveNodeDto.getActDefName())
                    && ScriptActivityNameEnum.isValidCode(monitorFowActiveNodeDto.getActName());
        }
        return false;
    }

    private String getContent(Map<String,Object> outPutMap,String actType){
        String content = "";
        if(outPutMap!=null){
            if(ActivityTypeEnum.COMPARE_CONTENT.getCode().equals(actType) ){
                content = outPutMap.get(ContrastConstants.COMPARE_RESULT)!=null? outPutMap.get(ContrastConstants.COMPARE_RESULT).toString() :(outPutMap.get("err")!=null?outPutMap.get("err").toString():"");
            }
            else if(ActivityTypeEnum.SYNC_CONTENT.getCode().equals(actType)){
                content = outPutMap.get(ContrastConstants.SYNC_RESULT)!=null?outPutMap.get(ContrastConstants.SYNC_RESULT).toString():(outPutMap.get("err")!=null?outPutMap.get("err").toString():"");
            }
            else  {
               String stdOut = outPutMap.get(ContrastConstants.STDOUT)!=null?outPutMap.get(ContrastConstants.STDOUT).toString():"";
               if(StringUtils.isBlank(stdOut)){
                  String stdErr = outPutMap.get(ContrastConstants.STDERR)!=null?outPutMap.get(ContrastConstants.STDERR).toString():"";
                   if(StringUtils.isBlank(stdErr)){
                       content = outPutMap.get(ContrastConstants.ERR)!=null?outPutMap.get(ContrastConstants.ERR).toString():"";
                   }else{
                       content = stdErr;
                   }
               }else {
                   content = stdOut;
               }

            }
        }
        return content;
    }

    private boolean checkMonitorMessage(MonitorFlowDto monitorFlowDto){
        if(monitorFlowDto==null){
            logger.info("receive  monitor  callback message is null");
            return false;
        }

        if(monitorFlowDto.getFlowId()==null||monitorFlowDto.getFlowId()<=0){
            logger.info("receive  monitor  callback message has flowId is null");
            return false;
        }

        if(monitorFlowDto.getFlowStatus()==null){
            logger.info("receive  monitor  callback message has flow status is null");
            return false;
        }

        if(monitorFlowDto.getBizUniqueId()==null || monitorFlowDto.getBizUniqueId()<=0){
            logger.info("receive  monitor  callback message has bizUniqueId is null");
            return false;
        }

        if(monitorFlowDto.getDateTime()==null){
            logger.info("receive  monitor  callback message has lastTimestamp is null");
            return false;
        }
        return true;
    }
}
