package com.ideal.envc.component;

import com.ideal.envc.common.ContrastConstants;
import com.ideal.envc.exception.ContrastBusinessException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.Collection;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

/**
 * 任务计数器处理组件
 * 用于管理Redis中的任务计数器，支持高并发场景下的安全操作
 * 
 * <AUTHOR>
 */
@Component
public class TaskCounterComponent {

    private static final Logger logger = LoggerFactory.getLogger(TaskCounterComponent.class);

    /** 分布式锁超时时间（秒） */
    public static final long LOCK_TIMEOUT = 30L;
    
    /** 分布式锁等待时间（秒） */
    public static final long LOCK_WAIT_TIME = 10L;

    private final RedissonClient redissonClient;

    public TaskCounterComponent(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 初始化运行实例计数器
     * 
     * @param instanceId 实例ID
     * @param initialValue 初始值
     * @throws ContrastBusinessException 业务异常
     */
    public void initInstanceCounter(Long instanceId, Integer initialValue) throws ContrastBusinessException {
        if (instanceId == null) {
            throw new ContrastBusinessException("实例ID不能为空");
        }
        if (initialValue == null || initialValue < 0) {
            throw new ContrastBusinessException("初始值不能为空且必须大于等于0");
        }

        String counterKey = ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX + instanceId;
        String lockKey = ContrastConstants.RUN_INSTANCE_COUNTER_LOCK_PREFIX + instanceId;

        RLock lock = redissonClient.getLock(lockKey);
        boolean isLocked = false;

        try {
            lock = redissonClient.getLock(lockKey);
            // 尝试获取分布式锁
            isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS);

            if (!isLocked) {
                logger.error("获取分布式锁超时，实例ID：{}", instanceId);
                throw new ContrastBusinessException("初始化计数器时获取锁超时");
            }

            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);
            counter.set(initialValue);
            lock.unlock();
            isLocked =false;
            logger.info("成功初始化运行实例计数器，实例ID：{}，初始值：{}", instanceId, initialValue);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 保留中断状态
            logger.error("初始化运行实例计数器时被中断，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("初始化计数器时被中断");
        } catch (Exception e) {
            logger.error("初始化运行实例计数器失败，实例ID：{}，初始值：{}", instanceId, initialValue, e);
            throw new ContrastBusinessException("初始化计数器失败：" + e.getMessage());
        } finally {
            // 确保锁被释放，只检查锁是否为null和是否被当前线程持有
            if (lock != null && isLocked ) {
                lock.unlock();
            }
        }
    }

    /**
     * 初始化运行实例详情计数器
     * 
     * @param instanceInfoId 实例详情ID
     * @param initialValue 初始值
     * @throws ContrastBusinessException 业务异常
     */
    public void initInstanceInfoCounter(Long instanceInfoId, Integer initialValue) throws ContrastBusinessException {
        if (instanceInfoId == null) {
            throw new ContrastBusinessException("实例详情ID不能为空");
        }
        if (initialValue == null || initialValue < 0) {
            throw new ContrastBusinessException("初始值不能为空且必须大于等于0");
        }

        String counterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        String lockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        
        RLock lock = null;
        boolean isLocked = false;
        
        try {
            lock = redissonClient.getLock(lockKey);
            // 尝试获取分布式锁
            isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!isLocked) {
                logger.error("获取分布式锁超时，实例详情ID：{}", instanceInfoId);
                throw new ContrastBusinessException("初始化计数器时获取锁超时");

            }
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);
            counter.set(initialValue);
            lock.unlock();
            isLocked =false;
            logger.info("成功初始化运行实例详情计数器，实例详情ID：{}，初始值：{}", instanceInfoId, initialValue);

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("初始化运行实例详情计数器时被中断，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("初始化计数器时被中断");
        } catch (Exception e) {
            logger.error("初始化运行实例详情计数器失败，实例详情ID：{}，初始值：{}", instanceInfoId, initialValue, e);
            throw new ContrastBusinessException("初始化计数器失败：" + e.getMessage());
        } finally {
            // 确保锁被释放，只检查锁是否为null和是否被当前线程持有
            if (lock != null && isLocked ) {
                lock.unlock();
            }
        }
    }

    /**
     * 运行实例计数器减值操作
     * 纯计数器操作，不包含业务逻辑
     * 
     * @param instanceId 实例ID
     * @return 减值后的计数器值，如果计数器不存在返回-1
     * @throws ContrastBusinessException 业务异常
     */
    public long decrementInstanceCounter(Long instanceId) throws ContrastBusinessException {
        if (instanceId == null) {
            throw new ContrastBusinessException("实例ID不能为空");
        }

        String counterKey = ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX + instanceId;
        String lockKey = ContrastConstants.RUN_INSTANCE_COUNTER_LOCK_PREFIX + instanceId;
        
        RLock lock = null;
        boolean isLocked = false;
        
        try {
            lock = redissonClient.getLock(lockKey);
            // 尝试获取分布式锁
            isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!isLocked) {
                logger.error("获取分布式锁超时，实例ID：{}", instanceId);
                throw new ContrastBusinessException("计数器减值时获取锁超时");
            }
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);

            // 检查计数器是否存在
            if (!counter.isExists()) {
                logger.warn("运行实例计数器不存在，实例ID：{}", instanceId);
                return -1;
            }

            // 执行减值操作
            long currentValue = counter.decrementAndGet();
            logger.info("运行实例计数器减值操作完成，实例ID：{}，当前值：{}", instanceId, currentValue);

            // 防止计数器变为负数
            if (currentValue < 0) {
                logger.warn("运行实例计数器值异常（小于0），重置为0，实例ID：{}，异常值：{}", instanceId, currentValue);
                counter.set(0);
                currentValue = 0;
            }
            lock.unlock();
            isLocked =false;
            return currentValue;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("运行实例计数器减值时被中断，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("计数器减值时被中断");
        } catch (Exception e) {
            logger.error("运行实例计数器减值失败，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("计数器减值失败：" + e.getMessage());
        } finally {
            // 确保锁被释放，只检查锁是否为null和是否被当前线程持有
//            if (lock != null && lock.isHeldByCurrentThread()) {
//                lock.unlock();
//            }
            if (lock != null && isLocked ) {
                lock.unlock();
            }
        }
    }

    /**
     * 运行实例详情计数器减值操作
     * 纯计数器操作，不包含业务逻辑
     * 
     * @param instanceInfoId 实例详情ID
     * @return 减值后的计数器值，如果计数器不存在返回-1
     * @throws ContrastBusinessException 业务异常
     */
    public long decrementInstanceInfoCounter(Long instanceInfoId) throws ContrastBusinessException {
        if (instanceInfoId == null) {
            throw new ContrastBusinessException("实例详情ID不能为空");
        }

        String counterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        String lockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        
        RLock lock = null;
        boolean isLocked = false;
        
        try {
            lock = redissonClient.getLock(lockKey);
            // 尝试获取分布式锁
            isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!isLocked) {
                logger.error("获取分布式锁超时，实例详情ID：{}", instanceInfoId);
                throw new ContrastBusinessException("计数器减值时获取锁超时");
            }
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);

            // 检查计数器是否存在
            if (!counter.isExists()) {
                logger.warn("运行实例详情计数器不存在，实例详情ID：{}", instanceInfoId);
                return -1;
            }

            // 执行减值操作
            long currentValue = counter.decrementAndGet();
            logger.info("运行实例详情计数器减值操作完成，实例详情ID：{}，当前值：{}", instanceInfoId, currentValue);

            // 防止计数器变为负数
            if (currentValue < 0) {
                logger.warn("运行实例详情计数器值异常（小于0），重置为0，实例详情ID：{}，异常值：{}", instanceInfoId, currentValue);
                counter.set(0);
                currentValue = 0;
            }
            lock.unlock();
            isLocked =false;
            return currentValue;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("运行实例详情计数器减值时被中断，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("计数器减值时被中断");
        } catch (Exception e) {
            logger.error("运行实例详情计数器减值失败，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("计数器减值失败：" + e.getMessage());
        } finally {
            // 确保锁被释放，只检查锁是否为null和是否被当前线程持有
            if (lock != null && isLocked ) {
                lock.unlock();
            }
        }
    }

    /**
     * 获取运行实例计数器当前值
     * 
     * @param instanceId 实例ID
     * @return 计数器当前值，如果计数器不存在返回-1
     * @throws ContrastBusinessException 业务异常
     */
    public long getInstanceCounterValue(Long instanceId) throws ContrastBusinessException {
        if (instanceId == null) {
            throw new ContrastBusinessException("实例ID不能为空");
        }

        try {
            String counterKey = ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX + instanceId;
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);
            
            if (!counter.isExists()) {
                logger.debug("运行实例计数器不存在，实例ID：{}", instanceId);
                return -1;
            }
            
            long value = counter.get();
            logger.debug("获取运行实例计数器值，实例ID：{}，当前值：{}", instanceId, value);
            return value;
        } catch (Exception e) {
            logger.error("获取运行实例计数器值失败，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("获取计数器值失败：" + e.getMessage());
        }
    }

    /**
     * 获取运行实例详情计数器当前值
     * 
     * @param instanceInfoId 实例详情ID
     * @return 计数器当前值，如果计数器不存在返回-1
     * @throws ContrastBusinessException 业务异常
     */
    public long getInstanceInfoCounterValue(Long instanceInfoId) throws ContrastBusinessException {
        if (instanceInfoId == null) {
            throw new ContrastBusinessException("实例详情ID不能为空");
        }

        try {
            String counterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);
            
            if (!counter.isExists()) {
                logger.debug("运行实例详情计数器不存在，实例详情ID：{}", instanceInfoId);
                return -1;
            }
            
            long value = counter.get();
            logger.debug("获取运行实例详情计数器值，实例详情ID：{}，当前值：{}", instanceInfoId, value);
            return value;
        } catch (Exception e) {
            logger.error("获取运行实例详情计数器值失败，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("获取计数器值失败：" + e.getMessage());
        }
    }

    /**
     * 清理运行实例计数器
     * 
     * @param instanceId 实例ID
     * @throws ContrastBusinessException 业务异常
     */
    public void clearInstanceCounter(Long instanceId) throws ContrastBusinessException {
        if (instanceId == null) {
            throw new ContrastBusinessException("实例ID不能为空");
        }

        String counterKey = ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX + instanceId;
        String lockKey = ContrastConstants.RUN_INSTANCE_COUNTER_LOCK_PREFIX + instanceId;
        
        RLock lock = null;
        boolean isLocked = false;
        
        try {
            lock = redissonClient.getLock(lockKey);
            // 尝试获取分布式锁
            isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!isLocked) {
                logger.error("获取分布式锁超时，实例ID：{}", instanceId);
                throw new ContrastBusinessException("清理计数器时获取锁超时");
            }
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);

            if (counter.isExists()) {
                counter.delete();
                logger.info("成功清理运行实例计数器，实例ID：{}", instanceId);
            } else {
                logger.debug("运行实例计数器不存在，无需清理，实例ID：{}", instanceId);
            }
            lock.unlock();
            isLocked =false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("清理运行实例计数器时被中断，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("清理计数器时被中断");
        } catch (Exception e) {
            logger.error("清理运行实例计数器失败，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("清理计数器失败：" + e.getMessage());
        } finally {
            // 确保锁被释放，只检查锁是否为null和是否被当前线程持有
            if (lock != null && isLocked ) {
                lock.unlock();
            }
        }
    }

    /**
     * 清理运行实例详情计数器
     * 
     * @param instanceInfoId 实例详情ID
     * @throws ContrastBusinessException 业务异常
     */
    public void clearInstanceInfoCounter(Long instanceInfoId) throws ContrastBusinessException {
        if (instanceInfoId == null) {
            throw new ContrastBusinessException("实例详情ID不能为空");
        }

        String counterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
        String lockKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTER_LOCK_PREFIX + instanceInfoId;
        
        RLock lock = null;
        boolean isLocked = false;
        
        try {
            lock = redissonClient.getLock(lockKey);
            // 尝试获取分布式锁
            isLocked = lock.tryLock(LOCK_WAIT_TIME, LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (!isLocked) {
                logger.error("获取分布式锁超时，实例详情ID：{}", instanceInfoId);
                throw new ContrastBusinessException("清理计数器时获取锁超时");
            }
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);

            if (counter.isExists()) {
                counter.delete();
                logger.info("成功清理运行实例详情计数器，实例详情ID：{}", instanceInfoId);
            } else {
                logger.debug("运行实例详情计数器不存在，无需清理，实例详情ID：{}", instanceInfoId);
            }
            lock.unlock();
            isLocked =false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("清理运行实例详情计数器时被中断，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("清理计数器时被中断");
        } catch (Exception e) {
            logger.error("清理运行实例详情计数器失败，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("清理计数器失败：" + e.getMessage());
        } finally {
            // 确保锁被释放，只检查锁是否为null和是否被当前线程持有
            if (lock != null && isLocked ) {
                lock.unlock();
            }
        }
    }

    /**
     * 检查运行实例计数器是否存在
     * 
     * @param instanceId 实例ID
     * @return 如果计数器存在返回true，否则返回false
     * @throws ContrastBusinessException 业务异常
     */
    public boolean isInstanceCounterExists(Long instanceId) throws ContrastBusinessException {
        if (instanceId == null) {
            throw new ContrastBusinessException("实例ID不能为空");
        }

        try {
            String counterKey = ContrastConstants.RUN_INSTANCE_COUNTOR_PREFIX + instanceId;
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);
            boolean exists = counter.isExists();
            logger.debug("检查运行实例计数器是否存在，实例ID：{}，存在：{}", instanceId, exists);
            return exists;
        } catch (Exception e) {
            logger.error("检查运行实例计数器是否存在失败，实例ID：{}", instanceId, e);
            throw new ContrastBusinessException("检查计数器是否存在失败：" + e.getMessage());
        }
    }

    /**
     * 检查运行实例详情计数器是否存在
     * 
     * @param instanceInfoId 实例详情ID
     * @return 如果计数器存在返回true，否则返回false
     * @throws ContrastBusinessException 业务异常
     */
    public boolean isInstanceInfoCounterExists(Long instanceInfoId) throws ContrastBusinessException {
        if (instanceInfoId == null) {
            throw new ContrastBusinessException("实例详情ID不能为空");
        }

        try {
            String counterKey = ContrastConstants.RUN_INSTANCE_INFO_COUNTOR_PREFIX + instanceInfoId;
            RAtomicLong counter = redissonClient.getAtomicLong(counterKey);
            boolean exists = counter.isExists();
            logger.debug("检查运行实例详情计数器是否存在，实例详情ID：{}，存在：{}", instanceInfoId, exists);
            return exists;
        } catch (Exception e) {
            logger.error("检查运行实例详情计数器是否存在失败，实例详情ID：{}", instanceInfoId, e);
            throw new ContrastBusinessException("检查计数器是否存在失败：" + e.getMessage());
        }
    }

    /**
     * 获取业务处理锁
     * 用于保护业务处理过程，防止重复处理
     * 注意：此方法仅负责获取锁对象，不负责获取锁和释放锁。调用者必须自行处理锁的获取和释放。
     * 
     * @param lockKey 锁键
     * @param timeoutSeconds 超时时间（秒）
     * @return 锁对象
     * @throws ContrastBusinessException 业务异常
     */
    public RLock getBusinessLock(String lockKey, long timeoutSeconds) throws ContrastBusinessException {
        if (StringUtils.isBlank(lockKey)) {
            throw new ContrastBusinessException("锁键不能为空");
        }

        RLock lock = null;
        try {
            lock = redissonClient.getLock(lockKey);
            return lock;
        } catch (Exception e) {
            logger.error("获取业务处理锁失败，lockKey: {}", lockKey, e);
            throw new ContrastBusinessException("获取业务处理锁失败：" + e.getMessage());
        }
    }

    /**
     * 释放业务处理锁
     * 
     * @param lockKey 锁键
     * @throws ContrastBusinessException 业务异常
     */
    public void releaseBusinessLock(String lockKey) throws ContrastBusinessException {
        if (StringUtils.isBlank(lockKey)) {
            throw new ContrastBusinessException("锁键不能为空");
        }

        try {
            RLock lock = redissonClient.getLock(lockKey);
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
                logger.debug("成功释放业务处理锁，lockKey: {}", lockKey);
            } else {
                logger.warn("当前线程不持有锁，无法释放，lockKey: {}", lockKey);
            }
        } catch (Exception e) {
            logger.error("释放业务处理锁失败，lockKey: {}", lockKey, e);
            throw new ContrastBusinessException("释放业务处理锁失败：" + e.getMessage());
        }
    }

    /**
     * 批量清理指定前缀的计数器
     * 
     * @param prefix 计数器前缀
     * @throws ContrastBusinessException 业务异常
     */
    public void clearCountersByPrefix(String prefix) throws ContrastBusinessException {
        if (StringUtils.isBlank(prefix)) {
            throw new ContrastBusinessException("计数器前缀不能为空");
        }

        try {
            // 获取匹配前缀的所有键
            Iterable<String> keysIterable = redissonClient.getKeys().getKeysByPattern(prefix + "*");
            
            // 将Iterable转换为Collection以便进行操作
            Collection<String> keys = new ArrayList<>();
            for (String key : keysIterable) {
                keys.add(key);
            }
            
            if (keys.isEmpty()) {
                logger.debug("未找到匹配前缀的计数器，前缀：{}", prefix);
                return;
            }
            
            // 批量删除键
            long deletedCount = redissonClient.getKeys().delete(keys.toArray(new String[0]));
            logger.info("批量清理计数器成功，前缀：{}，清理数量：{}", prefix, deletedCount);
        } catch (Exception e) {
            logger.error("批量清理计数器失败，前缀：{}，异常信息：{}", prefix, e.getMessage(), e);
            throw new ContrastBusinessException("批量清理计数器失败：" + e.getMessage());
        }
    }
} 