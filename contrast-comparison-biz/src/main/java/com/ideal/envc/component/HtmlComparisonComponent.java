package com.ideal.envc.component;

import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.model.dto.HtmlComparisonRequestDto;
import com.ideal.envc.model.dto.HtmlComparisonResultDto;
import com.ideal.envc.service.IHtmlComparisonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;

/**
 * HTML比对组件
 * 提供HTML比对内容解析和Excel导出功能的入口组件
 *
 * <AUTHOR>
 */
@Component
public class HtmlComparisonComponent {
    private static final Logger logger = LoggerFactory.getLogger(HtmlComparisonComponent.class);

    private final IHtmlComparisonService htmlComparisonService;

    public HtmlComparisonComponent(IHtmlComparisonService htmlComparisonService) {
        this.htmlComparisonService = htmlComparisonService;
    }

    /**
     * 解析HTML比对内容（通过flowId）
     *
     * @param flowId 流程ID
     * @return 比对结果
     * @throws ContrastBusinessException 业务异常
     */
    public HtmlComparisonResultDto parseHtmlComparison(Long flowId) throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容，flowId：{}", flowId);

        if (flowId == null) {
            throw new ContrastBusinessException("输入参数验证失败：flowId不能为空");
        }

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(flowId);

        return htmlComparisonService.parseHtmlComparison(request);
    }

    /**
     * 解析HTML比对内容（带服务器信息）
     *
     * @param flowId 流程ID
     * @param baselineServer 基线服务器名称（可选，会从流程详情中获取）
     * @param targetServer 目标服务器名称（可选，会从流程详情中获取）
     * @return 比对结果
     * @throws ContrastBusinessException 业务异常
     */
    public HtmlComparisonResultDto parseHtmlComparison(Long flowId, String baselineServer,
                                                     String targetServer) throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容，flowId：{}，基线服务器：{}，目标服务器：{}",
                   flowId, baselineServer, targetServer);

        if (flowId == null) {
            throw new ContrastBusinessException("输入参数验证失败：flowId不能为空");
        }

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(flowId);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);

        return htmlComparisonService.parseHtmlComparison(request);
    }

    /**
     * 解析HTML比对内容（带完整信息）
     *
     * @param flowId 流程ID
     * @param baselineServer 基线服务器名称（可选）
     * @param targetServer 目标服务器名称（可选）
     * @param description 比对描述（可选）
     * @return 比对结果
     * @throws ContrastBusinessException 业务异常
     */
    public HtmlComparisonResultDto parseHtmlComparison(Long flowId, String baselineServer,
                                                     String targetServer, String description) throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容，flowId：{}，基线服务器：{}，目标服务器：{}，描述：{}",
                   flowId, baselineServer, targetServer, description);

        if (flowId == null) {
            throw new ContrastBusinessException("输入参数验证失败：flowId不能为空");
        }

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(flowId);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);
        request.setDescription(description);

        return htmlComparisonService.parseHtmlComparison(request);
    }

    /**
     * 解析HTML比对内容并导出Excel
     *
     * @param request 比对请求参数
     * @param response HTTP响应对象
     * @throws ContrastBusinessException 业务异常
     */
    public void parseAndExport(HtmlComparisonRequestDto request, HttpServletResponse response) 
            throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容并导出Excel，基线服务器：{}，目标服务器：{}", 
                   request.getBaselineServer(), request.getTargetServer());

        if (!validateInput(request.getFlowId())) {
            throw new ContrastBusinessException("输入参数验证失败：flowId为空");
        }

        htmlComparisonService.exportHtmlComparisonResult(request, response);
    }

    /**
     * 解析HTML比对内容并导出Excel（简化版）
     *
     * @param flowId 流程ID
     * @param baselineServer 基线服务器名称（可选）
     * @param targetServer 目标服务器名称（可选）
     * @param response HTTP响应对象
     * @throws ContrastBusinessException 业务异常
     */
    public void parseAndExport(Long flowId, String baselineServer, String targetServer,
                             HttpServletResponse response) throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容并导出Excel，flowId：{}，基线服务器：{}，目标服务器：{}",
                   flowId, baselineServer, targetServer);

        if (flowId == null) {
            throw new ContrastBusinessException("输入参数验证失败：flowId不能为空");
        }

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(flowId);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);

        htmlComparisonService.exportHtmlComparisonResult(request, response);
    }

    /**
     * 解析HTML比对内容并导出Excel（带IP信息）
     *
     * @param flowId 流程ID
     * @param baselineServer 基线服务器名称（可选）
     * @param targetServer 目标服务器名称（可选）
     * @param baseServerIp 基线服务器IP（可选）
     * @param targetServerIp 目标服务器IP（可选）
     * @param response HTTP响应对象
     * @throws ContrastBusinessException 业务异常
     */
    public void parseAndExport(Long flowId, String baselineServer, String targetServer,
                             String baseServerIp, String targetServerIp, HttpServletResponse response)
            throws ContrastBusinessException {
        logger.info("开始解析HTML比对内容并导出Excel，flowId：{}，基线服务器：{}({}), 目标服务器：{}({})",
                   flowId, baselineServer, baseServerIp, targetServer, targetServerIp);

        if (flowId == null) {
            throw new ContrastBusinessException("输入参数验证失败：flowId不能为空");
        }

        HtmlComparisonRequestDto request = new HtmlComparisonRequestDto();
        request.setFlowId(flowId);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);
        request.setBaseServerIp(baseServerIp);
        request.setTargetServerIp(targetServerIp);

        htmlComparisonService.exportHtmlComparisonResult(request, response);
    }

    /**
     * 获取比对摘要信息
     *
     * @param result 比对结果
     * @return 摘要信息
     */
    public String getComparisonSummary(HtmlComparisonResultDto result) {
        if (result == null) {
            return "比对结果为空";
        }

        StringBuilder summary = new StringBuilder();
        summary.append("HTML比对完成！");
        summary.append("解析HTML行数：").append(result.getTotalHtmlRows()).append("行；");
        summary.append("基线文件：").append(result.getTotalSourceFiles()).append("个，");
        summary.append("目标文件：").append(result.getTotalTargetFiles()).append("个；");
        summary.append("一致：").append(result.getConsistentCount()).append("个（")
               .append(result.getConsistentRate()).append("%），");
        summary.append("不一致：").append(result.getInconsistentCount()).append("个（")
               .append(result.getInconsistentRate()).append("%），");
        summary.append("缺失：").append(result.getMissingCount()).append("个（")
               .append(result.getMissingRate()).append("%），");
        summary.append("多出：").append(result.getExtraCount()).append("个（")
               .append(result.getExtraRate()).append("%）");

        return summary.toString();
    }

    /**
     * 验证输入参数
     *
     * @param flowId 流程ID
     * @return 是否有效
     */
    public boolean validateInput(Long flowId) {
        if (flowId == null) {
            logger.warn("flowId为空");
            return false;
        }

        return true;
    }

    /**
     * 获取比对建议
     *
     * @param result 比对结果
     * @return 建议信息
     */
    public String getComparisonAdvice(HtmlComparisonResultDto result) {
        if (result == null) {
            return "无法提供建议：比对结果为空";
        }

        int totalFiles = result.getTotalSourceFiles();
        int inconsistentCount = result.getInconsistentCount();
        int missingCount = result.getMissingCount();
        
        if (totalFiles == 0) {
            return "无文件需要比对";
        }

        double inconsistentRate = inconsistentCount * 100.0 / totalFiles;
        double missingRate = missingCount * 100.0 / totalFiles;
        double totalDiffRate = inconsistentRate + missingRate;

        if (totalDiffRate > 50) {
            return "🚨 差异率超过50%，建议全面检查环境配置和部署流程";
        } else if (totalDiffRate > 20) {
            return "⚠️ 差异率在20-50%之间，建议重点关注关键文件和配置";
        } else if (totalDiffRate > 5) {
            return "✅ 差异率在5-20%之间，环境基本一致，建议检查少量差异文件";
        } else {
            return "✅ 差异率低于5%，环境高度一致，可以放心使用";
        }
    }
}
