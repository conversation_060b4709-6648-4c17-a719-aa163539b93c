package com.ideal.envc.component;

import com.ideal.envc.model.enums.FileComparisonStrategy;
import com.ideal.envc.exception.ContrastBusinessException;
import com.ideal.envc.mapper.SystemComputerMapper;
import com.ideal.envc.model.dto.FileComparisonRequestDto;
import com.ideal.envc.model.dto.FileComparisonResultDto;
import com.ideal.envc.model.entity.SystemComputerEntity;
import com.ideal.envc.service.IFileComparisonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

/**
 * 文件比较组件
 * 提供文件内容比较和汇总功能的入口组件
 *
 * <AUTHOR>
 */
@Component
public class FileComparisonComponent {
    private static final Logger logger = LoggerFactory.getLogger(FileComparisonComponent.class);

    private final IFileComparisonService fileComparisonService;
    private final SystemComputerMapper systemComputerMapper;

    public FileComparisonComponent(IFileComparisonService fileComparisonService, SystemComputerMapper systemComputerMapper) {
        this.fileComparisonService = fileComparisonService;
        this.systemComputerMapper = systemComputerMapper;
    }

    /**
     * 比较文件内容
     * 以sourceContent为基线，分析targetContent与sourceContent相比的差异
     *
     * @param sourceContent 源内容字符串（基线）
     * @param targetContent 目标内容字符串
     * @return 比较结果
     */
    public FileComparisonResultDto compareFiles(String sourceContent, String targetContent) throws ContrastBusinessException {
        logger.info("开始比较文件内容");

        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(sourceContent);
        request.setTargetContent(targetContent);

        return fileComparisonService.compareFileContents(request);
    }

    /**
     * 比较文件内容（带服务器信息）
     *
     * @param sourceContent 源内容字符串（基线）
     * @param targetContent 目标内容字符串
     * @param baselineServer 基线服务器名称
     * @param targetServer 目标服务器名称
     * @return 比较结果
     */
    public FileComparisonResultDto compareFiles(String sourceContent, String targetContent,
                                               String baselineServer, String targetServer) throws ContrastBusinessException {
        logger.info("开始比较文件内容，基线服务器：{}，目标服务器：{}", baselineServer, targetServer);

        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(sourceContent);
        request.setTargetContent(targetContent);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);

        return fileComparisonService.compareFileContents(request);
    }

    /**
     * 比较文件内容（完整参数）
     *
     * @param sourceContent 源内容字符串（基线）
     * @param targetContent 目标内容字符串
     * @param baselineServer 基线服务器名称
     * @param targetServer 目标服务器名称
     * @param description 比较描述
     * @return 比较结果
     */
    public FileComparisonResultDto compareFiles(String sourceContent, String targetContent,
                                               String baselineServer, String targetServer, String description) throws ContrastBusinessException {
        logger.info("开始比较文件内容，基线服务器：{}，目标服务器：{}，描述：{}",
                baselineServer, targetServer, description);

        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(sourceContent);
        request.setTargetContent(targetContent);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);
        request.setDescription(description);

        return fileComparisonService.compareFileContents(request);
    }


    /**
     * 比较文件内容并导出Excel（带服务器信息）
     *
     * @param response HTTP响应对象
     */
    public void compareAndExport(FileComparisonRequestDto request, HttpServletResponse response) throws ContrastBusinessException {
        fileComparisonService.exportComparisonResult(request, response);
    }




    /**
     * 比较文件内容（支持比较策略）
     *
     * @param sourceContent 源内容字符串（基线）
     * @param targetContent 目标内容字符串
     * @param comparisonStrategy 比较策略
     * @return 比较结果
     */
    public FileComparisonResultDto compareFiles(String sourceContent, String targetContent,
                                               FileComparisonStrategy comparisonStrategy) throws ContrastBusinessException {
        logger.info("开始比较文件内容，比较策略：{}", comparisonStrategy);

        if (!validateInput(sourceContent, targetContent)) {
            throw new ContrastBusinessException("输入参数验证失败：源内容或目标内容为空");
        }

        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(sourceContent);
        request.setTargetContent(targetContent);
        request.setComparisonStrategy(comparisonStrategy);

        return fileComparisonService.compareFileContents(request);
    }

    /**
     * 比较文件内容（带服务器信息和比较策略）
     *
     * @param sourceContent 源内容字符串（基线）
     * @param targetContent 目标内容字符串
     * @param baselineServer 基线服务器名称
     * @param targetServer 目标服务器名称
     * @param comparisonStrategy 比较策略
     * @return 比较结果
     */
    public FileComparisonResultDto compareFiles(String sourceContent, String targetContent,
                                               String baselineServer, String targetServer,
                                               FileComparisonStrategy comparisonStrategy) throws ContrastBusinessException {
        logger.info("开始比较文件内容，基线服务器：{}，目标服务器：{}，比较策略：{}",
                   baselineServer, targetServer, comparisonStrategy);

        if (!validateInput(sourceContent, targetContent)) {
            throw new ContrastBusinessException("输入参数验证失败：源内容或目标内容为空");
        }

        FileComparisonRequestDto request = new FileComparisonRequestDto();
        request.setSourceContent(sourceContent);
        request.setTargetContent(targetContent);
        request.setBaselineServer(baselineServer);
        request.setTargetServer(targetServer);
        request.setComparisonStrategy(comparisonStrategy);

        return fileComparisonService.compareFileContents(request);
    }



    /**
     * 验证输入参数
     *
     * @param sourceContent 源内容
     * @param targetContent 目标内容
     * @return 是否有效
     */
    public boolean validateInput(String sourceContent, String targetContent) {
        if (StringUtils.isBlank(sourceContent)) {
            logger.warn("源内容为空");
            return false;
        }
        
        if (StringUtils.isBlank(targetContent)) {
            logger.warn("目标内容为空");
            return false;
        }
        
        return true;
    }

    /**
     * 获取比较统计摘要
     *
     * @param result 比较结果
     * @return 统计摘要字符串
     */
    public String getComparisonSummary(FileComparisonResultDto result) {
        if (result == null) {
            return "比较结果为空";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("文件比较结果摘要：");
        summary.append("基线文件总数：").append(result.getTotalSourceFiles()).append("，");
        summary.append("目标文件总数：").append(result.getTotalTargetFiles()).append("，");
        summary.append("一致文件：").append(result.getConsistentCount()).append("个，");
        summary.append("不一致文件：").append(result.getInconsistentCount()).append("个，");
        summary.append("缺失文件：").append(result.getMissingCount()).append("个，");
        summary.append("多出文件：").append(result.getExtraCount()).append("个");
        
        if (result.getConsistentRate() != null) {
            summary.append("，一致率：").append(result.getConsistentRate()).append("%");
        }
        
        return summary.toString();
    }

    /**
     * 根据IP地址查询主机名
     *
     * @param computerIp 设备IP地址
     * @return 主机名，如果查询不到则返回IP地址
     */
    private String getHostnameByIp(String computerIp) {
        if (StringUtils.isBlank(computerIp)) {
            logger.warn("IP地址为空，无法查询主机名");
            return computerIp;
        }

        try {
            String hostname = systemComputerMapper.selectComputerNameByIp(computerIp);
            if (StringUtils.isNotBlank(hostname)) {
                logger.debug("根据IP {} 查询到主机名：{}", computerIp, hostname);
                return hostname;
            } else {
                logger.warn("根据IP {} 未查询到主机名，使用IP地址作为主机名", computerIp);
                return computerIp;
            }
        } catch (Exception e) {
            logger.error("根据IP {} 查询主机名时发生异常：{}", computerIp, e.getMessage(), e);
            return computerIp;
        }
    }

    /**
     * 批量根据IP地址查询主机名
     *
     * @param computerIps IP地址列表
     * @return IP地址和主机名的映射关系，key为IP地址，value为主机名
     */
    private Map<String, String> getHostnameMapByIps(List<String> computerIps) {
        Map<String, String> hostnameMap = new HashMap<>();

        if (computerIps == null || computerIps.isEmpty()) {
            logger.warn("IP地址列表为空，无法批量查询主机名");
            return hostnameMap;
        }

        try {
            Map<String, SystemComputerEntity> computerMap = systemComputerMapper.selectComputerNameMapByIps(computerIps);

            for (String ip : computerIps) {
                SystemComputerEntity computer = computerMap.get(ip);
                if (computer != null && StringUtils.isNotBlank(computer.getComputerName())) {
                    hostnameMap.put(ip, computer.getComputerName());
                    logger.debug("根据IP {} 查询到主机名：{}", ip, computer.getComputerName());
                } else {
                    hostnameMap.put(ip, ip);
                    logger.warn("根据IP {} 未查询到主机名，使用IP地址作为主机名", ip);
                }
            }
        } catch (Exception e) {
            logger.error("批量查询主机名时发生异常：{}", e.getMessage(), e);
            // 异常情况下，使用IP地址作为主机名
            for (String ip : computerIps) {
                hostnameMap.put(ip, ip);
            }
        }

        return hostnameMap;
    }
}
