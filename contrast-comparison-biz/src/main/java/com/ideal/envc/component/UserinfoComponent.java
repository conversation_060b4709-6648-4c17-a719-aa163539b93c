package com.ideal.envc.component;

import com.ideal.common.util.BeanUtils;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.system.common.component.config.AuthContext;
import com.ideal.system.common.component.model.CurrentUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 登录用户信息处理类，仅支持controller层调用
 * <AUTHOR> lch
 */
@Component
public class UserinfoComponent {
    private static final Logger logger = LoggerFactory.getLogger(UserinfoComponent.class);

    public UserDto getUser(){
        CurrentUser userApiDto = AuthContext.getUser();
        if (userApiDto == null) {
            throw new IllegalStateException("用户未登录");
        }
        UserDto userDto = BeanUtils.copy(userApiDto,UserDto.class);
        logger.info("登录用户信息:{}",userDto.toString());
        return userDto;
    }

}
