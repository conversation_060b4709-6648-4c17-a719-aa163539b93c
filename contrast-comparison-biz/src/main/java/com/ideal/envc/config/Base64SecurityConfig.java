package com.ideal.envc.config;

/**
 * Base64解码安全配置类
 * 
 * <AUTHOR>
 */
public class Base64SecurityConfig {

    /**
     * Base64输入最大长度限制 (30MB)
     */
    public static final int MAX_BASE64_INPUT_LENGTH = 30 * 1024 * 1024;

    /**
     * 解码后内容最大大小限制 (24MB)
     */
    public static final int MAX_DECODED_CONTENT_SIZE = 24 * 1024 * 1024;

    /**
     * 是否启用Base64格式预验证
     */
    public static final boolean ENABLE_FORMAT_VALIDATION = true;

    /**
     * 是否启用详细的安全日志记录
     */
    public static final boolean ENABLE_SECURITY_LOGGING = true;

    /**
     * Base64解码超时时间 (毫秒)
     */
    public static final long DECODE_TIMEOUT_MS = 5000;

    /**
     * 获取Base64输入长度限制
     *
     * @return 最大输入长度
     */
    public static int getMaxInputLength() {
        // 可以从配置文件或环境变量中读取
        String configValue = System.getProperty("base64.max.input.length");
        if (configValue != null) {
            try {
                return Integer.parseInt(configValue);
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        return MAX_BASE64_INPUT_LENGTH;
    }

    /**
     * 获取解码后内容大小限制
     *
     * @return 最大解码后内容大小
     */
    public static int getMaxDecodedSize() {
        // 可以从配置文件或环境变量中读取
        String configValue = System.getProperty("base64.max.decoded.size");
        if (configValue != null) {
            try {
                return Integer.parseInt(configValue);
            } catch (NumberFormatException e) {
                // 使用默认值
            }
        }
        return MAX_DECODED_CONTENT_SIZE;
    }

}
