package com.ideal.envc.xxjob;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.model.dto.ContrastScheduleJobTaskDto;
import com.ideal.envc.model.dto.StartResult;
import com.ideal.envc.model.dto.TaskLevelStartDto;
import com.ideal.envc.model.dto.UserDto;
import com.ideal.envc.model.enums.StartFromEnums;
import com.ideal.envc.service.IStartContrastService;
import com.xxl.job.core.context.XxlJobContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * 定时类任务触发入口
 * <AUTHOR>
 */
@Configuration
public class ScheduleJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(ScheduleJobHandler.class);
    private final IStartContrastService startContrastService;

    public ScheduleJobHandler(IStartContrastService startContrastService) {
        this.startContrastService = startContrastService;
    }

    /**
     * 任务 定时执行
     * <AUTHOR>
     */
    @XxlJob("contrastJobHandler")
    public void contrastJobHandler() {
        //获取参数
        String param = XxlJobHelper.getJobParam();
        logger.info("receive schedule job have to execute ,task has params : {}",param);
        XxlJobHelper.log("receive schedule job have to execute ,task has params : {}",param);
        if(StringUtils.isBlank(param)){
            logger.error("receive schedule job have to execute ,task has params is null");
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL,"params is null");
            return;
        }

        ContrastScheduleJobTaskDto contrastScheduleJobTaskDto;

        //验证返回执行参数是否符合规范要求
        try{
            contrastScheduleJobTaskDto = JSON.parseObject(param, ContrastScheduleJobTaskDto.class);
        }catch (Exception e){
            logger.error("receive schedule job have to execute ,task has params is illegal");
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL,"params is illegal");
            return;
        }

        if(contrastScheduleJobTaskDto == null){
            logger.error("receive schedule job have to execute ,task has execute params is empty");
            XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL,"task has execute params is empty");
            return;
        }
        //封装上定时服务对应任务的主键ID
        contrastScheduleJobTaskDto.setScheduleJobId(XxlJobHelper.getJobId());

        try{
            //调用待执行任务启动接口，进行任务启动.此处编写调用service启动方法的逻辑
            UserDto userDto = new UserDto();
            userDto.setId(contrastScheduleJobTaskDto.getCreatorId());
            userDto.setFullName(contrastScheduleJobTaskDto.getCreateName());

            TaskLevelStartDto taskLevelStartDto = new TaskLevelStartDto();
            taskLevelStartDto.setFrom(StartFromEnums.PERIOD_TRIGGER.getCode());
            List<Long> taskIds = new ArrayList<>();
            taskIds.add(contrastScheduleJobTaskDto.getTaskId());
            taskLevelStartDto.setTaskIds(taskIds);
            taskLevelStartDto.setUserId(contrastScheduleJobTaskDto.getCreatorId());
            taskLevelStartDto.setUserName(contrastScheduleJobTaskDto.getCreateName());

            StartResult startResult = startContrastService.startByTasks(taskLevelStartDto,userDto);
            boolean success = startResult.isSuccess();
            if(!success){
                logger.error("receive schedule job have to execute ,taskId:{} execution failure",contrastScheduleJobTaskDto.getTaskId());
                XxlJobHelper.handleResult(XxlJobContext.HANDLE_CODE_FAIL,"TaskEntity execution failure");
                return;
            }
            logger.info("receive schedule job have to execute,taskId:{} execution success",contrastScheduleJobTaskDto.getTaskId());
            //返回给调度中心执行结果正常，执行备注信息
            XxlJobHelper.handleSuccess("task start is success!");

        }catch (Exception e){
            logger.error("receive schedule job have to execute ,taskId:{} execute is exception",contrastScheduleJobTaskDto.getTaskId(),e);
            //返回给调度中心执行结果异常，执行备注信息
            XxlJobHelper.handleFail(e.getMessage());
        }
    }
}

