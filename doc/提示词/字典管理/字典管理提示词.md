# 字典管理模块提示词文档

本文档提供了与字典管理模块相关的提示词，用于辅助开发人员与AI助手进行交互，更高效地完成字典管理相关功能的开发和维护工作。

## 1. 基础概念提示词

### 1.1 字典码表

```
字典码表(ieai_envc_dictionary)是系统中用于存储字典类型定义的基础表，包含字典码、字典描述等信息。每个字典码对应一类字典数据，如"性别"、"状态"等。字典码表由DictionaryController、IDictionaryService和DictionaryMapper等组件提供管理功能。
```

### 1.2 字典详情表

```
字典详情表(ieai_envc_dictionary_detail)是系统中用于存储具体字典项的表，与字典码表形成父子关系。每条记录包含显示名称、显示值、排序序号等信息。字典详情表由DictionaryDetailController、IDictionaryDetailService和DictionaryDetailMapper等组件提供管理功能。
```

### 1.3 字典数据结构

```
字典管理模块的核心数据结构包括：
1. DictionaryEntity/DictionaryDto：字典码实体类及DTO
2. DictionaryDetailEntity/DictionaryDetailDto：字典详情实体类及DTO
3. DictionaryQueryDto：字典码查询条件DTO
4. DictionaryDetailQueryDto：字典详情查询条件DTO
这些类定义了字典数据的结构和传输格式。
```

## 2. 功能开发提示词

### 2.1 验证字典码是否存在

```
实现验证字典码是否存在功能，需要：
1. 在DictionaryController中使用validateDictionaryCode方法接收验证请求
2. 调用IDictionaryService.validateDictionaryCode方法验证字典码是否存在
3. 在DictionaryServiceImpl中实现验证逻辑，调用DictionaryMapper.selectDictionaryList方法
4. 根据查询结果判断字典码是否存在
5. 返回布尔值给前端
```

### 2.2 查询字典码列表

```
实现字典码列表查询功能，需要：
1. 在DictionaryController中使用list方法接收查询请求
2. 调用IDictionaryService.selectDictionaryList方法获取分页数据
3. 在DictionaryServiceImpl中实现查询逻辑，调用DictionaryMapper.selectDictionaryList方法
4. 使用PageHelper进行分页处理
5. 将Entity转换为DTO返回给前端
```

### 2.2 新增字典码

```
实现新增字典码功能，需要：
1. 在DictionaryController中使用save方法接收新增请求
2. 调用IDictionaryService.insertDictionary方法保存数据
3. 在DictionaryServiceImpl中实现保存逻辑，将DTO转换为Entity
4. 调用DictionaryMapper.insertDictionary方法插入数据
5. 返回操作结果
```

### 2.3 修改字典码

```
实现修改字典码功能，需要：
1. 在DictionaryController中使用update方法接收修改请求
2. 调用IDictionaryService.updateDictionary方法更新数据
3. 在DictionaryServiceImpl中实现更新逻辑，将DTO转换为Entity
4. 调用DictionaryMapper.updateDictionary方法更新数据
5. 返回操作结果
```

### 2.4 删除字典码

```
实现删除字典码功能，需要：
1. 在DictionaryController中使用remove方法接收删除请求
2. 调用IDictionaryService.deleteDictionaryByIds方法删除数据
3. 在DictionaryServiceImpl中实现删除逻辑
4. 调用DictionaryMapper.deleteDictionaryByIds方法删除数据
5. 返回操作结果
```

### 2.5 查询字典详情列表

```
实现字典详情列表查询功能，需要：
1. 在DictionaryDetailController中使用list方法接收查询请求
2. 调用IDictionaryDetailService.selectDictionaryDetailList方法获取分页数据
3. 在DictionaryDetailServiceImpl中实现查询逻辑，调用DictionaryDetailMapper.selectDictionaryDetailList方法
4. 使用PageHelper进行分页处理
5. 将Entity转换为DTO返回给前端
```

### 2.6 新增字典详情

```
实现新增字典详情功能，需要：
1. 在DictionaryDetailController中使用save方法接收新增请求
2. 调用IDictionaryDetailService.insertDictionaryDetail方法保存数据
3. 在DictionaryDetailServiceImpl中实现保存逻辑，将DTO转换为Entity
4. 调用DictionaryDetailMapper.insertDictionaryDetail方法插入数据
5. 返回操作结果
```

### 2.7 修改字典详情

```
实现修改字典详情功能，需要：
1. 在DictionaryDetailController中使用update方法接收修改请求
2. 调用IDictionaryDetailService.updateDictionaryDetail方法更新数据
3. 在DictionaryDetailServiceImpl中实现更新逻辑，将DTO转换为Entity
4. 调用DictionaryDetailMapper.updateDictionaryDetail方法更新数据
5. 返回操作结果
```

### 2.8 删除字典详情

```
实现删除字典详情功能，需要：
1. 在DictionaryDetailController中使用remove方法接收删除请求
2. 调用IDictionaryDetailService.deleteDictionaryDetailByIds方法删除数据
3. 在DictionaryDetailServiceImpl中实现删除逻辑
4. 调用DictionaryDetailMapper.deleteDictionaryDetailByIds方法删除数据
5. 返回操作结果
```

### 2.9 根据字典码获取字典详情列表

```
实现根据字典码获取字典详情列表功能，需要：
1. 在DictionaryDetailController中使用findDictionaryDeatailList方法接收查询请求
2. 调用IDictionaryDetailService.findDictionaryDetailListByCode方法获取字典详情列表
3. 在DictionaryDetailServiceImpl中实现查询逻辑，调用DictionaryDetailMapper.selectDictionaryDetailListByCode方法
4. 将Entity列表转换为DTO列表
5. 返回字典详情列表给前端
```

## 3. 功能扩展提示词

### 3.1 字典缓存实现

```
为提高字典数据访问效率，可以实现字典缓存功能：
1. 使用Spring Cache或Redis实现缓存机制
2. 在Service层添加缓存注解，如@Cacheable、@CacheEvict等
3. 在字典数据变更时及时更新缓存
4. 提供缓存刷新接口，用于手动刷新缓存
```

### 3.2 字典数据导入导出

```
实现字典数据导入导出功能，需要：
1. 添加导出接口，支持Excel、CSV等格式
2. 使用POI或EasyExcel等工具处理Excel文件
3. 添加导入接口，支持批量导入字典数据
4. 实现数据验证和错误处理机制
```

### 3.3 字典关联查询

```
实现字典码与字典详情的关联查询功能，需要：
1. 添加关联查询接口，根据字典码查询对应的字典详情
2. 在Service层实现关联查询逻辑
3. 使用JOIN查询或分步查询获取关联数据
4. 构建包含字典详情列表的字典码DTO返回给前端
```

### 3.4 字典值类型支持

```
完善字典值类型支持，需要：
1. 定义支持的字典值类型常量，如STRING、INTEGER、DOUBLE等
2. 在字典详情中添加值类型字段
3. 实现值类型转换逻辑，根据类型将字符串值转换为对应类型
4. 提供类型转换工具类，便于前端使用
```

## 4. 问题排查提示词

### 4.1 字典查询问题

```
排查字典查询问题，可以检查：
1. 查询条件是否正确，特别是字符串比较是否使用了模糊匹配
2. SQL语句是否正确，可以通过日志或调试查看实际执行的SQL
3. 分页参数是否正确，包括页码和每页记录数
4. 返回结果是否按预期格式封装
```

### 4.2 字典保存问题

```
排查字典保存问题，可以检查：
1. 请求参数是否完整，必填字段是否有值
2. 数据转换是否正确，DTO到Entity的转换过程
3. SQL执行是否成功，检查日志中的错误信息
4. 数据库约束是否满足，如唯一索引等
```

### 4.3 字典删除问题

```
排查字典删除问题，可以检查：
1. 删除参数是否正确，ID数组是否有效
2. 是否存在外键约束，导致删除失败
3. 是否使用了软删除机制，实际是更新删除标志而非物理删除
4. 删除操作是否有权限控制，导致无权限删除
```

## 5. 代码示例提示词

### 5.1 验证字典码是否存在示例

```java
// Controller层
@GetMapping("/validateDictionaryCode")
public R<Boolean> validateDictionaryCode(@RequestParam(value = "code") String code) {
    logger.info("验证字典码是否存在: {}", code);
    Boolean exists = dictionaryService.validateDictionaryCode(code);
    return R.ok(exists);
}

// Service层
@Override
public Boolean validateDictionaryCode(String code) {
    logger.info("验证字典码是否存在: {}", code);
    if (code == null || code.trim().isEmpty()) {
        return false;
    }

    DictionaryEntity query = new DictionaryEntity();
    query.setCode(code);
    List<DictionaryEntity> list = dictionaryMapper.selectDictionaryList(query);

    return list != null && !list.isEmpty();
}
```

### 5.2 字典码查询示例

```java
// Controller层
@GetMapping("/list")
public R<PageInfo<DictionaryDto>> list(TableQueryDto<DictionaryQueryDto> tableQueryDto) {
    PageInfo<DictionaryDto> list = dictionaryService.selectDictionaryList(
            tableQueryDto.getQueryParam(),
            tableQueryDto.getPageNum(),
            tableQueryDto.getPageSize()
    );
    return R.ok(list);
}

// Service层
@Override
public PageInfo<DictionaryDto> selectDictionaryList(DictionaryQueryDto dictionaryQueryDto, Integer pageNum, Integer pageSize) {
    DictionaryEntity query = BeanUtils.copy(dictionaryQueryDto, DictionaryEntity.class);
    PageMethod.startPage(pageNum, pageSize);
    List<DictionaryEntity> dictionaryList = dictionaryMapper.selectDictionaryList(query);
    return PageDataUtil.toDtoPage(dictionaryList, DictionaryDto.class);
}
```

### 5.2 字典详情保存示例

```java
// Controller层
@PostMapping("/save")
public R<Void> save(@RequestBody DictionaryDetailDto dictionaryDetailDto) {
    if (dictionaryDetailService.insertDictionaryDetail(dictionaryDetailDto) > 0) {
        return R.ok();
    }
    return R.fail();
}

// Service层
@Override
public int insertDictionaryDetail(DictionaryDetailDto dictionaryDetailDto) {
    DictionaryDetailEntity dictionaryDetail = BeanUtils.copy(dictionaryDetailDto, DictionaryDetailEntity.class);
    return dictionaryDetailMapper.insertDictionaryDetail(dictionaryDetail);
}
```

### 5.3 字典码删除示例

```java
// Controller层
@PostMapping("/remove")
public R<Void> remove(@RequestBody Long[] ids) {
    dictionaryService.deleteDictionaryByIds(ids);
    return R.ok();
}

// Service层
@Override
public int deleteDictionaryByIds(Long[] ids) {
    return dictionaryMapper.deleteDictionaryByIds(ids);
}
```

### 5.4 根据字典码获取字典详情列表示例

```java
// Controller层
@GetMapping("/findDictionaryDeatailList")
public R<List<DictionaryDetailDto>> findDictionaryDeatailList(@RequestParam(value = "code") String code) {
    logger.info("根据字典码获取字典详情列表: {}", code);
    List<DictionaryDetailDto> list = dictionaryDetailService.findDictionaryDetailListByCode(code);
    return R.ok(list);
}

// Service层
@Override
public List<DictionaryDetailDto> findDictionaryDetailListByCode(String code) {
    logger.info("根据字典码获取字典详情列表: {}", code);
    if (code == null || code.trim().isEmpty()) {
        return java.util.Collections.emptyList();
    }

    // 调用Mapper方法查询数据
    List<DictionaryDetailEntity> entityList = dictionaryDetailMapper.selectDictionaryDetailListByCode(code);

    // 将Entity列表转换为DTO列表
    return BeanUtils.copyList(entityList, DictionaryDetailDto.class);
}

// Mapper XML
<select id="selectDictionaryDetailListByCode" parameterType="String" resultMap="DictionaryDetailResult">
    <include refid="selectDictionaryDetail"/>
    where icode = #{code}
    and ideleted = 0
    order by isort asc
</select>
```

## 6. 接口测试提示词

### 6.1 验证字典码是否存在测试

```
测试验证字典码是否存在接口：
1. 使用GET方法访问/dictionary/validateDictionaryCode接口
2. 传入参数：code=test_code
3. 检查返回结果是否包含布尔值
4. 验证返回的布尔值是否符合预期（存在返回true，不存在返回false）
```

### 6.2 字典码列表查询测试

```
测试字典码列表查询接口：
1. 使用GET方法访问/dictionary/list接口
2. 传入查询参数：pageNum=1&pageSize=10&queryParam.code=test
3. 检查返回结果是否包含分页信息和数据列表
4. 验证查询条件是否生效，返回的数据是否符合条件
```

### 6.2 字典详情保存测试

```
测试字典详情保存接口：
1. 使用POST方法访问/envc/detail/save接口
2. 传入JSON格式的请求体，包含必要字段：
   {
     "envcDictionaryId": 1,
     "code": "test",
     "lable": "测试",
     "value": "1",
     "sort": 1
   }
3. 检查返回结果是否成功
4. 查询数据库验证数据是否正确保存
```

### 6.3 字典码删除测试

```
测试字典码删除接口：
1. 使用POST方法访问/dictionary/remove接口
2. 传入JSON格式的请求体：[1, 2, 3]
3. 检查返回结果是否成功
4. 查询数据库验证数据是否已删除
```

### 6.4 根据字典码获取字典详情列表测试

```
测试根据字典码获取字典详情列表接口：
1. 使用GET方法访问/dictionary/detail/findDictionaryDeatailList接口
2. 传入参数：code=test_code
3. 检查返回结果是否包含字典详情列表
4. 验证返回的列表是否符合预期，包含正确的字典详情数据
```

## 7. 性能优化提示词

### 7.1 查询优化

```
优化字典查询性能：
1. 使用索引提高查询效率，特别是对code字段的查询
2. 合理设置分页参数，避免一次查询过多数据
3. 使用缓存减少数据库访问，特别是对频繁访问的字典数据
4. 优化SQL语句，避免不必要的JOIN和子查询
```

### 7.2 批量操作优化

```
优化字典批量操作性能：
1. 使用批量插入替代循环单条插入
2. 使用批量更新替代循环单条更新
3. 使用事务保证批量操作的原子性
4. 合理控制批量操作的数据量，避免单次处理过多数据
```

## 8. 安全性提示词

### 8.1 输入验证

```
实现字典数据输入验证：
1. 使用Bean Validation进行参数验证
2. 对特殊字符进行过滤或转义，防止XSS攻击
3. 使用参数绑定方式执行SQL，防止SQL注入
4. 验证字典码唯一性，避免重复数据
```

### 8.2 权限控制

```
实现字典管理权限控制：
1. 使用Spring Security或自定义权限框架进行认证和授权
2. 对敏感操作（如删除）进行权限检查
3. 记录操作日志，便于审计和追踪
4. 实现数据级权限控制，限制用户只能访问授权的字典数据
```

## 9. 版本变更提示词

### 9.1 版本1.0.0

```
字典管理模块版本1.0.0包含以下功能：
1. 字典码的增删改查
2. 字典详情的增删改查
3. 基本的分页查询
4. 软删除机制
```

### 9.2 版本1.1.0

```
字典管理模块版本1.1.0包含以下新功能：
1. 验证字典码是否存在接口
2. 根据字典码获取字典详情列表接口
3. 接口路径和请求方式的标准化
```

### 9.3 版本1.2.0（计划）

```
字典管理模块版本1.2.0计划包含以下新功能：
1. 字典数据缓存机制
2. 字典数据导入导出
3. 字典关联查询
4. 字典值类型支持增强
```
