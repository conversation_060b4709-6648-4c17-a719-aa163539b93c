# 响应码及描述

本文件用于记录本平台所有的响应码及对应描述，响应码和描述均为字符串类型。所有响应码以 `13` 开头进行分类，操作成功码为 `10000`。

---

## 目录
- [操作成功](#操作成功)
- [查询类异常码 1301xx](#查询类异常码-1301xx)
- [新增类异常码 1302xx](#新增类异常码-1302xx)
- [修改类异常码 1303xx](#修改类异常码-1303xx)
- [删除类异常码 1304xx](#删除类异常码-1304xx)
- [操作类异常码 1305xx](#操作类异常码-1305xx)
- [比对解析相关异常码 1306xx](#比对解析相关异常码-1306xx)
- [导出相关异常码 1307xx](#导出相关异常码-1307xx)
- [系统级别异常码 1399xx](#系统级别异常码-1399xx)

---

## 操作成功
| 响应码 | 描述         |
|--------|--------------|
| 10000  | 操作成功     |

---

## 查询类异常码 1301xx
| 响应码   | 描述           |
|----------|----------------|
| 130100   | 查询失败       |
| 130101   | 数据不存在     |
| 130102   | 查询参数错误   |
| 130103   | 查询超时       |
| 130104   | 查询权限不足   |

---

## 新增类异常码 1302xx
| 响应码   | 描述           |
|----------|----------------|
| 130200   | 新增失败       |
| 130201   | 数据已存在     |
| 130202   | 新增参数错误   |
| 130203   | 新增权限不足   |
| 130204   | 批量新增失败   |
| 130205   | 验证错误       |
| 130210   | id不能为空     |
| 130211   | 用户id不能为空 |
| 130212   | 设备编号不能为空 |
| 130213   | 系统名称不能为空 |
| 130214   | 参数格式不正确 |
| 130215   | 字段长度超限   |
| 130216   | 必填字段缺失   |

---

## 修改类异常码 1303xx
| 响应码   | 描述           |
|----------|----------------|
| 130300   | 修改失败       |
| 130301   | 数据不存在     |
| 130302   | 修改参数错误   |
| 130303   | 修改权限不足   |
| 130304   | 批量修改失败   |

---

## 删除类异常码 1304xx
| 响应码   | 描述           |
|----------|----------------|
| 130400   | 删除失败       |
| 130401   | 数据不存在     |
| 130402   | 删除参数错误   |
| 130403   | 删除权限不足   |
| 130404   | 批量删除失败   |

---

## 操作类异常码 1305xx
| 响应码   | 描述           |
|----------|----------------|
| 130500   | 操作失败       |
| 130501   | 任务已处于该状态 |

---

## 比对解析相关异常码 1306xx
| 响应码   | 描述           |
|----------|----------------|
| 130600   | 比对解析失败   |
| 130601   | HTML内容为空   |
| 130602   | 流程ID不能为空 |
| 130603   | 流程结果不存在 |
| 130604   | 流程详情不存在 |
| 130605   | 流程内容为空   |
| 130606   | 内容解析失败   |
| 130607   | HTML解析失败   |
| 130608   | JSON解析失败   |
| 130609   | 不支持的数据格式 |

---

## 导出相关异常码 1307xx
| 响应码   | 描述           |
|----------|----------------|
| 130700   | 导出失败       |
| 130701   | Excel导出失败  |
| 130702   | 文件写入失败   |
| 130703   | 模板文件不存在 |
| 130704   | 导出数据为空   |
| 130705   | 导出权限不足   |

---

## 系统级别异常码 1399xx
| 响应码   | 描述               |
|----------|--------------------|
| 139900   | 系统异常           |
| 139901   | 服务不可用         |
| 139902   | 未知异常           |
| 139903   | 网络异常           |
| 139904   | 数据库异常         |
| 139905   | 外部服务调用失败   |
| 139906   | 未授权访问         |
| 139907   | 会话失效           |
| 139908   | 资源受限           |

---

> 注：如需补充其他业务场景，请在对应分类下添加响应码及描述。 