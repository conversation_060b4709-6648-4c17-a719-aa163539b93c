# 环境对比系统开发规范指南

本文档整合了项目开发过程中需要遵循的重要规范和开发约定，作为项目成员的开发参考。

## 1. 项目结构

- Controller生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/controller` 目录中
- Service接口生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/service` 目录中
- Service接口实现生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/service/impl` 目录中
- Entity实体类生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/model/entity` 目录中，所有表的主键都叫 `iid`，Entity实体类使用 `@IdGenerator` 注解标记主键属性
- DTO对象生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/model/dto` 目录中
- Bean对象生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/model/bean` 目录中，当涉及多表关联查询时，创建Bean对象使用
- MyBatis的Mapper接口生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/mapper` 目录中
- MyBatis的Mapper接口的XML文件生成在 `contrast-comparison-biz/src/main/resources/mapper` 目录中，需要和Mapper接口包层级一致
- 与各服务间调用获取数据公共接口处理生成在 `contrast-comparison-biz/src/main/java/com/ideal/envc/interaction` 目录中

## 2. 代码规范

### 2.1 基本规范

- 项目使用JDK 1.8，不允许使用1.8以上的API
- 所有方法要有javadoc注释，复杂逻辑方法内的代码也要写上代码注释
- 所有类名、包名使用驼峰命名，所有类都需要有@author，名字用lch
- 关键业务逻辑处需要添加日志打印
- 每个功能的Controller、Service、Mapper接口、Mapper的XML使用单独的包文件夹
- 所有DTO、Bean、Entity类都需要实现 `Serializable` 接口，都需要包含这句 `private static final long serialVersionUID = 1L;`
- 代码注释规范：
  - 允许使用行上注释（即 `//` 形式的注释）
  - 建议优先使用块注释形式（即 `/* */` 或 `/** */`）
  - 注释必须放在被注释代码的上方，禁止放在代码行末尾
  - 注释内容应该清晰说明代码的意图和功能
  - 对于复杂的业务逻辑，应该添加详细的注释说明
  - 示例：
    ```java
    /* 计算用户年龄 */
    int age = calculateAge(birthDate);
    
    /* 处理用户数据 */
    processUserData(user);
    ```

### 2.2 数据库和表命名规范

- 此项目所有表名均以 `ieai_envc_` 开头，实际生成DTO、Bean、Entity类时，不要包含此前缀
- Entity实体类使用 `@IdGenerator` 注解标记主键属性
- 开发的代码中 `jsr303注解` 的约束和提供的表结构字段长度保持一致

### 2.3 依赖注入规范

- 所有Spring注入请使用构造函数方式注入，不要使用其他方式

### 2.4 事务管理规范

- 所有涉及事务操作的Service层的方法必须要加上 `@Transactional` 注解，并且指定 `rollbackFor = Exception.class` 属性
- 禁止同类中内调用本类内其他事务方法，如有此场景，请使用 `com.ideal.common.util.spring.SpringUtil#getBean(Class<T> clz)` 获取本类对象，再调用方法

## 3. 参数验证和异常处理

### 3.1 参数验证

- Controller中参数DTO上、DTO类中需要使用 `jsr303注解` 和 `@Validated`、`@Valid`
- 不可以使用任何lombok注解
- 使用 `jsr303注解` 时，当处理增删改时添加 `groups` 属性，可以使用这两个类 `Update.java` 和 `Save.java`

### 3.2 异常处理原则

- Service层需要对调用方法传递参数进行必要验证，验证需要结合业务逻辑比如是否可为空等
- Service层对于异常抛出需要有分析，有考量，明确规范的异常类型
- Service层接口定义和impl实现类代码中应考虑需对代码的异常捕获并且分析是抛出业务异常还是其他类型异常
- 对可预知的异常情况和验证情况应向上抛出明确的异常信息，Controller层需要捕获并按规范解析和分析出应使用的响应码类型和响应码
- Service层对于传入参数必要的条件和属性要求在参数验证失败时应抛出业务异常由调用方捕获处理

## 4. Controller层规范

### 4.1 返回值规范

- Controller返回值使用泛型类 `com.ideal.common.dto.R<泛型类>`
- 如果不带翻页的接口直接返回 `com.ideal.common.dto.R<泛型类>`
- 如果是带翻页的接口，返回 `com.ideal.common.dto.R<PageInfo<泛型类>>`
- 带翻页的接口，@RequestBody参数对象，使用 `com.ideal.common.dto.TableQueryDto<泛型类>` 包装

### 4.2 响应码规范

- 对 `R.fail()` 等失败返回做统一处理，根据失败类型结合响应码，调用 `R.fail(String code, String msg)`
- 返回结果使用 `R.ok()` 需要改为调用 `R.ok(String code, T data, String message)` 方法
- `code` 和 `message` 来源于 `ResponseCodeEnum` 的相应枚举项的 `code` 和 `desc`
- 对于查询业务失败使用 `QUERY_FAIL(130100)` 和 `DATA_NOT_FOUND(130101)`
- 对于新增业务失败使用 `ADD_FAIL(130200)` 和 `DATA_ALREADY_EXISTS(130201)`
- 对于修改业务失败使用 `UPDATE_FAIL(130300)` 和 `UPDATE_DATA_NOT_FOUND(130301)`
- 对于删除业务失败使用 `DELETE_FAIL(130400)` 和 `DELETE_DATA_NOT_FOUND(130401)`

### 4.3 异常处理规范

- Controller层代码进行捕获异常来响应具体的响应码和描述
- 对异常进行分析结合响应码及描述.md分析出具体的响应异常类型和响应码
- 优先使用业务实现层（Service/Mapper）向上抛出的异常信息
- 对不同类型的异常应返回不同的响应码，比如参数异常、数据不存在、数据已存在等
- 未知异常统一用 `SYSTEM_ERROR(139900)` 响应码和描述

### 4.4 用户信息记录

- 所有增删改操作需要记录操作人信息
- 在Controller中通过 `UserinfoComponent` 获取当前登录用户信息，使用 `UserDto userDto = userinfoComponent.getUser();`
- 调用Service方法时传入用户信息
- 在Service实现中将用户信息设置到对应的实体字段
- 新增操作需要同时设置创建人和更新人信息
- 修改操作需要设置更新人信息

### 4.5 包名和类引用规范

- UserDto类的正确包名：`com.ideal.envc.model.dto.UserDto`
- ContrastBusinessException类的正确包名：`com.ideal.envc.exception.ContrastBusinessException`
- ResponseCodeEnum类的正确包名：`com.ideal.envc.model.enums.ResponseCodeEnum`
- UserinfoComponent类的正确包名：`com.ideal.envc.component.UserinfoComponent`
- 在使用这些类时，必须使用完整的包名进行引用，不要使用通配符导入（*）
- 在编写新代码时，应当遵循项目既定的包结构，将新类放在正确的包路径下

## 5. Service层规范

### 5.1 接口设计

- Service层接口方法改造原则：
  - 优先考虑在原有方法基础上进行改造，如果简单修改就能满足新需求，应直接修改原方法
  - 只有在以下情况下才考虑新增接口方法：
    1. 原方法改造会影响现有业务逻辑
    2. 新增的功能与原方法差异较大
    3. 需要保持向后兼容的特殊场景
  - 如果新增了接口方法，需同步修改所有调用原方法的Controller代码
  - 禁止为了支持新参数而简单地重载原方法，应该评估是否可以通过改造原方法来支持新参数
- 方法签名需明确表达所需参数和返回值
- 异常声明规范：
  - 对于业务逻辑验证失败等常规业务异常，使用 `throws ContrastBusinessException` 声明
  - 对于已有的特定异常类型（如引擎操作异常 `EngineServiceException` 等），可以直接抛出，让Controller层感知并分析异常类型来确定对应的响应码
  - 在抛出异常时，异常信息应当清晰描述具体的错误原因，便于Controller层进行响应码映射
  - 对于方法中可能出现的多个异常类型，应进行合理归类：
    - 属于业务逻辑相关的多个异常应合并使用 `ContrastBusinessException`，在异常信息中清晰说明具体的业务错误原因
    - 只有对于特定的技术组件（如引擎操作、外部服务调用等）的异常，才需要单独声明特定的异常类型
    - 应避免在方法签名中声明过多的异常类型，以保持接口的清晰性和可维护性

### 5.2 实现类规范

- 方法实现应遵循单一职责原则，避免重复实现相似的业务逻辑
- 所有增删改方法需添加 `@Transactional` 注解并指定 `rollbackFor`
- 事务方法调用规范：
  - 严格禁止在同一个Service实现类中的事务方法直接调用本类的其他事务方法，这样会导致事务失效
  - 如果确实需要在事务方法中调用本类的其他事务方法，应该：
    1. 使用 `SpringUtil.getBean()` 获取当前Service的代理对象
    2. 通过代理对象调用目标方法，确保事务正常生效
  - 建议将需要被多个事务方法调用的公共逻辑抽取为普通方法（非事务方法）
- 方法实现中应首先验证参数合法性，对不合法参数抛出明确的异常
- 异常信息应包含具体的错误原因，以便Controller层进行适当的响应码映射
- 方法执行成功后应记录适当的日志信息，包括操作类型、关键数据和操作人

### 5.3 批量操作规范

- 批量操作应尽量减少数据库交互次数
- 可使用 `BatchHandler` 实现批量插入和更新
- 批量操作前先验证数据的合法性，如记录是否存在、状态是否符合要求等
- 批量操作应在一个事务中完成，确保数据一致性
- 更多详细的批量操作方法和最佳实践，请参考 [批量操作最佳实践](./批量操作最佳实践.md)

### 5.4 Mapper接口调用规范

- Service层调用Mapper接口方法时，参数类型必须符合以下规范：
  1. 基本数据类型及其包装类（四类八种）：
     - 整型：byte、short、int、long 及其包装类
     - 浮点型：float、double 及其包装类
     - 字符型：char 及其包装类
     - 布尔型：boolean 及其包装类
  2. 对象类型参数：
     - 必须以 `Entity` 或 `Bean` 结尾
     - 禁止使用其他后缀命名的对象作为参数
  3. 多个基本类型参数：
     - 当需要传递多个基本类型参数时，允许直接传入
     - 建议当参数超过3个时，封装为Entity或Bean对象
     - 多参数时必须使用 `@Param` 注解指定参数名称
     - XML中的参数引用必须与 `@Param` 注解指定的名称保持一致
  4. 关联查询场景：
     - 涉及多表关联查询时，响应结果必须使用Bean对象
     - Bean对象中应包含关联表的关键字段（如名称等）
     - 查询条件应支持关联字段的模糊查询

- Mapper接口参数命名规范：
  1. 单个参数时：
     - 基本类型参数建议使用 `@Param` 注解
     - 对象类型参数可以省略 `@Param` 注解
  2. 多个参数时：
     - 必须为每个参数添加 `@Param` 注解
     - 注解值要见名知意，建议使用驼峰命名
     - XML中使用 `#{参数名}` 或 `${参数名}` 引用参数
  3. 示例代码：
     ```java
     // 多参数示例
     List<TaskBean> selectByCondition(@Param("taskId") Long taskId, 
                                    @Param("planName") String planName);
     
     // XML中的对应用法
     <select id="selectByCondition" resultMap="TaskBeanResult">
         SELECT * FROM table 
         WHERE task_id = #{taskId}
         AND plan_name LIKE CONCAT('%', #{planName}, '%')
     </select>
     ```

### 5.5 用户信息处理规范

- 创建人和更新人信息设置规范：
  1. 获取用户全名：
     - 必须使用 `userDto.getFullName()` 获取用户全名
     - 禁止使用 `getLoginName()` 或其他方法获取用户名
  2. 新增操作时：
     ```java
     // 设置创建人信息
     entity.setCreatorId(userDto.getId());
     entity.setCreatorName(userDto.getFullName());
     // 设置更新人信息（与创建人相同）
     entity.setUpdatorId(userDto.getId());
     entity.setUpdatorName(userDto.getFullName());
     // 设置时间信息
     Date now = new Date();
     entity.setCreateTime(now);
     entity.setUpdateTime(now);
     ```
  3. 更新操作时：
     ```java
     // 只设置更新人信息
     entity.setUpdatorId(userDto.getId());
     entity.setUpdatorName(userDto.getFullName());
     entity.setUpdateTime(new Date());
     ```
  4. 日志记录时：
     ```java
     logger.info("操作说明，操作人：{}，其他信息：{}", userDto.getFullName(), otherInfo);
     ```

- 用户信息处理原则：
  1. 所有需要记录操作人的地方，统一使用 `getFullName()` 方法
  2. 创建和更新操作必须同时记录操作人ID和名称
  3. 批量操作时也要保证每条记录都正确设置操作人信息
  4. 关键业务操作的日志记录中应包含操作人信息
  5. 确保用户信息不为空，必要时进行空值检查：
     ```java
     if (userDto == null || userDto.getId() == null) {
         throw new ContrastBusinessException("用户信息不能为空");
     }
     ```

## 6. Mapper和XML规范

### 6.1 接口设计

- 方法名应清晰表达操作意图，如 `selectXXXByYYY`、`insertXXX`、`updateXXX`、`deleteXXX`
- 参数应使用Entity或基本类型，不应直接使用DTO
- 需要多表查询的场景，返回值使用Bean对象

### 6.2 SQL编写

- XML中SQL编写不允许使用自定义函数或特定数据库的函数
- 仅能使用如sum、max、min等支持所有数据类型的内置函数
- 批量操作应使用合适的SQL语法，如批量插入、批量更新等
- 动态SQL应使用MyBatis提供的标签，如`<if>`、`<choose>`、`<where>`等

## 7. 工具类使用

- 使用 `com.ideal.common.util.BeanUtils` 进行DTO和Entity对象的转换
- 使用 `com.ideal.common.util.PageDataUtil` 分页工具类
- 使用 `com.ideal.common.util.spring.SpringUtil` 工具类获取spring中的bean对象
- Service实现类方法中如果涉及分页使用 `com.github.pagehelper.PageHelper.startPage(pageNum, pageSize)`
- 导入导出Excel功能使用 `easyexcel` 库实现
- 可以使用 `commons-lang3` 的类库、`hutool` 类库中提供的常用方法，如判断字符串是否为空等

## 8. 状态值和常量规范

- 启用/禁用状态：0表示启用，1表示禁用
- 其他状态值应在相关枚举类中定义，不应在代码中使用魔法数字
- 常用的常量应在相应的常量类中定义

## 9. 参考实现

可参考以下类作为开发参考：
- `DictionaryController.java`
- `DictionaryServiceImpl.java`
- `DictionaryMapper.java`
- `NodeRelationController.java` 
- `NodeRelationServiceImpl.java`
- `PlanController.java`
- `PlanServiceImpl.java`

## 10. 相关文档

项目开发过程中，请参考以下文档进行开发：

- [批量操作最佳实践](./批量操作最佳实践.md)：详细介绍了批量操作的实现方法和优化策略
- [响应码和异常处理](./响应码和异常处理.md)：详细介绍了项目中响应码的使用和异常处理机制

## 11. 数据结构变更规范

### 11.1 变更申请流程

1. **变更信息提供**
   - 表名称：需明确指出要变更的表名
   - 变更类型：新增表、修改表（新增字段/修改字段/删除字段）、删除表
   - 变更内容：详细说明具体的变更内容，包括字段名、类型、长度、约束等

2. **变更评估**
   - 评估变更影响范围
   - 确认是否影响现有功能
   - 评估数据迁移需求
   - 确定变更实施计划

### 11.2 Liquibase补丁管理

1. **版本目录管理**
   - 补丁文件位置：src/main/resources/db/changelog/当前开发版本目录下
   - 版本号获取：从项目根目录的`version.log`文件中读取（格式：VERSION = 9.20.0）
   - 目录命名：使用版本号作为目录名

2. **补丁文件规范**
   - 文件命名：`changeLog.yaml`
   - 文件位置：`版本号目录/changeLog.yaml`
   - 补丁内容：按Liquibase语法编写变更内容
   - id生成规则：当前时间戳如：1715768038000
   - author的指定：固定值为lch
   - 主文件更新：在`db.changelog-master.yaml`中添加新补丁文件的引用

3. **补丁编写示例**
```yaml
databaseChangeLog:
  - changeSet:
      id: 20240315-add-column-example
      author: lch
      changes:
        - addColumn:
            tableName: ieai_envc_example
            columns:
              - column:
                  name: new_column
                  type: varchar(50)
                  remarks: "新增字段说明"
```

### 11.3 代码调整规范

1. **新增表操作**
   - 创建对应的DTO类（位于`model/dto`目录）
   - 创建Entity类（位于`model/entity`目录）
   - 创建Bean类（如需多表关联）
   - 创建Mapper接口及XML文件
   - 实现基础的CRUD代码
   - 添加必要的单元测试

2. **新增字段操作**
   - 在DTO类中添加对应属性及注解
   - 在Entity类中添加对应属性
   - 在Bean类中添加对应属性（如有）
   - 在Mapper XML中更新SQL语句
   - 更新相关单元测试

3. **修改字段操作**
   - 更新DTO类中的属性定义及注解
   - 更新Entity类中的属性定义
   - 更新Bean类中的属性定义（如有）
   - 更新Mapper XML中的SQL语句
   - 更新相关单元测试

4. **删除字段操作**
   - 从DTO类中移除相关属性
   - 从Entity类中移除相关属性
   - 从Bean类中移除相关属性（如有）
   - 更新Mapper XML中的SQL语句
   - 更新相关单元测试

### 11.4 代码调整范围

基于项目开发经验，数据结构变更通常涉及以下文件的调整：

1. **模型层调整**
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/model/dto/相关DTO.java`
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/model/entity/相关Entity.java`
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/model/bean/相关Bean.java`

2. **数据访问层调整**
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/mapper/相关Mapper.java`
   - `contrast-comparison-biz/src/main/resources/mapper/相关Mapper.xml`

3. **业务逻辑层调整**
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/service/相关Service.java`
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/service/impl/相关ServiceImpl.java`

4. **控制层调整**
   - `contrast-comparison-biz/src/main/java/com/ideal/envc/controller/相关Controller.java`

5. **测试代码调整**
   - `contrast-comparison-biz/src/test/java/com/ideal/envc/service/impl/相关ServiceImplTest.java`
   - `contrast-comparison-biz/src/test/java/com/ideal/envc/controller/相关ControllerTest.java`

### 11.5 变更注意事项

1. **数据一致性**
   - 确保所有相关代码文件的变更保持一致
   - 确保字段命名规范统一
   - 确保字段类型在各层之间匹配

2. **测试覆盖**
   - 所有变更必须有对应的单元测试
   - 确保测试用例覆盖变更的功能点
   - 验证变更不影响现有功能

3. **文档更新**
   - 更新相关接口文档
   - 更新数据库设计文档
   - 记录变更历史

### 11.6 数据结构文档同步更新

1. **文档范围**
   - `doc/一致性比对微服务表结构.md`
   - `doc/一致性比对微服务表结构.txt`

2. **更新要求**
   - 新增表：在两个文档中同步添加新表的完整结构说明
   - 修改表：更新两个文档中对应表的字段信息
   - 删除表：从两个文档中同步移除相关表的信息
   - 字段变更：在两个文档中同步更新字段的定义、类型、说明等信息

3. **文档内容规范**
   - 表结构说明需包含：表名、表说明、字段清单
   - 字段信息需包含：字段名、字段类型、长度、是否可空、默认值、字段说明
   - 保持MD和TXT两种格式文档的内容完全一致
   - 确保文档格式规范统一

4. **同步更新流程**
   - 在进行数据结构变更时，同步检查并更新这两个文档
   - 在提交代码前，确保文档变更与实际数据结构变更保持一致
   - 在代码评审时，将文档更新作为检查项之一
   - 定期进行文档与实际数据结构的一致性校验

5. **注意事项**
   - 确保两个文档的内容完全一致，避免信息不同步
   - 保持文档的可读性和格式统一性
   - 对重要的变更说明添加变更原因和影响说明

### 11.2 表结构参考规范

- 在开发过程中，必须首先参考 `一致性比对微服务表结构.md` 文档中定义的表结构
- 所有的字段名称、类型、长度、是否可空等属性必须严格按照表结构文档执行
- 禁止在代码中使用未在表结构文档中定义的字段
- 如需添加新字段，必须先更新表结构文档并获得审批

### 11.3 数据对象命名规范

- 与Mapper层交互的Java对象必须遵循以下命名规范：
  1. 单表操作时使用 `Entity` 结尾的实体类，如 `TaskEntity`
  2. 多表关联查询时使用 `Bean` 结尾的对象，如 `TaskListBean`
  3. 禁止使用其他后缀命名的对象与Mapper层交互
- Bean对象的创建原则：
  1. 当查询涉及多个表的字段时，必须创建对应的Bean对象
  2. Bean对象的属性名要与数据库字段的映射关系一致
  3. Bean对象必须实现 `Serializable` 接口
  4. Bean对象应该包含完整的JavaDoc注释

## 12. 更新记录

- 2023-05-18：初始版本创建
- 2023-06-10：添加批量操作最佳实践文档链接
- 2024-03-15：添加数据结构变更规范
- 2024-03-15：补充数据结构文档同步更新要求
- 功能优化后，如有新的规范项请更新此文档 