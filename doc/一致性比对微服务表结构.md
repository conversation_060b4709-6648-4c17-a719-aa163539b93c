# 一致性比对微服务表结构文档

本文档描述了一致性比对微服务的数据库表结构，包括表字段、索引等信息，用于辅助开发程序逻辑和SQL关系。

## 目录

- [ieai_envc_project (系统配置主表)](#ieai_envc_project)
- [ieai_envc_system_computer (系统设备关系表)](#ieai_envc_system_computer)
- [ieai_envc_system_computer_node (系统与设备节点关系表)](#ieai_envc_system_computer_node)
- [ieai_envc_node_relation (节点关系规则)](#ieai_envc_node_relation)
- [ieai_envc_node_rule_content (节点关系规则)](#ieai_envc_node_rule_content)
- [ieai_envc_plan (方案信息)](#ieai_envc_plan)
- [ieai_envc_plan_relation (方案信息)](#ieai_envc_plan_relation)
- [ieai_envc_task (任务表)](#ieai_envc_task)
- [ieai_envc_run_instance (实例表)](#ieai_envc_run_instance)
- [ieai_envc_run_instance_info (实例详情)](#ieai_envc_run_instance_info)
- [ieai_envc_run_rule (节点规则结果表)](#ieai_envc_run_rule)
- [ieai_envc_run_rule_sync (节点规则同步结果)](#ieai_envc_run_rule_sync)
- [ieai_envc_run_flow (节点规则流程表)](#ieai_envc_run_flow)
- [ieai_envc_run_flow_result (流程输出结果表)](#ieai_envc_run_flow_result)
- [ieai_envc_dictionary (字典码表)](#ieai_envc_dictionary)
- [ieai_envc_dictionary_detail (字典详情表)](#ieai_envc_dictionary_detail)
- [表关系图](#表关系图)

## 表结构详情

<a id="ieai_envc_project"></a>
### ieai_envc_project (比对业务系统表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| ibusiness_system_code | VARCHAR(100) | | 是 | 系统编码 |
| ibusiness_system_name | VARCHAR(100) | | 是 | 系统名称 |
| ibusiness_system_unique | VARCHAR(100) | | 是 | 系统唯一标识 |
| ibusiness_system_desc | VARCHAR(150) | | 是 | 系统描述 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | VARCHAR(50) | | 是 | 创建人 |
| icreate_time | timestamp | | 是 | 添加时间 |
| istatus | smallint | 1 | 是 | 是否有效（1：有效，0：失效） |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- idx_envc_project_system_01：ibusiness_system_id, ibusiness_system_name
- idx_envc_project_system_02：ibusiness_system_name
- idx_envc_project_system_03：ibusiness_system_desc

<a id="ieai_envc_system_computer"></a>
### ieai_envc_system_computer (系统设备关系表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| icomputer_id | bigint | | 是 | 设备ID |
| icomputer_ip | varchar(255) | | 是 | 代理IP |
| icomputer_name | varchar(150) | | 是 | 代理名称 |
| icenter_id | bigint | -1 | 是 | 中心ID |
| icenter_name | varchar(100) | | 是 | 中心名称 |
| icreate_time | timestamp | | 是 | 存储时间 |
| icreator_id | bigint | | 是 | 添加人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |

**索引：**
- 主键：iid
- idx_system_computer_01：ibusiness_system_id, icomputer_id
- idx_system_computer_02：icomputer_id

<a id="ieai_envc_system_computer_node"></a>
### ieai_envc_system_computer_node (系统与设备节点关系表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| isource_center_id | bigint | | 是 | 源中心ID |
| itarget_center_id | bigint | | 是 | 目标中心ID |
| isource_computer_id | bigint | | 是 | 源设备ID |
| isource_computer_ip | varchar(255) | | 是 | 源设备IP |
| itarget_computer_id | bigint | | 是 | 目标设备ID |
| itarget_computer_ip | varchar(255) | | 是 | 目标设备IP |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |

**索引：**
- 主键：iid
- idx_system_computer_node_01：ibusiness_system_id, isource_center_id
- idx_system_computer_node_02：ibusiness_system_id, itarget_center_id
- idx_system_computer_node_03：isource_computer_id
- idx_system_computer_node_04：itarget_computer_id

<a id="ieai_envc_node_relation"></a>
### ieai_envc_node_relation (节点关系规则)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ienvc_system_computer_node_id | bigint | | 是 | 节点关系ID |
| imodel | smallint | | 是 | 模式（0：比对，1：同步，2：比对后同步） |
| itype | bigint | 0 | 是 | 模块类型（0：目录，1;文件，2：脚本） |
| ipath | varchar(255) | | 是 | 路径 |
| isource_path | varchar(255) | | 是 | 原路径 |
| iencode | varchar(32) | | 是 | 字符集 |
| iway | smallint | 0 | 是 | 方式（0：全部:1：部分） |
| irule_type | smallint | | 是 | 规则类型（0：匹配，1：排除） |
| ienabled | smallint | 0 | 是 | 是否有效（0：有效，1：无效） |
| ichild_level | smallint | 1 | 是 | 是否子集（0:是，1：否） |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- idx_node_relation_01：ienvc_system_computer_node_id

<a id="ieai_envc_node_rule_content"></a>
### ieai_envc_node_rule_content (节点关系规则)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ienvc_node_relation_id | bigint | | 是 | 信息配置ID |
| irule_content | longtext | | 是 | 规则内容 |

**索引：**
- 主键：iid
- idx_node_rule_content_01：ienvc_node_relation_id

<a id="ieai_envc_plan"></a>
### ieai_envc_plan (方案信息)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| iname | varchar(100) | | 是 | 方案名称 |
| iplan_desc | varchar(150) | | 是 | 方案描述 |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | |

**索引：**
- 主键：iid
- idx_envc_plan_01：iname

<a id="ieai_envc_plan_relation"></a>
### ieai_envc_plan_relation (方案信息)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| ienvc_plan_id | bigint | | 是 | 方案ID |
| ibusiness_system_id | bigint | | 是 | 业务系统ID |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |

**索引：**
- 主键：iid
- idx_plan_relation_01：ienvc_plan_id, ibusiness_system_id (唯一索引)
- idx_plan_relation_02：ibusiness_system_id

<a id="ieai_envc_task"></a>
### ieai_envc_task (任务表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| ienvc_plan_id | bigint | | 是 | 方案ID |
| icron | varchar(300) | | 是 | 周期表达式 |
| ienabled | smallint | 1 | 是 | 是否启用（1:启用，0：禁用） |
| istate | smallint | | 是 | 启停状态（0:启动，1：停止） |
| isource_center_id | bigint | | 是 | 源中心ID |
| itarget_center_id | bigint | | 是 | 目标中心ID |
| ischeduled_id | bigint | | 是 | 定时ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | |

**索引：**
- 主键：iid
- idx_envc_task_01：ienvc_plan_id
- idx_envc_task_02：istate
- idx_task_center_01：isource_center_id, itarget_center_id

<a id="ieai_envc_run_instance"></a>
### ieai_envc_run_instance (实例表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| ienvc_plan_id | bigint | | 是 | 方案ID |
| ienvc_task_id | bigint | -1 | 是 | 周期任务ID（方案启动和重试无任务id） |
| iresult | smallint | -1 | 是 | 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） |
| istate | smallint | 0 | 是 | 启停状态（0：运行中，1：已完成，2：终止） |
| ifrom | smallint | | 是 | 触发来源：（1：周期触发，2：手动触发，3：重试） |
| istarter_name | varchar(50) | | 是 | 启动人名称 |
| istarter_id | bigint | | 是 | 启动人ID |
| istart_time | timestamp | | 是 | 启动时间 |
| iend_time | timestamp | | 是 | 结束时间 |
| ielapsed_time | bigint | | 是 | 耗时 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- idx_run_instance_01：ienvc_plan_id, ienvc_task_id
- idx_run_instance_02：ienvc_task_id

<a id="ieai_envc_run_instance_info"></a>
### ieai_envc_run_instance_info (实例详情)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ienvc_run_instance_id | bigint | | 是 | 实例ID |
| ienvc_plan_id | bigint | | 是 | 方案ID |
| ibusiness_system_id | bigint | | 是 | 系统ID |
| isource_center_id | bigint | | 是 | 源中心ID |
| isource_center_name | varchar(150) | | 是 | 源中心名称 |
| itarget_center_id | bigint | | 是 | 目标中心ID |
| itarget_center_name | varchar(150) | | 是 | 目标中心名称 |
| isource_computer_id | bigint | | 是 | 源设备ID |
| isource_computer_ip | varchar(255) | | 是 | 源设备IP |
| isource_computer_port | integer | | 是 | 源设备端口 |
| isource_computer_os | varchar(150) | | 是 | 源设备操作系统 |
| itarget_computer_id | bigint | | 是 | 目标设备ID |
| itarget_computer_ip | varchar(255) | | 是 | 目标设备IP |
| itarget_computer_port | integer | | 是 | 目标设备端口 |
| itarget_computer_os | varchar(150) | | 是 | 目标设备操作系统 |
| istore_time | timestamp | | 是 | 存储时间 |
| iresult | smallint | | 是 | 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） |
| istate | smallint | | 是 | 启停状态（0：运行中，1：已完成，2：终止） |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- idx_run_instance_info_01：ienvc_run_instance_id
- idx_run_instance_info_02：ienvc_plan_id, ibusiness_system_id
- idx_run_instance_info_03：isource_computer_ip
- idx_run_instance_info_04：itarget_computer_ip

<a id="ieai_envc_run_rule"></a>
### ieai_envc_run_rule (节点规则结果表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ienvc_run_instance_info_id | bigint | | 是 | 实例详情ID |
| imodel | smallint | -1 | 是 | 模式（0：比对，1：同步，2：比对后同步） |
| itype | bigint | 0 | 是 | 模块类型（0：目录，1;文件，2：脚本） |
| ipath | varchar(255) | | 是 | 路径 |
| iencode | varchar(32) | | 是 | 字符集 |
| iway | smallint | 0 | 是 | 方式（0：全部:1：部分） |
| irule_type | smallint | | 是 | 规则类型（0：匹配，1：排除） |
| ienabled | smallint | 0 | 是 | 是否有效（0：有效，1：无效） |
| ichild_level | smallint | 1 | 是 | 是否子集（0:是，1：否） |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |
| iend_time | timestamp | | 是 | 更新时间 |
| iresult | smallint | | 是 | 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） |
| istate | smallint | | 是 | 启停状态（0：运行中，1：已完成，2：终止） |
| ielapsed_time | bigint | | 是 | 耗时 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- idx_run_rule_01：ienvc_run_instance_info_id

<a id="ieai_envc_run_rule_sync"></a>
### ieai_envc_run_rule_sync (节点规则同步结果)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ienvc_system_computer_node_id | bigint | | 是 | 节点关系ID |
| ienvc_run_rule_id | bigint | | 是 | 节点规则结果ID |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |
| iend_time | timestamp | | 是 | 更新时间 |
| iresult | smallint | | 是 | 结果状态（-1:运行中，0:一致/成功，1：不一致/失败） |
| istate | smallint | | 是 | 启停状态（0：运行中，1：已完成，2：终止） |
| ielapsed_time | bigint | | 是 | 耗时 |

**索引：**
- 主键：iid
- idx_run_rule_sync_01：ienvc_run_rule_id

<a id="ieai_envc_run_rule_content"></a>
### ieai_envc_run_rule_content (节点规则内容表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| ienvc_run_rule_id | bigint | | 是 | 节点规则结果ID |
| icontent | text | | 是 | 内容 |

**索引：**
- 主键：iid
- idx_run_rule_content_01：ienvc_run_rule_id

<a id="ieai_envc_run_flow"></a>
### ieai_envc_run_flow (节点规则流程表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID |
| iflowid | bigint | | 是 | 流程ID |
| irun_biz_id | bigint | | 是 | 比对规则ID或者比对规则对应的同步id |
| imodel | smallint | | 是 | 来源标识（0：比对，1：同步） |
| icreator_id | bigint | | 是 | 创建人ID |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreate_time | timestamp | | 是 | 创建时间 |
| iend_time | timestamp | | 是 | 更新时间 |
| istate | smallint | | 是 | 启停状态（0：运行中，1：已完成，2：终止） |
| ielapsed_time | bigint | | 是 | 耗时 |
| iret | varchar(10) | | 是 | 执行结束码 |
| iupdate_order_time | bigint | | 是 | 引擎消息时间字段 |

**索引：**
- 主键：iid
- idx_run_flow_01：irun_biz_id, iflowid
- idx_run_flow_02：iflowid

<a id="ieai_envc_run_flow_result"></a>
### ieai_envc_run_flow_result (流程输出结果表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键ID (自增) |
| ienvc_run_flow_id | bigint | | 是 | 节点规则流程主键 |
| iflowid | bigint | | 是 | 流程ID |
| icontent | longtext | | 是 | 输出内容 |
| istderr | longtext | | 是 | 错误输出内容 |
| istore_time | timestamp | | 是 | 结果入库时间 |
| iupdate_order_time | bigint | | 是 | 引擎消息时间字段 |

**索引：**
- 主键：iid
- idx_run_flow_result_01：ienvc_run_flow_id
- idx_run_flow_result_02：iflowid

<a id="ieai_envc_dictionary"></a>
### ieai_envc_dictionary (字典码表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| icode | varchar(50) | | 是 | 字典码 |
| idescription | varchar(150) | | 是 | 字典描述 |
| init | smallint | | 是 | 是否初始化 0：否，1：是 |
| ideleted | smallint | 0 | 是 | 删除标识 0：否，1：是 |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | |

**索引：**
- 主键：iid
- idx_envc_dictionary_01：icode (唯一索引)

<a id="ieai_envc_dictionary_detail"></a>
### ieai_envc_dictionary_detail (字典详情表)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| ienvc_dictionary_id | bigint | | 是 | 码表主键 |
| icode | varchar(50) | | 是 | 字典码 |
| ilable | varchar(100) | | 是 | 显示名称 |
| ivalue | varchar(200) | | 是 | 显示值 |
| isort | bigint | | 是 | 排序序号 |
| ideleted | smallint | 0 | 是 | 删除标识 0：否，1：是 |
| iarray_flag | smallint | 0 | 是 | 数组标识 0：否，1：是 |
| ivalue_type | varchar(20) | | 是 | 字典值类型（四类八种+String） |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | |

**索引：**
- 主键：iid
- idx_dictionary_detail_01：icode
- idx_dictionary_detail_02：ienvc_dictionary_id

<a id="表关系图"></a>
## 表关系图

以下是主要表之间的关系：

```
ieai_envc_project <-- ieai_envc_system_computer
                  <-- ieai_envc_plan_relation --> ieai_envc_plan --> ieai_envc_task
                                                                  --> ieai_envc_run_instance --> ieai_envc_run_instance_info --> ieai_envc_run_rule --> ieai_envc_run_rule_sync
                                                                                                                              --> ieai_envc_run_flow --> ieai_envc_run_flow_result

ieai_envc_system_computer --> ieai_envc_system_computer_node --> ieai_envc_node_relation --> ieai_envc_node_rule_content

ieai_envc_dictionary --> ieai_envc_dictionary_detail
```

### 主要业务流程关系

1. **系统配置**：
   - ieai_envc_project 存储业务系统信息
   - ieai_envc_system_computer 存储系统设备关系
   - ieai_envc_system_computer_node 存储系统与设备节点关系

2. **比对规则管理**：
   - ieai_envc_node_relation 存储节点关系规则
   - ieai_envc_node_rule_content 存储节点关系规则内容

3. **方案管理**：
   - ieai_envc_plan 存储方案信息
   - ieai_envc_plan_relation 存储方案与业务系统的关系

4. **任务管理**：
   - ieai_envc_task 存储任务信息

5. **执行实例**：
   - ieai_envc_run_instance 存储实例信息
   - ieai_envc_run_instance_info 存储实例详情
   - ieai_envc_run_rule 存储节点规则结果
   - ieai_envc_run_rule_sync 存储节点规则同步结果
   - ieai_envc_run_flow 存储节点规则流程
   - ieai_envc_run_flow_result 存储流程输出结果

6. **字典管理**：
   - ieai_envc_dictionary 存储字典码表
   - ieai_envc_dictionary_detail 存储字典详情
