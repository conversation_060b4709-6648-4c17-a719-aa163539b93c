# 字典管理设计文档

## 1. 概述

字典管理模块是一致性比对微服务的基础功能模块，用于管理系统中的各类字典数据。该模块包含字典码表和字典详情表两个主要部分，提供了字典数据的增删改查等基本功能。

## 2. 数据模型

### 2.1 数据库表结构

#### 2.1.1 字典码表 (ieai_envc_dictionary)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| icode | varchar(50) | | 是 | 字典码 |
| idescription | varchar(150) | | 是 | 字典描述 |
| init | smallint | | 是 | 是否初始化 0：否，1：是 |
| ideleted | smallint | 0 | 是 | 删除标识 0：否，1：是 |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- 唯一索引：idx_envc_dictionary_01 (icode)

#### 2.1.2 字典详情表 (ieai_envc_dictionary_detail)

| 字段名 | 类型 | 默认值 | 允许空 | 说明 |
|-------|------|-------|-------|------|
| iid | bigint | | 否 | 主键 |
| ienvc_dictionary_id | bigint | | 是 | 码表主键 |
| icode | varchar(50) | | 是 | 字典码 |
| ilable | varchar(100) | | 是 | 显示名称 |
| ivalue | varchar(200) | | 是 | 显示值 |
| isort | bigint | | 是 | 排序序号 |
| ideleted | smallint | 0 | 是 | 删除标识 0：否，1：是 |
| iarray_flag | smallint | 0 | 是 | 数组标识 0：否，1：是 |
| ivalue_type | varchar(20) | | 是 | 字典值类型（四类八种+String） |
| icreator_name | varchar(50) | | 是 | 创建人名称 |
| icreator_id | bigint | | 是 | 创建人ID |
| icreate_time | timestamp | | 是 | 创建时间 |
| iupdator_id | bigint | | 是 | 更新人ID |
| iupdator_name | varchar(50) | | 是 | 更新人名称 |
| iupdate_time | timestamp | | 是 | 更新时间 |

**索引：**
- 主键：iid
- 索引：idx_dictionary_detail_01 (icode)
- 索引：idx_dictionary_detail_02 (ienvc_dictionary_id)

### 2.2 实体类

#### 2.2.1 字典码实体类 (DictionaryEntity)

```java
public class DictionaryEntity implements Serializable {
    private Long id;                // 主键
    private String code;            // 字典码
    private String description;     // 字典描述
    private Integer nit;            // 是否初始化 0：否，1：是
    private Integer deleted;        // 删除标识 0：否，1：是
    private String creatorName;     // 创建人名称
    private Long creatorId;         // 创建人ID
    private Date createTime;        // 创建时间
    private Long updatorId;         // 更新人ID
    private String updatorName;     // 更新人名称
    private Date updateTime;        // 更新时间

    // getter和setter方法
}
```

#### 2.2.2 字典详情实体类 (DictionaryDetailEntity)

```java
public class DictionaryDetailEntity implements Serializable {
    private Long id;                // 主键
    private Long envcDictionaryId;  // 码表主键
    private String code;            // 字典码
    private String lable;           // 显示名称
    private String value;           // 显示值
    private Long sort;              // 排序序号
    private Integer deleted;        // 删除标识 0：否，1：是
    private Integer arrayFlag;      // 数组标识 0：否，1：是
    private String valueType;       // 字典值类型
    private String creatorName;     // 创建人名称
    private Long creatorId;         // 创建人ID
    private Date createTime;        // 创建时间
    private Long updatorId;         // 更新人ID
    private String updatorName;     // 更新人名称
    private Date updateTime;        // 更新时间

    // getter和setter方法
}
```

### 2.3 DTO类

#### 2.3.1 字典码DTO (DictionaryDto)

与DictionaryEntity结构相同，用于前后端数据传输。

#### 2.3.2 字典码查询DTO (DictionaryQueryDto)

与DictionaryDto结构相同，用于查询条件传输。

#### 2.3.3 字典详情DTO (DictionaryDetailDto)

与DictionaryDetailEntity结构相同，用于前后端数据传输。

#### 2.3.4 字典详情查询DTO (DictionaryDetailQueryDto)

与DictionaryDetailDto结构相同，用于查询条件传输。

## 3. 系统架构

字典管理模块采用标准的三层架构设计：

1. **表现层（Controller）**：处理HTTP请求，调用服务层方法，返回处理结果
2. **服务层（Service）**：实现业务逻辑，调用数据访问层方法
3. **数据访问层（Mapper）**：与数据库交互，执行CRUD操作

### 3.1 表现层

#### 3.1.1 字典码控制器 (DictionaryController)

```java
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {
    private final IDictionaryService dictionaryService;

    // 构造函数注入服务
    public DictionaryController(IDictionaryService dictionaryService) {
        this.dictionaryService = dictionaryService;
    }

    // 查询字典码列表
    @GetMapping("/list")
    public R<PageInfo<DictionaryDto>> list(TableQueryDto<DictionaryQueryDto> tableQueryDto);

    // 查询字典码详细信息
    @GetMapping(value = "/get")
    public R<DictionaryDto> getAgentInfoInfo(@RequestParam(value = "id")Long id);

    // 新增保存字典码
    @PostMapping("/save")
    public R<Void> save(@RequestBody DictionaryDto dictionaryDto);

    // 修改保存字典码
    @PostMapping("/update")
    public R<Void> update(@RequestBody DictionaryDto dictionaryDto);

    // 删除字典码
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids);
}
```

#### 3.1.2 字典详情控制器 (DictionaryDetailController)

```java
@RestController
@RequestMapping("/envc/detail")
public class DictionaryDetailController {
    private final IDictionaryDetailService dictionaryDetailService;

    // 构造函数注入服务
    public DictionaryDetailController(IDictionaryDetailService dictionaryDetailService) {
        this.dictionaryDetailService = dictionaryDetailService;
    }

    // 查询字典详情列表
    @GetMapping("/list")
    public R<PageInfo<DictionaryDetailDto>> list(TableQueryDto<DictionaryDetailQueryDto> tableQueryDto);

    // 查询字典详情详细信息
    @GetMapping(value = "/get")
    public R<DictionaryDetailDto> getAgentInfoInfo(@RequestParam(value = "id")Long id);

    // 新增保存字典详情
    @PostMapping("/save")
    public R<Void> save(@RequestBody DictionaryDetailDto dictionaryDetailDto);

    // 修改保存字典详情
    @PostMapping("/update")
    public R<Void> update(@RequestBody DictionaryDetailDto dictionaryDetailDto);

    // 删除字典详情
    @PostMapping("/remove")
    public R<Void> remove(@RequestParam(value = "ids") Long[] ids);
}
```

### 3.2 服务层

#### 3.2.1 字典码服务接口 (IDictionaryService)

```java
public interface IDictionaryService {
    // 查询字典码
    DictionaryDto selectDictionaryById(Long id);

    // 查询字典码列表
    PageInfo<DictionaryDto> selectDictionaryList(DictionaryQueryDto dictionaryQueryDto, Integer pageNum, Integer pageSize);

    // 新增字典码
    int insertDictionary(DictionaryDto dictionaryDto);

    // 修改字典码
    int updateDictionary(DictionaryDto dictionaryDto);

    // 批量删除字典码
    int deleteDictionaryByIds(Long[] ids);

    // 验证字典码是否存在
    Boolean validateDictionaryCode(String code);
}
```

#### 3.2.2 字典码服务实现 (DictionaryServiceImpl)

```java
@Service
public class DictionaryServiceImpl implements IDictionaryService {
    private final DictionaryMapper dictionaryMapper;

    // 构造函数注入Mapper
    public DictionaryServiceImpl(DictionaryMapper dictionaryMapper) {
        this.dictionaryMapper = dictionaryMapper;
    }

    // 实现接口方法
    // ...
}
```

#### 3.2.3 字典详情服务接口 (IDictionaryDetailService)

```java
public interface IDictionaryDetailService {
    // 查询字典详情
    DictionaryDetailDto selectDictionaryDetailById(Long id);

    // 查询字典详情列表
    PageInfo<DictionaryDetailDto> selectDictionaryDetailList(DictionaryDetailQueryDto dictionaryDetailQueryDto, Integer pageNum, Integer pageSize);

    // 新增字典详情
    int insertDictionaryDetail(DictionaryDetailDto dictionaryDetailDto);

    // 修改字典详情
    int updateDictionaryDetail(DictionaryDetailDto dictionaryDetailDto);

    // 批量删除字典详情
    int deleteDictionaryDetailByIds(Long[] ids);

    // 根据字典码获取字典详情列表
    List<DictionaryDetailDto> findDictionaryDetailListByCode(String code);
}
```

#### 3.2.4 字典详情服务实现 (DictionaryDetailServiceImpl)

```java
@Service
public class DictionaryDetailServiceImpl implements IDictionaryDetailService {
    private final DictionaryDetailMapper dictionaryDetailMapper;

    // 构造函数注入Mapper
    public DictionaryDetailServiceImpl(DictionaryDetailMapper dictionaryDetailMapper) {
        this.dictionaryDetailMapper = dictionaryDetailMapper;
    }

    // 实现接口方法
    // ...
}
```

### 3.3 数据访问层

#### 3.3.1 字典码Mapper接口 (DictionaryMapper)

```java
public interface DictionaryMapper {
    // 查询字典码
    DictionaryEntity selectDictionaryById(Long id);

    // 查询字典码列表
    List<DictionaryEntity> selectDictionaryList(DictionaryEntity dictionary);

    // 新增字典码
    int insertDictionary(DictionaryEntity dictionary);

    // 修改字典码
    int updateDictionary(DictionaryEntity dictionary);

    // 删除字典码
    int deleteDictionaryById(Long id);

    // 批量删除字典码
    int deleteDictionaryByIds(Long[] ids);
}
```

#### 3.3.2 字典详情Mapper接口 (DictionaryDetailMapper)

```java
public interface DictionaryDetailMapper {
    // 查询字典详情
    DictionaryDetailEntity selectDictionaryDetailById(Long id);

    // 查询字典详情列表
    List<DictionaryDetailEntity> selectDictionaryDetailList(DictionaryDetailEntity dictionaryDetail);

    // 新增字典详情
    int insertDictionaryDetail(DictionaryDetailEntity dictionaryDetail);

    // 修改字典详情
    int updateDictionaryDetail(DictionaryDetailEntity dictionaryDetail);

    // 删除字典详情
    int deleteDictionaryDetailById(Long id);

    // 批量删除字典详情
    int deleteDictionaryDetailByIds(Long[] ids);

    // 根据字典码获取字典详情列表
    List<DictionaryDetailEntity> selectDictionaryDetailListByCode(String code);
}
```

## 4. API接口设计

### 4.1 字典码接口

#### 4.1.1 验证字典码是否存在

- **URL**: `/dictionary/validateDictionaryCode`
- **Method**: GET
- **请求参数**:
  - `code`: 字典码
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息
  - `data`: 布尔值，true表示存在，false表示不存在

#### 4.1.2 查询字典码列表

- **URL**: `/dictionary/list`
- **Method**: GET
- **请求参数**:
  - `queryParam`: 查询条件，DictionaryQueryDto类型
  - `pageNum`: 页码
  - `pageSize`: 每页记录数
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息
  - `data`: 分页数据，PageInfo<DictionaryDto>类型

#### 4.1.2 查询字典码详情

- **URL**: `/dictionary/get`
- **Method**: GET
- **请求参数**:
  - `id`: 字典码ID
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息
  - `data`: 字典码详情，DictionaryDto类型

#### 4.1.3 新增字典码

- **URL**: `/dictionary/save`
- **Method**: POST
- **请求参数**:
  - 请求体: DictionaryDto对象
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息

#### 4.1.4 修改字典码

- **URL**: `/dictionary/update`
- **Method**: POST
- **请求参数**:
  - 请求体: DictionaryDto对象
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息

#### 4.1.5 删除字典码

- **URL**: `/dictionary/remove`
- **Method**: POST
- **请求参数**:
  - `ids`: 字典码ID数组
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息

### 4.2 字典详情接口

#### 4.2.1 查询字典详情列表

- **URL**: `/dictionary/detail/list`
- **Method**: POST
- **请求参数**:
  - `queryParam`: 查询条件，DictionaryDetailQueryDto类型
  - `pageNum`: 页码
  - `pageSize`: 每页记录数
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息
  - `data`: 分页数据，PageInfo<DictionaryDetailDto>类型

#### 4.2.2 查询字典详情

- **URL**: `/dictionary/detail/get`
- **Method**: GET
- **请求参数**:
  - `id`: 字典详情ID
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息
  - `data`: 字典详情，DictionaryDetailDto类型

#### 4.2.3 新增字典详情

- **URL**: `/dictionary/detail/save`
- **Method**: POST
- **请求参数**:
  - 请求体: DictionaryDetailDto对象
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息

#### 4.2.4 修改字典详情

- **URL**: `/dictionary/detail/update`
- **Method**: POST
- **请求参数**:
  - 请求体: DictionaryDetailDto对象
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息

#### 4.2.5 删除字典详情

- **URL**: `/dictionary/detail/remove`
- **Method**: POST
- **请求参数**:
  - 请求体: 字典详情ID数组 (Long[])
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息

#### 4.2.6 根据字典码获取字典详情列表

- **URL**: `/dictionary/detail/findDictionaryDeatailList`
- **Method**: GET
- **请求参数**:
  - `code`: 字典码
- **响应结果**:
  - `code`: 状态码
  - `msg`: 消息
  - `data`: 字典详情列表，List<DictionaryDetailDto>类型

## 5. 业务流程

### 5.1 字典码管理流程

1. **验证字典码是否存在**:
   - 前端发送请求，携带字典码
   - 后端接收请求，调用服务层方法验证字典码是否存在
   - 返回验证结果给前端

2. **查询字典码列表**:
   - 前端发送请求，携带查询条件和分页参数
   - 后端接收请求，调用服务层方法查询数据
   - 返回分页结果给前端

2. **新增字典码**:
   - 前端发送请求，携带字典码信息
   - 后端接收请求，调用服务层方法保存数据
   - 返回操作结果给前端

3. **修改字典码**:
   - 前端发送请求，携带修改后的字典码信息
   - 后端接收请求，调用服务层方法更新数据
   - 返回操作结果给前端

4. **删除字典码**:
   - 前端发送请求，携带要删除的字典码ID
   - 后端接收请求，调用服务层方法删除数据
   - 返回操作结果给前端

### 5.2 字典详情管理流程

1. **根据字典码获取字典详情列表**:
   - 前端发送请求，携带字典码
   - 后端接收请求，调用服务层方法查询对应的字典详情列表
   - 返回字典详情列表给前端

2. **查询字典详情列表**:
   - 前端发送请求，携带查询条件和分页参数
   - 后端接收请求，调用服务层方法查询数据
   - 返回分页结果给前端

2. **新增字典详情**:
   - 前端发送请求，携带字典详情信息
   - 后端接收请求，调用服务层方法保存数据
   - 返回操作结果给前端

3. **修改字典详情**:
   - 前端发送请求，携带修改后的字典详情信息
   - 后端接收请求，调用服务层方法更新数据
   - 返回操作结果给前端

4. **删除字典详情**:
   - 前端发送请求，携带要删除的字典详情ID
   - 后端接收请求，调用服务层方法删除数据
   - 返回操作结果给前端

## 6. 安全性考虑

1. **输入验证**:
   - 对所有输入参数进行验证，防止SQL注入和XSS攻击
   - 使用参数绑定方式执行SQL语句

2. **权限控制**:
   - 确保只有授权用户能够访问字典管理功能
   - 对敏感操作（如删除）进行权限检查

3. **数据安全**:
   - 使用软删除机制，避免直接物理删除数据
   - 记录操作日志，便于审计和追踪

## 7. 性能优化

1. **查询优化**:
   - 使用索引提高查询效率
   - 分页查询减少数据传输量

2. **缓存策略**:
   - 考虑对字典数据进行缓存，减少数据库访问
   - 在数据变更时及时更新缓存

## 8. 扩展性设计

1. **接口设计**:
   - 使用统一的响应格式，便于前端处理
   - 提供灵活的查询条件，满足不同场景需求

2. **代码结构**:
   - 采用分层架构，便于功能扩展
   - 使用依赖注入，降低模块间耦合

## 9. 变更记录

| 版本号 | 变更日期 | 变更人 | 变更内容 |
|-------|---------|-------|---------|
| 1.0.0 | 2023-07-10 | AI助手 | 初始版本 |
| 1.1.0 | 2023-07-15 | AI助手 | 1. 新增验证字典码是否存在接口<br>2. 新增根据字典码获取字典详情列表接口<br>3. 更新接口路径和请求方式 |

## 10. 待优化项

1. 增加字典码与字典详情的关联查询功能
2. 增加字典数据的导入导出功能
3. 增加字典数据的缓存机制
4. 完善字典数据的权限控制
