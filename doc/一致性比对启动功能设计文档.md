# 一致性比对启动功能设计文档

## 1. 功能概述

一致性比对启动功能用于支持多种场景下的比对/同步任务启动，包括方案级启动、系统级启动、节点级启动、规则级启动和任务级启动。该功能将根据用户选择的不同级别的对象，查询相关配置数据，构建运行实例，并通过MQ发送消息给引擎进行处理。

## 2. 启动场景

### 2.1 方案级启动
- 用户勾选多个方案
- 可选择源中心和目标中心（非必选）
- 系统基于选中的方案启动对比/同步任务

### 2.2 系统级启动
- 基于一个特定方案
- 用户勾选该方案下的多个业务系统ID
- 可选择源中心和目标中心（非必选）
- 系统基于选中的方案与业务系统ID启动对比/同步任务

### 2.3 节点级启动
- 基于一个特定方案下的特定系统
- 用户勾选该系统下的多个节点设备
- 系统基于选中的节点设备启动对比/同步任务

### 2.4 规则级启动
- 基于一个特定方案下的特定系统的特定设备
- 用户勾选该设备下的多个规则
- 系统基于选中的规则启动对比/同步任务

### 2.5 任务级启动
- 用户勾选多个任务
- 系统根据任务上的源中心ID和目标中心ID（如果为空则不受此条件限制）
- 查询对应的方案及满足条件的节点
- 基于这些信息启动对比/同步任务

## 3. 数据流程

### 3.1 数据查询
根据不同的启动场景，从以下表中查询相关数据：
- 任务表（ieai_envc_task）
- 方案表（ieai_envc_plan）
- 方案与业务系统关系表（ieai_envc_plan_relation）
- 业务系统与设备节点关系表（ieai_envc_system_computer_node）
- 节点规则数据表（ieai_envc_node_relation）
- 规则对应的内容表（ieai_envc_node_rule_content）
- 运行规则对应内容表（ieai_envc_run_rule_content）

### 3.2 数据存储
将查询结果存储到以下运行实例相关数据表中：
- 运行实例主表（ieai_envc_run_instance）
- 实例详情表（ieai_envc_run_instance_info）
- 节点规则运行表（ieai_envc_run_rule）
- 节点规则同步表（ieai_envc_run_rule_sync）
- 节点规则流程表（ieai_envc_run_flow）

### 3.3 ID生成策略
- 使用SnowflakeIdWorker.generateId()方法生成ID
- 在封装对象时就创建好ID，确保ID的下传性
- 保持各对象间的ID关系

### 3.4 消息发送
- 将存储的运行实体对象转换为MQ所需的数据格式（StartTaskFlowDto）
- 通过MQ生产者将数据推送给引擎
- 推送后，更新节点规则流程表中的状态为"运行中"（istate=0）

### 3.5 回调处理
- 处理MQ消息发送成功或失败的情况
- 更新相关表的状态

## 4. 系统架构

### 4.1 DTO类设计
- StartContrastBaseDto：基础DTO，包含所有启动场景共有的字段
- PlanLevelStartDto：方案级启动
- SystemLevelStartDto：系统级启动
- NodeLevelStartDto：节点级启动
- RuleLevelStartDto：规则级启动
- TaskLevelStartDto：任务级启动
- StartTaskFlowDto：MQ消息格式
- StartFlowDto：MQ消息中的任务项

### 4.2 Service接口设计
```java
public interface IStartContrastService {
    // 方案级启动
    StartResult startByPlans(PlanLevelStartDto startDto);
    
    // 系统级启动
    StartResult startBySystems(SystemLevelStartDto startDto);
    
    // 节点级启动
    StartResult startByNodes(NodeLevelStartDto startDto);
    
    // 规则级启动
    StartResult startByRules(RuleLevelStartDto startDto);
    
    // 任务级启动
    StartResult startByTasks(TaskLevelStartDto startDto);
    
    // 内部方法，处理MQ消息发送结果
    void handleMqSendResult(String messageId, boolean success, String errorMessage);
}
```

### 4.3 Mapper接口设计
```java
public interface StartContrastMapper {
    // 查询方案相关信息
    List<PlanInfo> selectPlanInfoByIds(List<Long> planIds);
    
    // 查询方案与系统关系
    List<PlanSystemRelation> selectPlanSystemRelations(Long planId, List<Long> systemIds);
    
    // 查询系统与节点关系
    List<SystemNodeRelation> selectSystemNodeRelations(Long systemId, List<Long> nodeIds);
    
    // 查询节点规则
    List<NodeRule> selectNodeRules(Long nodeId, List<Long> ruleIds);
    
    // 查询任务信息
    List<TaskInfo> selectTaskInfoByIds(List<Long> taskIds);
    
    // 批量插入运行实例
    int batchInsertRunInstance(List<RunInstanceEntity> instances);
    
    // 批量插入实例详情
    int batchInsertRunInstanceInfo(List<RunInstanceInfoEntity> instanceInfos);
    
    // 批量插入节点规则运行
    int batchInsertRunRule(List<RunRuleEntity> rules);
    
    // 批量插入节点规则同步
    int batchInsertRunRuleSync(List<RunRuleSyncEntity> ruleSyncs);
    
    // 批量插入节点规则流程
    int batchInsertRunFlow(List<RunFlowEntity> flows);
    
    // 批量更新节点规则流程状态
    int batchUpdateRunFlowState(List<Long> flowIds, Integer state);
}
```

## 5. 实现流程

### 5.1 接收请求
根据不同的启动场景，接收相应的DTO对象

### 5.2 查询配置
根据DTO对象中的参数，查询相关配置数据

### 5.3 构建实例
- 根据配置数据，构建运行实例相关对象
- 使用SnowflakeIdWorker.generateId()生成ID
- 确保ID的下传性，保持各对象间的ID关系

### 5.4 存储实例
将构建的对象批量存储到数据库

### 5.5 构建消息
根据存储的实例，构建MQ消息

### 5.6 发送消息
将消息发送到MQ

### 5.7 更新状态
根据MQ发送结果，更新相关表的状态

### 5.8 返回结果
返回启动结果

## 6. 异常处理

### 6.1 参数验证
验证输入参数的有效性

### 6.2 事务管理
使用Spring的事务管理确保数据一致性

### 6.3 异常捕获
捕获并处理可能出现的异常

### 6.4 日志记录
记录关键操作和异常信息

## 7. 并发处理

虽然当前不作为重点考虑，但设计中已考虑以下并发处理策略：

### 7.1 ID生成
使用SnowflakeIdWorker.generateId()生成全局唯一ID，避免ID冲突

### 7.2 事务隔离
使用适当的事务隔离级别，确保数据一致性

### 7.3 乐观锁
在必要时使用乐观锁机制，避免并发更新冲突

## 8. 表关系图

```
ieai_envc_run_instance (运行实例主表)
  |
  +-- ieai_envc_run_instance_info (实例详情表)
        |
        +-- ieai_envc_run_rule (节点规则运行表)
              |
              +-- ieai_envc_run_rule_sync (节点规则同步表，当imodel=1或2时)
              |
              +-- ieai_envc_run_flow (节点规则流程表)
```

## 9. ID关系示例

```
RunInstance.id = 100000
  |
  +-- RunInstanceInfo.id = 100001
  |   RunInstanceInfo.envcRunInstanceId = 100000
        |
        +-- RunRule.id = 100002
        |   RunRule.envcRunInstanceInfoId = 100001
              |
              +-- RunRuleSync.id = 100003 (当RunRule.model=1或2时)
              |   RunRuleSync.envcRunRuleId = 100002
              |
              +-- RunFlow.id = 100004
                  RunFlow.envcRunRuleId = 100002
```

## 10. 注意事项

- 确保ID的下传性，保持各对象间的ID关系
- 在封装对象时就创建好ID，减少查询消耗
- 批量操作提高性能
- 正确处理MQ消息发送结果
- 确保数据一致性，特别是在多表操作时
