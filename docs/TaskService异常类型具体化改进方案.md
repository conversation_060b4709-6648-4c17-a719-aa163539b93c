# TaskService异常类型具体化改进方案

## 问题分析

### 原始问题
在`TaskServiceImpl`中存在异常类型不具体的问题：

```java
// 原始代码 - 使用通用RuntimeException
public void checkTaskRule(TaskDto taskDto) {
    if (taskDto.getEnvcPlanId() == null || taskDto.getEnvcPlanId() <= 0) {
        throw new RuntimeException("任务方案ID不能为空");
    }
    if (taskDto.getCron() == null || taskDto.getCron().trim().isEmpty()) {
        throw new RuntimeException("任务cron表达式不能为空");
    }
    // ...
}
```

### 问题影响
1. **异常处理不精确**：Controller层需要通过字符串匹配来区分不同的业务异常
2. **用户体验差**：无法提供针对性的错误提示和错误码
3. **代码维护困难**：异常处理逻辑分散，不便于统一管理
4. **不符合最佳实践**：业务异常应该使用具体的业务异常类型

## 优化方案

### 1. 异常类型统一化

#### 替换RuntimeException为ContrastBusinessException
```java
// 优化后的代码
public void checkTaskRule(TaskDto taskDto) throws ContrastBusinessException {
    if (taskDto == null) {
        throw new ContrastBusinessException("任务参数不能为空");
    }
    if (taskDto.getEnvcPlanId() == null || taskDto.getEnvcPlanId() <= 0) {
        throw new ContrastBusinessException("任务方案ID不能为空");
    }
    if (taskDto.getCron() == null || taskDto.getCron().trim().isEmpty()) {
        throw new ContrastBusinessException("任务cron表达式不能为空");
    }
    
    try {
        int count = taskMapper.checkTaskExists(taskDto.getEnvcPlanId(), taskDto.getCron().trim());
        if (count > 0) {
            throw new ContrastBusinessException("同一方案下已存在相同cron表达式的任务，请重新输入");
        }
    } catch (Exception e) {
        if (e instanceof ContrastBusinessException) {
            throw e;
        }
        logger.error("检查任务是否存在时发生异常", e);
        throw new ContrastBusinessException("检查任务规则时发生系统异常：" + e.getMessage(), e);
    }
}
```

### 2. 方法签名更新

#### 接口层面的异常声明
```java
// ITaskService接口
int insertTask(TaskDto taskDto) throws ContrastBusinessException;
int updateTask(TaskDto taskDto) throws ContrastBusinessException;
int modifyTask(TaskDto taskDto, UserDto userDto) throws ContrastBusinessException;
```

#### 实现层面的异常处理
```java
// TaskServiceImpl实现
@Override
public int insertTask(TaskDto taskDto) throws ContrastBusinessException {
    try {
        // 业务逻辑
        checkTaskRule(taskDto);
        // ...
    } catch (ContrastBusinessException e) {
        throw e;
    } catch (Exception e) {
        logger.error("新增任务时发生异常", e);
        throw new ContrastBusinessException("新增任务时发生系统异常：" + e.getMessage(), e);
    }
}
```

### 3. Controller层优化

#### 精确的异常处理
```java
// 优化后的Controller异常处理
@PostMapping("/save")
public R<Void> save(@RequestBody TaskDto taskDto) {
    try {
        int rows = taskService.insertTask(taskDto);
        if (rows > 0) {
            return R.ok(ResponseCodeEnum.SUCCESS.getCode(), ResponseCodeEnum.SUCCESS.getDesc());
        }
        return R.fail("13001", "新增任务失败");
    } catch (ContrastBusinessException e) {
        logger.error("新增任务失败", e);
        if (e.getMessage() != null && e.getMessage().contains("同一方案下已存在相同cron表达式的任务")) {
            return R.fail("13001", e.getMessage());
        }
        return R.fail("13002", e.getMessage());
    } catch (Exception e) {
        logger.error("新增任务时发生系统异常", e);
        return R.fail("00000", "系统异常：" + e.getMessage());
    }
}
```

## 改进效果

### 1. 异常处理精确化

#### 改进前
- 所有业务异常都是RuntimeException
- Controller需要通过字符串匹配区分异常类型
- 无法提供精确的错误码

#### 改进后
- 业务异常统一使用ContrastBusinessException
- 系统异常和业务异常明确区分
- 可以提供精确的错误码和用户友好的错误信息

### 2. 代码可维护性提升

#### 异常处理统一化
- 所有参数验证异常都使用ContrastBusinessException
- 异常信息更加规范和一致
- 便于统一的异常处理和日志记录

#### 异常链保持
```java
// 保持异常链，便于问题排查
catch (Exception e) {
    if (e instanceof ContrastBusinessException) {
        throw e;
    }
    throw new ContrastBusinessException("系统异常：" + e.getMessage(), e);
}
```

### 3. 用户体验改善

#### 错误码体系
- 13001：业务规则违反（如重复任务）
- 13002：参数验证失败
- 00000：系统异常

#### 错误信息友好化
- 参数验证：明确指出哪个参数有问题
- 业务规则：提供具体的业务约束说明
- 系统异常：隐藏技术细节，提供用户友好的提示

## 测试覆盖

### 1. 参数验证测试
- ✅ 测试null参数的异常处理
- ✅ 测试无效参数值的异常处理
- ✅ 测试空字符串参数的异常处理

### 2. 业务规则测试
- ✅ 测试任务重复的异常处理
- ✅ 测试数据库异常的包装处理

### 3. 异常类型验证
- ✅ 验证抛出的异常类型为ContrastBusinessException
- ✅ 验证异常信息的准确性
- ✅ 验证异常链的完整性

## 最佳实践总结

### 1. 异常分层原则
- **业务异常**：使用ContrastBusinessException，包含用户友好的错误信息
- **系统异常**：包装为ContrastBusinessException，隐藏技术细节
- **参数异常**：统一使用ContrastBusinessException，明确指出参数问题

### 2. 异常处理模式
```java
try {
    // 业务逻辑
} catch (ContrastBusinessException e) {
    throw e; // 直接重新抛出业务异常
} catch (Exception e) {
    logger.error("操作失败", e);
    throw new ContrastBusinessException("操作失败：" + e.getMessage(), e);
}
```

### 3. 异常信息规范
- 参数验证："{参数名}不能为空"
- 业务规则：具体的业务约束描述
- 系统异常："{操作名}时发生系统异常：{原因}"

## 总结

通过异常类型具体化改进，我们实现了：

1. **统一的异常处理体系**：所有业务异常都使用ContrastBusinessException
2. **精确的错误码和错误信息**：便于前端进行针对性处理
3. **更好的用户体验**：提供友好的错误提示
4. **更高的代码质量**：符合异常处理最佳实践

这个改进不仅解决了当前的问题，还为后续的异常处理提供了良好的模式和规范。
