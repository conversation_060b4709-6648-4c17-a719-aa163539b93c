<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <id>package</id>
    <formats>
        <format>tar</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.basedir}/../build</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>start.sh</include>
                <include>stop.sh</include>
            </includes>
            <fileMode>0755</fileMode>
            <!--如果是脚本，一定要改为unix.如果是在windows上面编码，会出现dos编写问题-->
            <lineEnding>unix</lineEnding>
            <filtered>true</filtered><!-- 是否进行属性替换 -->
        </fileSet>
        <fileSet>
           <!-- <directory>${project.build.outputDirectory}</directory>-->
            <directory>${project.basedir}/../build</directory>
            <outputDirectory>conf</outputDirectory>
            <includes>
                <include>bootstrap.yml</include>
                <include>application.yml</include>
                <include>contrast-logback-spring.xml</include>
            </includes>
            <fileMode>0644</fileMode>
        </fileSet>
        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>${project.build.finalName}.jar</include>
            </includes>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>
</assembly>
