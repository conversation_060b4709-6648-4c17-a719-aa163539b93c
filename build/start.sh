#!/bin/bash

#Ӧ��������·��
SYSTEM_DIR="$(dirname "$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )")"
cd $SYSTEM_DIR
echo "$SYSTEM_DIR"
#bin������·��
BIN_DIR="$SYSTEM_DIR/bin"
#config�ļ�������·��
CONF_DIR="$SYSTEM_DIR/conf"
#JRE����·��
JRE_DIR="$SYSTEM_DIR/jre"
#Ӧ��JAR������·����������Ҫ��̬
JAR_FILE="$BIN_DIR/contrast-comparison-starter-1.2.0-SNAPSHOT.jar"
#java����
JAVA_COMMAND="java"

# ��� jre �ļ����Ƿ���ڣ����Ҳ�Ϊ��
if [ -d "$JRE_DIR" ] && [ -f "$JRE_DIR/bin/java" ]; then
echo "Using JRE from $JRE_DIR"
JAVA_COMMAND="$JRE_DIR/bin/java"
else
echo "Using system default JRE"
# ������JAVA_HOME��ʹ��ϵͳPATH�е�java
fi
#��������
#����jvm���������ã���̬����
nohup "$JAVA_COMMAND" -jar -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8212 -Xms1024m -Xmx1024m -Xmn500m  -XX:+UseG1GC  -XX:MaxGCPauseMillis=50  -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:-OmitStackTraceInFastThrow "$JAR_FILE" --spring.cloud.bootstrap.location="$CONF_DIR"/bootstrap.yml >/dev/null 2>&1 & echo $! > $BIN_DIR/pid_file