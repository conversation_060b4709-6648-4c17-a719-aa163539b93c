spring:
    profiles:
        active: ${appActive}
    application:
        name: ${appName}
<#assign configActive = configActive!"">
<#assign discoverActive = discoverActive!"">
<#if configActive='nacos' && discoverActive='nacos' >
    cloud:
        nacos:
            config:
                serverAddr: 192.168.1.171:8848
                file-extension: yml
                group: DEFAULT_GROUP
            discovery:
                server-addr: 192.168.1.171:8848
                group: DEFAULT_GROUP
</#if>
<#if configActive='nacos' && discoverActive!='nacos' >
    cloud:
        nacos:
            config:
                serverAddr: 192.168.1.171:8848
                file-extension: yml
                group: DEFAULT_GROUP
</#if>
<#if configActive!='nacos' && discoverActive='nacos' >
    cloud:
        nacos:
        discovery:
            server-addr: 192.168.1.171:8848
</#if>
<#if discoverActive='zookeeper'>
    cloud:
        zookeeper:
            connect-string: 192.168.1.171:2181
</#if>
<#if configActive='NONE'>
    config:
        import:
            - optional:file:./application.yml
</#if>
<#if configActive='apollo'>
app:
    id: studio
apollo:
    meta: http://192.168.1.171:9001
    bootstrap:
        enabled: true
        namespaces: ${appName}-${appActive}.yml
        eagerLoad:
            enabled: true
</#if>



logging:
    config: http://${r'${spring.cloud.nacos.discovery.server-addr}'}/nacos/v1/cs/configs?group=${r'${spring.cloud.nacos.discovery.group}'}&dataId=contrast-logback-spring.xml

