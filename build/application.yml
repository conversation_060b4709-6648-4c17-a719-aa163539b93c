server:
    port: 8210

contrast:
    mq:
        engine:
            send-task:
                topic: contrastStartFlow
                group: contrastStartFlowGroup
                enabled: true

spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            driver-class-name: com.mysql.cj.jdbc.Driver
            url: *************************************************************************************************************************************************************************************
            username: root
            password: ENC(4c4d6cdcc26fd9371a9bcf9f56d15019)
            # ��ʼ��ʱ�����������ӵĸ���
            initial-size: 5
            # ���ӳص���С��������
            min-idle: 5
            # ���ӳ������������
            maxActive: 50
            # ��ȡ����ʱ���ȴ�ʱ�䣬��λ����
            maxWait: 60000
            # ����Ϊ���ļ��ʱ������ΪtestWhileIdelִ�е�����
            timeBetweenEvictionRunsMillis: 60000
            # �����߳�ʱ��⵱ǰ���ӵ����ʱ��͵�ǰʱ�����ڸ�ֵʱ���رյ�ǰ����(���������ڳ��е���С����ʱ��)
            minEvictableIdleTimeMillis: 300000
            # ����������ݿ������Ƿ���Ч��sql ������һ����ѯ���
            validationQuery: SELECT 'x'
            # �������ӵ�ʱ���⣬�������ʱ�����timeBetweenEvictionRunsMillis��ִ��validationQuery��������Ƿ���Ч��
            testWhileIdle: true
            # ��������ʱ��ִ��validationQuery��������Ƿ���Ч,�����ή������,Ĭ��Ϊtrue
            testOnBorrow: false
            # �黹����ʱ��ִ��validationQuery��������Ƿ���Ч,�����ή������,Ĭ��Ϊtrue
            testOnReturn: false
            # �Ƿ񻺴�preparedStatement, Ҳ����PSCache,PSCache��֧���α�����ݿ����������޴󣬱���˵oracle,��mysql�½���رա�
            poolPreparedStatements: true
            # Ҫ����PSCache���������ô���0��������0ʱ��poolPreparedStatements�Զ������޸�Ϊtrue����Druid�У��������Oracle��PSCacheռ���ڴ��������⣬���԰������ֵ���ô�һЩ������˵100
            maxPoolPreparedStatementPerConnectionSize: 20
    liquibase:
        contexts: all
        enabled: true
        drop-first: false
    jackson:
        time-zone: GMT+8
    redis:
        cluster:
            #redis��Ⱥ��ַ
            nodes: ************:27001,************:27001,************:27001
            max-redirects: 5
        database: 0
        password: ideal123
        timeout: 180s

    servlet:
        multipart:
        max-file-size: 10MB
        max-request-size: 100MB
    cloud:
        config:
            override-none: true
        stream:
            rocketmq:
                binder:
                    name-server: *************:9876
            function:
                definition: contrastReceiveTaskSendResult
            bindings:
                contrastStartFlow-out-0:
                    destination: ${contrast.mq.engine.send-task.topic}
                    content-type: application/json
                    group: ${contrast.mq.engine.send-task.group}
                    binder: rocketmq
                    producer:
                        # ͬ������ȷ����Ϣ����ʧ
                        sync: true
                contrastReceiveTaskSendResult-out-0:
                    destination: contrastReceiveTaskSendResult
                    content-type: application/json
                    group: contrastReceiveTaskSendResultGroup
                    binder: rocketmq
mybatis:
    mapper-locations:
        - classpath:mapper/*Mapper.xml
pagehelper:
    reasonable: true
    supportMethodsArguments: true
    params: count=countSql

management:
    endpoints:
        web:
            exposure:
                include: "*"
    metrics:
        export:
            prometheus:
                enabled: true
ideal:
    communication:
        secret:
            salt: "ideal"
            user-info-key: "user"
            secret-token-key: "secretToken"
    common:
        file-path-validate:
            enable: true
        local-validator-message:
            enable: false
            model: ALL_WITH_CLASS_PATH
common:
    security:
        authentication: true
        button:
            # Ĭ��Ϊtrue��������ťȨ��
            authentication: true

xxl:
    job:
        admin:
            # �������ķ�����ĵ�ַ
            addresses: http://*************:8899/xxl-job-admin
        executor:
            # ִ����AppName
            appname: contrast-executor
            #ִ�����˿ںţ�С�ڵ���0���Զ���ȡ��Ĭ�϶˿�Ϊ9999������������ִ����ʱ��ע��Ҫ���ò�ִͬ�����˿ڣ�
            port: 8211
            # ִ����������־�ļ��洢����·�� [ѡ��] ����Ҫ�Ը�·��ӵ�ж�дȨ�ޣ�Ϊ����ʹ��Ĭ��·����
            logpath: ${user.dir}/logs
            # ִ������־�ļ��������� [ѡ��] �� ������־�Զ�����, ����ֵ���ڵ���3ʱ��Ч; ����, ��-1, �ر��Զ������ܣ�
            logretentiondays: 30
        # ִ����ͨѶTOKEN,Ҫ�͵������ķ��������õ�accessTokenһ�£�Ҫ��Ȼ�޷�����ע��
        accessToken: default_token


messagine-rocketmq:
    namesrvAddr: *************:9876
    consumerGroup: scheduleJobReceiveMonitorResultGroup
    topic: scheduleJobReceiveMonitorResult


