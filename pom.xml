<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ieai-envcontrast-comparison</artifactId>
    <version>${revision}</version>

    <packaging>pom</packaging>
    <modules>
        <module>contrast-comparison-api</module>
        <module>contrast-comparison-biz</module>
        <module>contrast-comparison-starter</module>
        <module>contrast-comparison-stream-subscriber</module>

    </modules>

    <properties>
        <java.version>1.8</java.version>
        <project.start.class>com.ideal.envc.Bootstrap</project.start.class>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <ideal-message-spring-cloud-stream-publisher.version>1.0-SNAPSHOT</ideal-message-spring-cloud-stream-publisher.version>
        <revision>1.2.0-SNAPSHOT</revision>
        <contrast.api.version>1.0-SNAPSHOT</contrast.api.version>
        <xxl-job-spring-boot-starter.version>1.0-SNAPSHOT</xxl-job-spring-boot-starter.version>

        <maven.flatten.version>1.7.0</maven.flatten.version>
        <sonar-maven-plugin.version>3.11.0.3922</sonar-maven-plugin.version>
        <sonar.login>****************************************</sonar.login>
        <sonar.host.url>http://*************:9000/</sonar.host.url>
        <sonar.projectKey>ieai-envcontrast-comparison</sonar.projectKey>
        <sonar.projectName>ieai-envcontrast-comparison</sonar.projectName>
        <sonar.coverage.exclusions>
            src/main/**/model/**/*,
            src/main/**/generate/**/*,
            src/main/**/api/**/*,
            src/main/**/client/**/*,
            src/main/**/enums/**/*,
            **/*.java:^import\s+.*?;$
        </sonar.coverage.exclusions>
    </properties>

    <parent>
        <groupId>com.ideal</groupId>
        <artifactId>ideal-dependencies</artifactId>
        <version>3.0.4-SNAPSHOT</version>
        <relativePath />
    </parent>

    <dependencyManagement>
        <dependencies>

            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>contrast-comparison-api</artifactId>
                <version>${contrast.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>contrast-comparison-biz</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>contrast-comparison-stream-subscriber</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-common</artifactId>
                <version>0.4.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-snowflake-spring-boot-starter</artifactId>
                <version>1.0.2-SNAPSHOT</version>
            </dependency>

            <!-- 消息队列的封装工程 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>ideal-message-spring-cloud-stream-publisher</artifactId>
                <version>${ideal-message-spring-cloud-stream-publisher.version}</version>
            </dependency>
            <!-- 依赖系统模块 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>system-api</artifactId>
                <version>1.6.2-SNAPSHOT</version>
            </dependency>
            <!-- 依赖管服模块 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>agent-management-api</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <!-- 依赖鉴权模块 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>system-common-component</artifactId>
                <version>1.4-SNAPSHOT</version>
            </dependency>
            <!-- 依赖通知平台模块 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>notification-api</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>

            <!-- 依赖网关鉴权模块 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>gateway-filter</artifactId>
                <version>1.0-SNAPSHOT</version>
            </dependency>
            <!-- 依赖引擎监控模块 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>engine-monitor</artifactId>
                <version>1.11-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>engine-api</artifactId>
                <version>1.7-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>flow-common</artifactId>
                <version>2.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>xxl-job-spring-boot-starter</artifactId>
                <version>${xxl-job-spring-boot-starter.version}</version>
            </dependency>
            <!-- 增加后端的按钮权限控制 -->
            <dependency>
                <groupId>com.ideal</groupId>
                <artifactId>system-button-permission</artifactId>
                <version>1.1-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>cn.idev.excel</groupId>
                <artifactId>fastexcel</artifactId>
                <version>1.2.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>${sonar-maven-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.2.7</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
        </pluginManagement>
    </build>
</project>